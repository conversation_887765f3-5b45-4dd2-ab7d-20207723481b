{"permissions": {"allow": ["Bash(git init:*)", "Bash(git add:*)", "Bash(git commit:*)", "Bash(cp:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(mv:*)", "Bash(rg:*)", "Bash(grep:*)", "<PERSON><PERSON>(python3:*)", "<PERSON><PERSON>(python test:*)", "Bash(pip install:*)", "<PERSON><PERSON>(python:*)", "Bash(timeout 30 python3 app.py)", "Bash(timeout 15 python3 app.py)", "<PERSON><PERSON>(sed:*)", "<PERSON><PERSON>(timeout:*)", "<PERSON><PERSON>(touch:*)", "<PERSON><PERSON>(pkill:*)", "Bash(lsof:*)", "<PERSON><PERSON>(curl:*)", "Bash(ls:*)", "Bash(kill:*)", "Bash(ss:*)", "Bash(find:*)", "Bash(rm:*)", "Bash(git restore:*)", "Bash(git checkout:*)", "Bash(git remote add:*)", "Bash(git push:*)", "<PERSON><PERSON>(pip show:*)", "Bash(PORT=5001 python3 app.py)", "Bash(PORT=5002 python3 app.py)", "Bash(PORT=5003 python3 app.py)", "<PERSON><PERSON>(cat:*)", "Bash(FLASK_PORT=5001 python3 app.py)", "<PERSON><PERSON>(true)", "Bash(git commit:*)"], "deny": []}}