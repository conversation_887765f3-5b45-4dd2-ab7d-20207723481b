# iframe配置示例

# 选项1: 允许同源iframe (只允许相同域名的网站嵌入)
FRAME_OPTIONS=SAMEORIGIN

# 选项2: 允许所有iframe (完全开放，任何网站都可以嵌入)
# FRAME_OPTIONS=ALLOWALL

# 选项3: 禁止所有iframe (最安全，但不允许任何嵌入)
# FRAME_OPTIONS=DENY

# 高级选项: 使用CSP控制允许的域名
# CSP_ENABLED=true
# CSP_FRAME_ANCESTORS='self' https://example.com https://trusted-site.com

# 如果你知道具体哪个网站要嵌入，建议使用CSP方式：
# CSP_ENABLED=true
# CSP_FRAME_ANCESTORS='self' https://your-parent-website.com