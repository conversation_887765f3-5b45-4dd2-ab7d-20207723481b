# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
nnscholars/
nnscholarweb/
venv/
env/
ENV/
env.bak/
venv.bak/

# IDE
.idea/
.vscode/
*.swp
*.swo

# Environment variables
.env

# Logs
*.log
logs/

# Database
*.db
*.sqlite3

# Other
.DS_Store

# Local environment
.env.local
.env.*.local

# Output directories
output/
exports/*
!exports/.gitkeep

# System Files
Thumbs.db
.cursor/
temp/
tests/
tatus
simple_test_results.json
综述版本/
# 数据文件
data/journal_metrics/*.csv
static/images/*
!static/images/.gitkeep

# API密钥和敏感信息
api_keys.py
secrets.py
credentials.json
*.key
*.pem
config.local.py
settings.local.py

# 文献缓存和临时数据
literature_cache/
pubmed_cache/
citation_cache/
embedding_cache/
*.cache

# 用户上传和下载文件
uploads/
downloads/
user_data/

# 会话文件
sessions/
*.session
session_data/

# 模型和检查点
models/
checkpoints/
*.model
*.pkl
*.joblib

# 测试和覆盖率报告
htmlcov/
.coverage
.pytest_cache/
.tox/
coverage.xml
*.cover

# 临时和备份文件
*.tmp
*.temp
*.bak
*.backup
*~
.#*

# 文档构建
docs/_build/
site/

# 原始版本备份
原来版本/
backup/
old_versions/

# Node.js (如果有前端构建工具)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json

# 操作系统文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
desktop.ini

# 编辑器临时文件
*.swp
*.swo
*~
.#*
\#*#

# 项目特定的忽略文件
# 添加任何项目特定的文件或目录