# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

NNScholar（智能学术文献检索与分析平台）是一个基于AI驱动的Flask Web应用，专为科研工作者设计。该项目集成了DeepSeek AI技术，提供智能化的文献检索、深度分析和学术写作支持。

## Architecture

### Modular Structure
The application follows a **modular Flask architecture** with clear separation of concerns:

```
├── app.py                      # Main application factory with SocketIO
├── config/                     # Configuration management
│   ├── settings.py            # Application settings
│   └── api_config.py          # API configurations
├── routes/                     # Blueprint-based routing
│   ├── web_routes.py          # HTML page routes
│   ├── api_routes.py          # REST API endpoints  
│   ├── export_routes.py       # Export functionality
│   └── websocket_routes.py    # Real-time communication
├── services/                   # Business logic layer (18+ services)
│   ├── pubmed_service.py      # PubMed API integration
│   ├── deepseek_service.py    # DeepSeek AI integration
│   └── embedding_service.py   # Embedding model service
├── models/                     # Data models
│   ├── paper.py               # Paper data structure
│   ├── journal.py             # Journal information
│   └── session.py             # Session management
├── templates/                  # Jinja2 templates (dual interface)
└── utils/                      # Utility functions
```

### Core Components
- **Main Application** (`app.py`) - Application factory with SocketIO setup for real-time communication
- **Service Layer** (`services/`) - 18+ specialized services handling external APIs, AI processing, and data management
- **Routing Layer** (`routes/`) - Blueprint-based route organization separating web pages, APIs, and WebSocket handlers
- **Data Models** (`models/`) - Structured data handling for papers, journals, and sessions

### Key Features
- **双界面设计**: 专业检索界面(`/`)和智能聊天界面(`/chat`)
- **AI智能推荐系统**: 基于影响因子和创新性的文献推荐
- **实时WebSocket通信**: 用于实时搜索进度和结果更新
- **多格式数据导出**: 支持Excel和Word格式的文献报告导出
- **期刊热点分析**: 可视化分析期刊研究趋势和热点主题

## Development Commands

### Environment Setup
```bash
# 创建虚拟环境 (if using virtualenv)
python -m venv nnscholarweb
source nnscholarweb/bin/activate  # Linux/Mac
# 或
nnscholarweb\Scripts\activate  # Windows

# 安装依赖
pip install -r requirements.txt
```

### Running the Application
```bash
# 开发模式启动
python app.py

# 生产模式启动 (使用gunicorn)
gunicorn --worker-class eventlet -w 1 app:app
```

### Environment Configuration
项目需要以下环境变量配置：
```bash
DEEPSEEK_API_KEY=your_deepseek_api_key
EMBEDDING_API_KEY=your_embedding_api_key  
PUBMED_EMAIL=<EMAIL>
SECRET_KEY=your_secret_key
```

### External API Integrations
- **PubMed API** - Medical literature search
- **DeepSeek AI** - Intelligent analysis and chat
- **Embedding API** - Semantic similarity calculation
- **arXiv API** - Preprint access
- **Semantic Scholar** - Citation networks
- **CrossRef** - DOI resolution

## Code Structure

### Main Application Flow
1. **用户界面**: 两个主要界面提供不同的交互方式
2. **文献检索**: 通过PubMed API进行学术文献搜索
3. **AI分析**: 使用DeepSeek AI进行文献内容分析和推荐
4. **数据处理**: 包含影响因子、JCR分区、中科院分区等期刊质量指标
5. **结果展示**: 多维度展示检索结果，包括相关性评分和筛选功能

### Key Modules
- **WebSocket通信**: 实时进度更新和结果推送
- **会话管理**: 用户检索历史和会话状态管理
- **数据导出**: 格式化的Excel和Word文档生成
- **可视化**: 使用matplotlib和seaborn进行数据可视化

## Dependencies

### Core Dependencies
- **Flask 3.0.2** - Web框架
- **Flask-SocketIO 5.3.6** - WebSocket支持
- **requests 2.31.0** - HTTP请求处理
- **gunicorn 21.2.0** - WSGI服务器

### AI & NLP
- **langchain-community 0.0.19** - AI应用框架
- **nltk 3.8.1** - 自然语言处理
- **scikit-learn 1.4.0** - 机器学习

### Data Processing
- **pandas 2.2.0** - 数据处理
- **numpy 1.26.4** - 数值计算
- **matplotlib 3.8.2** - 数据可视化
- **seaborn 0.13.2** - 统计可视化

### Document Processing
- **python-docx 1.1.0** - Word文档生成
- **openpyxl 3.1.2** - Excel文档处理
- **beautifulsoup4 4.12.3** - HTML解析

## Important Notes

### Data Storage
- **Session-based storage** (Flask sessions) for user state
- **JSON files** for journal metrics (`data/journal_metrics/`)
- **File-based caching** for performance optimization
- **No traditional database** - uses file-based data management

### Testing
项目目前没有配置标准测试框架。建议通过以下方式进行测试：
- 启动应用后访问 `/` 和 `/chat` 界面进行功能测试
- 使用提供的测试数据进行文献检索功能验证
- 监控 `logs/` 目录中的应用日志

### Deployment
项目配置了多种部署选项：
- **Railway**: 使用 `railway.toml` 配置
- **Heroku**: 使用 `Procfile` 配置
- **Docker**: 支持容器化部署
- **Nginx**: 提供了nginx配置文件

### Session Management
应用使用Flask Session进行会话管理，包括：
- 用户检索历史存储
- 会话状态维护
- 临时数据缓存

### Key Development Guidelines

#### From .cursorrules
- **主动完成工作** - Don't wait for multiple user prompts; be proactive
- **先理解项目** - Always read README.md and existing code before starting
- **遵循PEP 8** - Follow Python code style guidelines
- **使用类型提示** - Implement type hints for better code quality
- **详细文档** - Write comprehensive docstrings and comments
- **错误处理** - Implement proper error handling and logging
- **单元测试** - Write unit tests to ensure code quality

#### File Structure
- `static/` - 静态资源文件
- `templates/` - Flask模板文件 (dual interface design)
- `data/` - 数据文件（期刊指标等）
- `logs/` - 日志文件
- `exports/` - 导出文件临时存储
- `config/` - 配置管理
- `services/` - 业务逻辑服务层
- `models/` - 数据模型
- `routes/` - 路由蓝图
- `utils/` - 工具函数