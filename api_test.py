#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试各种API的返回数据格式
"""
import requests
import json
import time

def test_api(name, url, params=None, headers=None):
    """测试API并打印返回数据"""
    print(f"\n{'='*50}")
    print(f"测试 {name}")
    print(f"URL: {url}")
    if params:
        print(f"参数: {params}")
    print(f"{'='*50}")
    
    try:
        response = requests.get(url, params=params, headers=headers, timeout=30)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"数据类型: {type(data)}")
            
            if isinstance(data, dict):
                print(f"字典键: {list(data.keys())}")
                # 打印前几个字段的内容
                for key, value in list(data.items())[:3]:
                    if isinstance(value, (list, dict)):
                        print(f"{key}: {type(value)} (长度: {len(value) if hasattr(value, '__len__') else 'N/A'})")
                    else:
                        print(f"{key}: {value}")
            elif isinstance(data, list):
                print(f"列表长度: {len(data)}")
                if data:
                    print(f"第一个元素: {data[0]}")
            
            # 保存完整数据到文件
            filename = f"{name.replace(' ', '_').lower()}_response.json"
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            print(f"完整数据已保存到: {filename}")
            
        else:
            print(f"错误: {response.text[:200]}")
            
    except Exception as e:
        print(f"异常: {e}")

def main():
    # 测试DOI
    test_doi = "10.3390/ijms23116047"
    
    print("开始测试各种API...")
    
    # 1. 测试Crossref获取基本信息
    test_api(
        "Crossref 基本信息",
        f"https://api.crossref.org/works/{test_doi}"
    )
    
    time.sleep(2)
    
    # 2. 测试Semantic Scholar
    test_api(
        "Semantic Scholar",
        f"https://api.semanticscholar.org/graph/v1/paper/DOI:{test_doi}",
        params={"fields": "paperId,citations,citations.title,citations.externalIds"}
    )
    
    time.sleep(2)
    
    # 3. 测试OpenCitations
    test_api(
        "OpenCitations",
        f"https://opencitations.net/index/coci/api/v1/citations/{test_doi}"
    )
    
    time.sleep(2)
    
    # 4. 测试Crossref被引文献查询
    test_api(
        "Crossref 被引文献",
        "https://api.crossref.org/works",
        params={"filter": f"references-to:{test_doi}", "rows": 5, "select": "DOI,title"}
    )
    
    time.sleep(2)
    
    # 5. 测试另一种Crossref查询
    test_api(
        "Crossref Cites",
        "https://api.crossref.org/works", 
        params={"filter": f"cites:{test_doi}", "rows": 5, "select": "DOI,title"}
    )

if __name__ == "__main__":
    main()
