#!/usr/bin/env python3
"""检查服务器状态和路由"""

import requests
import json


def check_server_routes():
    """检查服务器路由状态"""
    print("🔍 检查服务器路由状态")
    print("=" * 50)
    
    base_url = "http://localhost:5000"
    
    routes_to_check = [
        "/",
        "/api/sessions", 
        "/api/search",
        "/api/export/excel",
        "/api/export/word"
    ]
    
    for route in routes_to_check:
        try:
            if route in ["/api/export/excel", "/api/export/word"]:
                # 对于导出路由，使用POST请求
                response = requests.post(f"{base_url}{route}", 
                                       json={"test": "data"}, 
                                       timeout=5)
            else:
                # 其他路由使用GET请求
                response = requests.get(f"{base_url}{route}", timeout=5)
            
            status = response.status_code
            if status == 404:
                print(f"❌ {route}: 404 (路由不存在)")
            elif status == 500:
                print(f"⚠️ {route}: 500 (服务器错误)")
            elif status in [200, 400, 405]:
                print(f"✅ {route}: {status} (路由存在)")
            else:
                print(f"? {route}: {status}")
                
        except requests.exceptions.ConnectionError:
            print(f"❌ {route}: 连接失败 (服务器未运行?)")
        except Exception as e:
            print(f"❌ {route}: {e}")


def test_real_export():
    """测试真实的导出场景"""
    print(f"\n🔍 测试真实导出场景")
    print("=" * 50)
    
    try:
        # 1. 执行搜索
        search_data = {
            "query": "machine learning",
            "database": "semantic_scholar", 
            "mode": "strategy",
            "filters": {"papers_limit": "2"}
        }
        
        print("1. 执行搜索...")
        search_response = requests.post(
            "http://localhost:5000/api/search",
            json=search_data,
            timeout=30
        )
        
        if search_response.status_code != 200:
            print(f"❌ 搜索失败: {search_response.status_code}")
            return
        
        search_result = search_response.json()
        if not search_result.get('success'):
            print(f"❌ 搜索失败: {search_result.get('error')}")
            return
        
        session_id = search_result.get('session_id')
        papers = search_result.get('data', [])
        
        print(f"✅ 搜索成功: {len(papers)} 篇文献, 会话ID: {session_id}")
        
        # 2. 立即尝试导出
        print("2. 立即尝试导出...")
        export_data = {
            "query": "machine learning",
            "data_type": "filtered"
        }
        
        headers = {
            'Content-Type': 'application/json',
            'sid': session_id
        }
        
        export_response = requests.post(
            "http://localhost:5000/api/export/excel",
            json=export_data,
            headers=headers,
            timeout=20
        )
        
        print(f"导出状态: {export_response.status_code}")
        
        if export_response.status_code == 404:
            print("❌ 404错误 - 路由不存在")
            
            # 检查是否有其他可能的端点
            alternative_endpoints = [
                "/api/export_excel",
                "/export/excel", 
                "/api/papers/export"
            ]
            
            print("尝试备用端点...")
            for endpoint in alternative_endpoints:
                try:
                    alt_response = requests.post(
                        f"http://localhost:5000{endpoint}",
                        json=export_data,
                        headers=headers,
                        timeout=10
                    )
                    print(f"  {endpoint}: {alt_response.status_code}")
                except:
                    print(f"  {endpoint}: 连接失败")
                    
        elif export_response.status_code == 200:
            print("✅ 导出成功")
            print(f"   文件大小: {len(export_response.content)} 字节")
        else:
            print(f"❌ 导出失败: {export_response.status_code}")
            print(f"   错误: {export_response.text}")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")


def main():
    print("🚀 检查导出功能404问题")
    print("=" * 80)
    
    # 检查路由状态
    check_server_routes()
    
    # 测试真实场景
    test_real_export()
    
    print(f"\n💡 如果仍然有404错误，可能的原因:")
    print(f"  1. 应用需要重启")
    print(f"  2. 路由注册顺序问题") 
    print(f"  3. 蓝图URL前缀冲突")
    print(f"  4. 前端缓存问题")


if __name__ == "__main__":
    main()
