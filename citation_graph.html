<html>
    <head>
        <meta charset="utf-8">
        
            <script>function neighbourhoodHighlight(params) {
  // console.log("in nieghbourhoodhighlight");
  allNodes = nodes.get({ returnType: "Object" });
  // originalNodes = JSON.parse(JSON.stringify(allNodes));
  // if something is selected:
  if (params.nodes.length > 0) {
    highlightActive = true;
    var i, j;
    var selectedNode = params.nodes[0];
    var degrees = 2;

    // mark all nodes as hard to read.
    for (let nodeId in allNodes) {
      // nodeColors[nodeId] = allNodes[nodeId].color;
      allNodes[nodeId].color = "rgba(200,200,200,0.5)";
      if (allNodes[nodeId].hiddenLabel === undefined) {
        allNodes[nodeId].hiddenLabel = allNodes[nodeId].label;
        allNodes[nodeId].label = undefined;
      }
    }
    var connectedNodes = network.getConnectedNodes(selectedNode);
    var allConnectedNodes = [];

    // get the second degree nodes
    for (i = 1; i < degrees; i++) {
      for (j = 0; j < connectedNodes.length; j++) {
        allConnectedNodes = allConnectedNodes.concat(
          network.getConnectedNodes(connectedNodes[j])
        );
      }
    }

    // all second degree nodes get a different color and their label back
    for (i = 0; i < allConnectedNodes.length; i++) {
      // allNodes[allConnectedNodes[i]].color = "pink";
      allNodes[allConnectedNodes[i]].color = "rgba(150,150,150,0.75)";
      if (allNodes[allConnectedNodes[i]].hiddenLabel !== undefined) {
        allNodes[allConnectedNodes[i]].label =
          allNodes[allConnectedNodes[i]].hiddenLabel;
        allNodes[allConnectedNodes[i]].hiddenLabel = undefined;
      }
    }

    // all first degree nodes get their own color and their label back
    for (i = 0; i < connectedNodes.length; i++) {
      // allNodes[connectedNodes[i]].color = undefined;
      allNodes[connectedNodes[i]].color = nodeColors[connectedNodes[i]];
      if (allNodes[connectedNodes[i]].hiddenLabel !== undefined) {
        allNodes[connectedNodes[i]].label =
          allNodes[connectedNodes[i]].hiddenLabel;
        allNodes[connectedNodes[i]].hiddenLabel = undefined;
      }
    }

    // the main node gets its own color and its label back.
    // allNodes[selectedNode].color = undefined;
    allNodes[selectedNode].color = nodeColors[selectedNode];
    if (allNodes[selectedNode].hiddenLabel !== undefined) {
      allNodes[selectedNode].label = allNodes[selectedNode].hiddenLabel;
      allNodes[selectedNode].hiddenLabel = undefined;
    }
  } else if (highlightActive === true) {
    // console.log("highlightActive was true");
    // reset all nodes
    for (let nodeId in allNodes) {
      // allNodes[nodeId].color = "purple";
      allNodes[nodeId].color = nodeColors[nodeId];
      // delete allNodes[nodeId].color;
      if (allNodes[nodeId].hiddenLabel !== undefined) {
        allNodes[nodeId].label = allNodes[nodeId].hiddenLabel;
        allNodes[nodeId].hiddenLabel = undefined;
      }
    }
    highlightActive = false;
  }

  // transform the object into an array
  var updateArray = [];
  if (params.nodes.length > 0) {
    for (let nodeId in allNodes) {
      if (allNodes.hasOwnProperty(nodeId)) {
        // console.log(allNodes[nodeId]);
        updateArray.push(allNodes[nodeId]);
      }
    }
    nodes.update(updateArray);
  } else {
    // console.log("Nothing was selected");
    for (let nodeId in allNodes) {
      if (allNodes.hasOwnProperty(nodeId)) {
        // console.log(allNodes[nodeId]);
        // allNodes[nodeId].color = {};
        updateArray.push(allNodes[nodeId]);
      }
    }
    nodes.update(updateArray);
  }
}

function filterHighlight(params) {
  allNodes = nodes.get({ returnType: "Object" });
  // if something is selected:
  if (params.nodes.length > 0) {
    filterActive = true;
    let selectedNodes = params.nodes;

    // hiding all nodes and saving the label
    for (let nodeId in allNodes) {
      allNodes[nodeId].hidden = true;
      if (allNodes[nodeId].savedLabel === undefined) {
        allNodes[nodeId].savedLabel = allNodes[nodeId].label;
        allNodes[nodeId].label = undefined;
      }
    }

    for (let i=0; i < selectedNodes.length; i++) {
      allNodes[selectedNodes[i]].hidden = false;
      if (allNodes[selectedNodes[i]].savedLabel !== undefined) {
        allNodes[selectedNodes[i]].label = allNodes[selectedNodes[i]].savedLabel;
        allNodes[selectedNodes[i]].savedLabel = undefined;
      }
    }

  } else if (filterActive === true) {
    // reset all nodes
    for (let nodeId in allNodes) {
      allNodes[nodeId].hidden = false;
      if (allNodes[nodeId].savedLabel !== undefined) {
        allNodes[nodeId].label = allNodes[nodeId].savedLabel;
        allNodes[nodeId].savedLabel = undefined;
      }
    }
    filterActive = false;
  }

  // transform the object into an array
  var updateArray = [];
  if (params.nodes.length > 0) {
    for (let nodeId in allNodes) {
      if (allNodes.hasOwnProperty(nodeId)) {
        updateArray.push(allNodes[nodeId]);
      }
    }
    nodes.update(updateArray);
  } else {
    for (let nodeId in allNodes) {
      if (allNodes.hasOwnProperty(nodeId)) {
        updateArray.push(allNodes[nodeId]);
      }
    }
    nodes.update(updateArray);
  }
}

function selectNode(nodes) {
  network.selectNodes(nodes);
  neighbourhoodHighlight({ nodes: nodes });
  return nodes;
}

function selectNodes(nodes) {
  network.selectNodes(nodes);
  filterHighlight({nodes: nodes});
  return nodes;
}

function highlightFilter(filter) {
  let selectedNodes = []
  let selectedProp = filter['property']
  if (filter['item'] === 'node') {
    let allNodes = nodes.get({ returnType: "Object" });
    for (let nodeId in allNodes) {
      if (allNodes[nodeId][selectedProp] && filter['value'].includes((allNodes[nodeId][selectedProp]).toString())) {
        selectedNodes.push(nodeId)
      }
    }
  }
  else if (filter['item'] === 'edge'){
    let allEdges = edges.get({returnType: 'object'});
    // check if the selected property exists for selected edge and select the nodes connected to the edge
    for (let edge in allEdges) {
      if (allEdges[edge][selectedProp] && filter['value'].includes((allEdges[edge][selectedProp]).toString())) {
        selectedNodes.push(allEdges[edge]['from'])
        selectedNodes.push(allEdges[edge]['to'])
      }
    }
  }
  selectNodes(selectedNodes)
}</script>
            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/vis-network/9.1.2/dist/dist/vis-network.min.css" integrity="sha512-WgxfT5LWjfszlPHXRmBWHkV2eceiWTOBvrKCNbdgDYTHrT2AeLCGbF4sZlZw3UMN3WtL0tGUoIAKsu8mllg/XA==" crossorigin="anonymous" referrerpolicy="no-referrer" />
            <script src="https://cdnjs.cloudflare.com/ajax/libs/vis-network/9.1.2/dist/vis-network.min.js" integrity="sha512-LnvoEWDFrqGHlHmDD2101OrLcbsfkrzoSpvtSQtxK3RMnRV0eOkhhBN2dXHKRrUU8p2DGRTk35n4O8nWSVe1mQ==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
            
            
            
            
            
            

        
<center>
<h1></h1>
</center>

<!-- <link rel="stylesheet" href="../node_modules/vis/dist/vis.min.css" type="text/css" />
<script type="text/javascript" src="../node_modules/vis/dist/vis.js"> </script>-->
        <link
          href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0-beta3/dist/css/bootstrap.min.css"
          rel="stylesheet"
          integrity="sha384-eOJMYsd53ii+scO/bJGFsiCZc+5NDVN2yr8+0RDqr0Ql0h+rP48ckxlpbzKgwra6"
          crossorigin="anonymous"
        />
        <script
          src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0-beta3/dist/js/bootstrap.bundle.min.js"
          integrity="sha384-JEW9xMcG8R+pH31jmWH6WWP0WintQrMb4s7ZOdauHnUtxwoG2vI5DkLtS3qm9Ekf"
          crossorigin="anonymous"
        ></script>


        <center>
          <h1></h1>
        </center>
        <style type="text/css">

             #mynetwork {
                 width: 100%;
                 height: 750px;
                 background-color: #222222;
                 border: 1px solid lightgray;
                 position: relative;
                 float: left;
             }

             

             

             
        </style>
    </head>


    <body>
        <div class="card" style="width: 100%">
            
            
            <div id="mynetwork" class="card-body"></div>
        </div>

        
        

        <script type="text/javascript">

              // initialize global variables.
              var edges;
              var nodes;
              var allNodes;
              var allEdges;
              var nodeColors;
              var originalNodes;
              var network;
              var container;
              var options, data;
              var filter = {
                  item : '',
                  property : '',
                  value : []
              };

              

              

              // This method is responsible for drawing the graph, returns the drawn network
              function drawGraph() {
                  var container = document.getElementById('mynetwork');

                  

                  // parsing and collecting nodes and edges from the python
                  nodes = new vis.DataSet([{"borderWidth": 2, "borderWidthSelected": 4, "color": "#FF9800", "fixed": {"x": true, "y": true}, "font": {"color": "white"}, "id": "10.3390/app14199106", "label": "Changes in Skeletal Muscle Atrophy over Time in a Rat Model of Adenine-Induced C...", "shape": "dot", "size": 27, "title": "\ud83d\udcc4 Changes in Skeletal Muscle Atrophy over Time in a Rat Model of Adenine-Induced Chronic Kidney Disease\n\ud83d\udcca \u88ab\u5f15\u6b21\u6570: 1\n\ud83d\udcc8 \u5f71\u54cd\u56e0\u5b50: 47.73\n\ud83d\udcd6 \u671f\u520a: Applied Sciences\n\ud83d\udcc5 \u5e74\u4efd: 2024\n\ud83d\udd17 DOI: 10.3390/app14199106", "x": 0, "y": 0}, {"borderWidth": 2, "borderWidthSelected": 4, "color": "#2196F3", "fixed": {"x": true, "y": true}, "font": {"color": "white"}, "id": "10.1038/sj.ki.5002343", "label": "Chronic kidney disease as a global public health problem: Ap... (2007)", "shape": "dot", "size": 30, "title": "\ud83d\udcc4 Chronic kidney disease as a global public health problem: Approaches and initiatives \u2013 a position statement from Kidney Disease Improving Global Outcomes\n\ud83d\udcca \u88ab\u5f15\u6b21\u6570: 1456\n\ud83d\udcc8 \u5f71\u54cd\u56e0\u5b50: 3.29\n\ud83d\udcd6 \u671f\u520a: Kidney International\n\ud83d\udcc5 \u5e74\u4efd: 2007\n\ud83d\udd17 DOI: 10.1038/sj.ki.5002343", "x": -850, "y": -150}, {"borderWidth": 2, "borderWidthSelected": 4, "color": "#2196F3", "fixed": {"x": true, "y": true}, "font": {"color": "white"}, "id": "10.1007/978-981-13-8871-2_1", "label": "Prevalence and Disease Burden of Chronic Kidney Disease... (2019)", "shape": "dot", "size": 30, "title": "\ud83d\udcc4 Prevalence and Disease Burden of Chronic Kidney Disease\n\ud83d\udcca \u88ab\u5f15\u6b21\u6570: 703\n\ud83d\udcc8 \u5f71\u54cd\u56e0\u5b50: 9.03\n\ud83d\udcd6 \u671f\u520a: Advances in Experimental Medicine and Biology\n\ud83d\udcc5 \u5e74\u4efd: 2019\n\ud83d\udd17 DOI: 10.1007/978-981-13-8871-2_1", "x": -250, "y": -150}, {"borderWidth": 2, "borderWidthSelected": 4, "color": "#2196F3", "fixed": {"x": true, "y": true}, "font": {"color": "white"}, "id": "10.2174/1381612820666140212194926", "label": "Vascular Calcification in Chronic Kidney Disease: Role of Di... (2014)", "shape": "dot", "size": 30, "title": "\ud83d\udcc4 Vascular Calcification in Chronic Kidney Disease: Role of Disordered Mineral Metabolism\n\ud83d\udcca \u88ab\u5f15\u6b21\u6570: 120\n\ud83d\udcc8 \u5f71\u54cd\u56e0\u5b50: 1.76\n\ud83d\udcd6 \u671f\u520a: Current Pharmaceutical Design\n\ud83d\udcc5 \u5e74\u4efd: 2014\n\ud83d\udd17 DOI: 10.2174/1381612820666140212194926", "x": -500, "y": -50}, {"borderWidth": 2, "borderWidthSelected": 4, "color": "#2196F3", "fixed": {"x": true, "y": true}, "font": {"color": "white"}, "id": "10.3390/ijms23116047", "label": "Muscle Wasting in Chronic Kidney Disease: Mechanism and Clin... (2022)", "shape": "dot", "size": 30, "title": "\ud83d\udcc4 Muscle Wasting in Chronic Kidney Disease: Mechanism and Clinical Implications\u2014A Narrative Review\n\ud83d\udcca \u88ab\u5f15\u6b21\u6570: 32\n\ud83d\udcc8 \u5f71\u54cd\u56e0\u5b50: 47.73\n\ud83d\udcd6 \u671f\u520a: International Journal of Molecular Sciences\n\ud83d\udcc5 \u5e74\u4efd: 2022\n\ud83d\udd17 DOI: 10.3390/ijms23116047", "x": -100, "y": 30.0}, {"borderWidth": 2, "borderWidthSelected": 4, "color": "#2196F3", "fixed": {"x": true, "y": true}, "font": {"color": "white"}, "id": "10.2215/CJN.01970309", "label": "Physical Activity and Mortality in Chronic Kidney Disease (N... (2009)", "shape": "dot", "size": 30, "title": "\ud83d\udcc4 Physical Activity and Mortality in Chronic Kidney Disease (NHANES III)\n\ud83d\udcca \u88ab\u5f15\u6b21\u6570: 269\n\ud83d\udcc8 \u5f71\u54cd\u56e0\u5b50: 8.40\n\ud83d\udcd6 \u671f\u520a: Clinical Journal of the American Society of Nephrology\n\ud83d\udcc5 \u5e74\u4efd: 2009\n\ud83d\udd17 DOI: 10.2215/CJN.01970309", "x": -750, "y": -50}, {"borderWidth": 2, "borderWidthSelected": 4, "color": "#2196F3", "fixed": {"x": true, "y": true}, "font": {"color": "white"}, "id": "10.3389/fphys.2017.00541", "label": "The Effect of Resistance Exercise on Inflammatory and Myogen... (2017)", "shape": "dot", "size": 30, "title": "\ud83d\udcc4 The Effect of Resistance Exercise on Inflammatory and Myogenic Markers in Patients with Chronic Kidney Disease\n\ud83d\udcca \u88ab\u5f15\u6b21\u6570: 38\n\ud83d\udcc8 \u5f71\u54cd\u56e0\u5b50: 3.20\n\ud83d\udcd6 \u671f\u520a: Frontiers in Physiology\n\ud83d\udcc5 \u5e74\u4efd: 2017\n\ud83d\udd17 DOI: 10.3389/fphys.2017.00541", "x": -350, "y": 70.0}, {"borderWidth": 2, "borderWidthSelected": 4, "color": "#2196F3", "fixed": {"x": true, "y": true}, "font": {"color": "white"}, "id": "10.1371/journal.pone.0159411", "label": "Skeletal Muscle Regeneration and Oxidative Stress Are Altere... (2016)", "shape": "dot", "size": 30, "title": "\ud83d\udcc4 Skeletal Muscle Regeneration and Oxidative Stress Are Altered in Chronic Kidney Disease\n\ud83d\udcca \u88ab\u5f15\u6b21\u6570: 74\n\ud83d\udcc8 \u5f71\u54cd\u56e0\u5b50: 3.24\n\ud83d\udcd6 \u671f\u520a: PLOS ONE\n\ud83d\udcc5 \u5e74\u4efd: 2016\n\ud83d\udd17 DOI: 10.1371/journal.pone.0159411", "x": -400, "y": 30.0}, {"borderWidth": 2, "borderWidthSelected": 4, "color": "#2196F3", "fixed": {"x": true, "y": true}, "font": {"color": "white"}, "id": "10.3390/ijms232113515", "label": "Uremic Myopathy and Mitochondrial Dysfunction in Kidney Dise... (2022)", "shape": "dot", "size": 26, "title": "\ud83d\udcc4 Uremic Myopathy and Mitochondrial Dysfunction in Kidney Disease\n\ud83d\udcca \u88ab\u5f15\u6b21\u6570: 5\n\ud83d\udcc8 \u5f71\u54cd\u56e0\u5b50: 47.73\n\ud83d\udcd6 \u671f\u520a: International Journal of Molecular Sciences\n\ud83d\udcc5 \u5e74\u4efd: 2022\n\ud83d\udd17 DOI: 10.3390/ijms232113515", "x": -100, "y": 70.0}, {"borderWidth": 2, "borderWidthSelected": 4, "color": "#2196F3", "fixed": {"x": true, "y": true}, "font": {"color": "white"}, "id": "10.1080/08860220801985694", "label": "Influence of Different Stages of Experimental Chronic Kidney... (2008)", "shape": "dot", "size": 30, "title": "\ud83d\udcc4 Influence of Different Stages of Experimental Chronic Kidney Disease on Rats Locomotor and Postural Skeletal Muscles Microcirculation\n\ud83d\udcca \u88ab\u5f15\u6b21\u6570: 23\n\ud83d\udcc8 \u5f71\u54cd\u56e0\u5b50: 2.37\n\ud83d\udcd6 \u671f\u520a: Renal Failure\n\ud83d\udcc5 \u5e74\u4efd: 2008\n\ud83d\udd17 DOI: 10.1080/08860220801985694", "x": -800, "y": 50}, {"borderWidth": 2, "borderWidthSelected": 4, "color": "#2196F3", "fixed": {"x": true, "y": true}, "font": {"color": "white"}, "id": "10.1002/jcsm.12296", "label": "miRNA\u201023a/27a attenuates muscle atrophy and renal fibrosis t... (2018)", "shape": "dot", "size": 30, "title": "\ud83d\udcc4 miRNA\u201023a/27a attenuates muscle atrophy and renal fibrosis through muscle\u2010kidney crosstalk\n\ud83d\udcca \u88ab\u5f15\u6b21\u6570: 121\n\ud83d\udcc8 \u5f71\u54cd\u56e0\u5b50: 5.90\n\ud83d\udcd6 \u671f\u520a: Journal of Cachexia, Sarcopenia and Muscle\n\ud83d\udcc5 \u5e74\u4efd: 2018\n\ud83d\udd17 DOI: 10.1002/jcsm.12296", "x": -300, "y": -50}, {"borderWidth": 2, "borderWidthSelected": 4, "color": "#F44336", "fixed": {"x": true, "y": true}, "font": {"color": "white"}, "id": "10.3390/ijms26073098", "label": "Inhibiting Myostatin Expression by the Antisense Oligonucleo... (2025)", "shape": "dot", "size": 12, "title": "\ud83d\udcc4 Inhibiting Myostatin Expression by the Antisense Oligonucleotides Improves Muscle Wasting in a Chronic Kidney Disease Mouse Model\n\ud83d\udcca \u88ab\u5f15\u6b21\u6570: 0\n\ud83d\udcc8 \u5f71\u54cd\u56e0\u5b50: 47.73\n\ud83d\udcd6 \u671f\u520a: International Journal of Molecular Sciences\n\ud83d\udcc5 \u5e74\u4efd: 2025\n\ud83d\udd17 DOI: 10.3390/ijms26073098", "x": 50, "y": -100}, {"color": "#666666", "fixed": {"x": true, "y": true}, "font": {"color": "white"}, "id": "timeline_2007", "label": "2007", "physics": false, "shape": "box", "size": 8, "x": -850, "y": 350}, {"color": "#666666", "fixed": {"x": true, "y": true}, "font": {"color": "white"}, "id": "timeline_2008", "label": "2008", "physics": false, "shape": "box", "size": 8, "x": -800, "y": 350}, {"color": "#666666", "fixed": {"x": true, "y": true}, "font": {"color": "white"}, "id": "timeline_2009", "label": "2009", "physics": false, "shape": "box", "size": 8, "x": -750, "y": 350}, {"color": "#666666", "fixed": {"x": true, "y": true}, "font": {"color": "white"}, "id": "timeline_2014", "label": "2014", "physics": false, "shape": "box", "size": 8, "x": -500, "y": 350}, {"color": "#666666", "fixed": {"x": true, "y": true}, "font": {"color": "white"}, "id": "timeline_2016", "label": "2016", "physics": false, "shape": "box", "size": 8, "x": -400, "y": 350}, {"color": "#666666", "fixed": {"x": true, "y": true}, "font": {"color": "white"}, "id": "timeline_2017", "label": "2017", "physics": false, "shape": "box", "size": 8, "x": -350, "y": 350}, {"color": "#666666", "fixed": {"x": true, "y": true}, "font": {"color": "white"}, "id": "timeline_2018", "label": "2018", "physics": false, "shape": "box", "size": 8, "x": -300, "y": 350}, {"color": "#666666", "fixed": {"x": true, "y": true}, "font": {"color": "white"}, "id": "timeline_2019", "label": "2019", "physics": false, "shape": "box", "size": 8, "x": -250, "y": 350}, {"color": "#666666", "fixed": {"x": true, "y": true}, "font": {"color": "white"}, "id": "timeline_2022", "label": "2022", "physics": false, "shape": "box", "size": 8, "x": -100, "y": 350}, {"color": "#666666", "fixed": {"x": true, "y": true}, "font": {"color": "white"}, "id": "timeline_2025", "label": "2025", "physics": false, "shape": "box", "size": 8, "x": 50, "y": 350}]);
                  edges = new vis.DataSet([{"arrows": "to", "color": "#666666", "from": "10.3390/app14199106", "to": "10.3390/ijms26073098", "width": 2}, {"arrows": "to", "color": "#666666", "from": "10.1038/sj.ki.5002343", "to": "10.3390/app14199106", "width": 2}, {"arrows": "to", "color": "#666666", "from": "10.1007/978-981-13-8871-2_1", "to": "10.3390/app14199106", "width": 2}, {"arrows": "to", "color": "#666666", "from": "10.2174/1381612820666140212194926", "to": "10.3390/app14199106", "width": 2}, {"arrows": "to", "color": "#666666", "from": "10.3390/ijms23116047", "to": "10.3390/app14199106", "width": 2}, {"arrows": "to", "color": "#666666", "from": "10.2215/CJN.01970309", "to": "10.3390/app14199106", "width": 2}, {"arrows": "to", "color": "#666666", "from": "10.3389/fphys.2017.00541", "to": "10.3390/app14199106", "width": 2}, {"arrows": "to", "color": "#666666", "from": "10.1371/journal.pone.0159411", "to": "10.3390/app14199106", "width": 2}, {"arrows": "to", "color": "#666666", "from": "10.3390/ijms232113515", "to": "10.3390/app14199106", "width": 2}, {"arrows": "to", "color": "#666666", "from": "10.1080/08860220801985694", "to": "10.3390/app14199106", "width": 2}, {"arrows": "to", "color": "#666666", "from": "10.1002/jcsm.12296", "to": "10.3390/app14199106", "width": 2}]);

                  nodeColors = {};
                  allNodes = nodes.get({ returnType: "Object" });
                  for (nodeId in allNodes) {
                    nodeColors[nodeId] = allNodes[nodeId].color;
                  }
                  allEdges = edges.get({ returnType: "Object" });
                  // adding nodes and edges to the graph
                  data = {nodes: nodes, edges: edges};

                  var options = {"physics": {"enabled": false}, "layout": {"hierarchical": {"enabled": false}}, "interaction": {"dragNodes": true, "dragView": true, "zoomView": true}, "edges": {"smooth": {"enabled": true, "type": "continuous", "roundness": 0.5}, "arrows": {"to": {"enabled": true, "scaleFactor": 1.2}}}, "nodes": {"font": {"color": "white", "size": 12}}};

                  


                  

                  network = new vis.Network(container, data, options);

                  

                  

                  


                  

                  return network;

              }
              drawGraph();
        </script>
    </body>
</html>