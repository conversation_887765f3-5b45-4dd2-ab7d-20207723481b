<html>
    <head>
        <meta charset="utf-8">
        
            <script>function neighbourhoodHighlight(params) {
  // console.log("in nieghbourhoodhighlight");
  allNodes = nodes.get({ returnType: "Object" });
  // originalNodes = JSON.parse(JSON.stringify(allNodes));
  // if something is selected:
  if (params.nodes.length > 0) {
    highlightActive = true;
    var i, j;
    var selectedNode = params.nodes[0];
    var degrees = 2;

    // mark all nodes as hard to read.
    for (let nodeId in allNodes) {
      // nodeColors[nodeId] = allNodes[nodeId].color;
      allNodes[nodeId].color = "rgba(200,200,200,0.5)";
      if (allNodes[nodeId].hiddenLabel === undefined) {
        allNodes[nodeId].hiddenLabel = allNodes[nodeId].label;
        allNodes[nodeId].label = undefined;
      }
    }
    var connectedNodes = network.getConnectedNodes(selectedNode);
    var allConnectedNodes = [];

    // get the second degree nodes
    for (i = 1; i < degrees; i++) {
      for (j = 0; j < connectedNodes.length; j++) {
        allConnectedNodes = allConnectedNodes.concat(
          network.getConnectedNodes(connectedNodes[j])
        );
      }
    }

    // all second degree nodes get a different color and their label back
    for (i = 0; i < allConnectedNodes.length; i++) {
      // allNodes[allConnectedNodes[i]].color = "pink";
      allNodes[allConnectedNodes[i]].color = "rgba(150,150,150,0.75)";
      if (allNodes[allConnectedNodes[i]].hiddenLabel !== undefined) {
        allNodes[allConnectedNodes[i]].label =
          allNodes[allConnectedNodes[i]].hiddenLabel;
        allNodes[allConnectedNodes[i]].hiddenLabel = undefined;
      }
    }

    // all first degree nodes get their own color and their label back
    for (i = 0; i < connectedNodes.length; i++) {
      // allNodes[connectedNodes[i]].color = undefined;
      allNodes[connectedNodes[i]].color = nodeColors[connectedNodes[i]];
      if (allNodes[connectedNodes[i]].hiddenLabel !== undefined) {
        allNodes[connectedNodes[i]].label =
          allNodes[connectedNodes[i]].hiddenLabel;
        allNodes[connectedNodes[i]].hiddenLabel = undefined;
      }
    }

    // the main node gets its own color and its label back.
    // allNodes[selectedNode].color = undefined;
    allNodes[selectedNode].color = nodeColors[selectedNode];
    if (allNodes[selectedNode].hiddenLabel !== undefined) {
      allNodes[selectedNode].label = allNodes[selectedNode].hiddenLabel;
      allNodes[selectedNode].hiddenLabel = undefined;
    }
  } else if (highlightActive === true) {
    // console.log("highlightActive was true");
    // reset all nodes
    for (let nodeId in allNodes) {
      // allNodes[nodeId].color = "purple";
      allNodes[nodeId].color = nodeColors[nodeId];
      // delete allNodes[nodeId].color;
      if (allNodes[nodeId].hiddenLabel !== undefined) {
        allNodes[nodeId].label = allNodes[nodeId].hiddenLabel;
        allNodes[nodeId].hiddenLabel = undefined;
      }
    }
    highlightActive = false;
  }

  // transform the object into an array
  var updateArray = [];
  if (params.nodes.length > 0) {
    for (let nodeId in allNodes) {
      if (allNodes.hasOwnProperty(nodeId)) {
        // console.log(allNodes[nodeId]);
        updateArray.push(allNodes[nodeId]);
      }
    }
    nodes.update(updateArray);
  } else {
    // console.log("Nothing was selected");
    for (let nodeId in allNodes) {
      if (allNodes.hasOwnProperty(nodeId)) {
        // console.log(allNodes[nodeId]);
        // allNodes[nodeId].color = {};
        updateArray.push(allNodes[nodeId]);
      }
    }
    nodes.update(updateArray);
  }
}

function filterHighlight(params) {
  allNodes = nodes.get({ returnType: "Object" });
  // if something is selected:
  if (params.nodes.length > 0) {
    filterActive = true;
    let selectedNodes = params.nodes;

    // hiding all nodes and saving the label
    for (let nodeId in allNodes) {
      allNodes[nodeId].hidden = true;
      if (allNodes[nodeId].savedLabel === undefined) {
        allNodes[nodeId].savedLabel = allNodes[nodeId].label;
        allNodes[nodeId].label = undefined;
      }
    }

    for (let i=0; i < selectedNodes.length; i++) {
      allNodes[selectedNodes[i]].hidden = false;
      if (allNodes[selectedNodes[i]].savedLabel !== undefined) {
        allNodes[selectedNodes[i]].label = allNodes[selectedNodes[i]].savedLabel;
        allNodes[selectedNodes[i]].savedLabel = undefined;
      }
    }

  } else if (filterActive === true) {
    // reset all nodes
    for (let nodeId in allNodes) {
      allNodes[nodeId].hidden = false;
      if (allNodes[nodeId].savedLabel !== undefined) {
        allNodes[nodeId].label = allNodes[nodeId].savedLabel;
        allNodes[nodeId].savedLabel = undefined;
      }
    }
    filterActive = false;
  }

  // transform the object into an array
  var updateArray = [];
  if (params.nodes.length > 0) {
    for (let nodeId in allNodes) {
      if (allNodes.hasOwnProperty(nodeId)) {
        updateArray.push(allNodes[nodeId]);
      }
    }
    nodes.update(updateArray);
  } else {
    for (let nodeId in allNodes) {
      if (allNodes.hasOwnProperty(nodeId)) {
        updateArray.push(allNodes[nodeId]);
      }
    }
    nodes.update(updateArray);
  }
}

function selectNode(nodes) {
  network.selectNodes(nodes);
  neighbourhoodHighlight({ nodes: nodes });
  return nodes;
}

function selectNodes(nodes) {
  network.selectNodes(nodes);
  filterHighlight({nodes: nodes});
  return nodes;
}

function highlightFilter(filter) {
  let selectedNodes = []
  let selectedProp = filter['property']
  if (filter['item'] === 'node') {
    let allNodes = nodes.get({ returnType: "Object" });
    for (let nodeId in allNodes) {
      if (allNodes[nodeId][selectedProp] && filter['value'].includes((allNodes[nodeId][selectedProp]).toString())) {
        selectedNodes.push(nodeId)
      }
    }
  }
  else if (filter['item'] === 'edge'){
    let allEdges = edges.get({returnType: 'object'});
    // check if the selected property exists for selected edge and select the nodes connected to the edge
    for (let edge in allEdges) {
      if (allEdges[edge][selectedProp] && filter['value'].includes((allEdges[edge][selectedProp]).toString())) {
        selectedNodes.push(allEdges[edge]['from'])
        selectedNodes.push(allEdges[edge]['to'])
      }
    }
  }
  selectNodes(selectedNodes)
}</script>
            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/vis-network/9.1.2/dist/dist/vis-network.min.css" integrity="sha512-WgxfT5LWjfszlPHXRmBWHkV2eceiWTOBvrKCNbdgDYTHrT2AeLCGbF4sZlZw3UMN3WtL0tGUoIAKsu8mllg/XA==" crossorigin="anonymous" referrerpolicy="no-referrer" />
            <script src="https://cdnjs.cloudflare.com/ajax/libs/vis-network/9.1.2/dist/vis-network.min.js" integrity="sha512-LnvoEWDFrqGHlHmDD2101OrLcbsfkrzoSpvtSQtxK3RMnRV0eOkhhBN2dXHKRrUU8p2DGRTk35n4O8nWSVe1mQ==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
            
            
            
            
            
            

        
<center>
<h1></h1>
</center>

<!-- <link rel="stylesheet" href="../node_modules/vis/dist/vis.min.css" type="text/css" />
<script type="text/javascript" src="../node_modules/vis/dist/vis.js"> </script>-->
        <link
          href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0-beta3/dist/css/bootstrap.min.css"
          rel="stylesheet"
          integrity="sha384-eOJMYsd53ii+scO/bJGFsiCZc+5NDVN2yr8+0RDqr0Ql0h+rP48ckxlpbzKgwra6"
          crossorigin="anonymous"
        />
        <script
          src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0-beta3/dist/js/bootstrap.bundle.min.js"
          integrity="sha384-JEW9xMcG8R+pH31jmWH6WWP0WintQrMb4s7ZOdauHnUtxwoG2vI5DkLtS3qm9Ekf"
          crossorigin="anonymous"
        ></script>


        <center>
          <h1></h1>
        </center>
        <style type="text/css">

             #mynetwork {
                 width: 100%;
                 height: 750px;
                 background-color: #222222;
                 border: 1px solid lightgray;
                 position: relative;
                 float: left;
             }

             

             

             
        </style>
    </head>


    <body>
        <div class="card" style="width: 100%">
            
            
            <div id="mynetwork" class="card-body"></div>
        </div>

        
        

        <script type="text/javascript">

              // initialize global variables.
              var edges;
              var nodes;
              var allNodes;
              var allEdges;
              var nodeColors;
              var originalNodes;
              var network;
              var container;
              var options, data;
              var filter = {
                  item : '',
                  property : '',
                  value : []
              };

              

              

              // This method is responsible for drawing the graph, returns the drawn network
              function drawGraph() {
                  var container = document.getElementById('mynetwork');

                  

                  // parsing and collecting nodes and edges from the python
                  nodes = new vis.DataSet([{"borderWidth": 2, "borderWidthSelected": 4, "color": "#FF9800", "fixed": {"x": true, "y": true}, "font": {"color": "white"}, "id": "10.1038/nature12373", "label": "Kucsko et al. (2013)", "shape": "dot", "size": 40, "title": "\ud83d\udcc4 Nanometre-scale thermometry in a living cell\n\ud83d\udcca \u88ab\u5f15\u6b21\u6570: 1618\n\ud83d\udcc8 \u5f71\u54cd\u56e0\u5b50: 49.96\n\ud83d\udcd6 \u671f\u520a: Nature\n\ud83d\udcc5 \u5e74\u4efd: 2013\n\ud83d\udd17 DOI: 10.1038/nature12373", "x": 0, "y": 0}, {"borderWidth": 2, "borderWidthSelected": 4, "color": "reference", "fixed": {"x": true, "y": true}, "font": {"color": "white"}, "id": "10.3402/nano.v3i0.11586", "label": "Yue et al. (2012)", "shape": "dot", "size": 30, "title": "\ud83d\udcc4 Nanoscale thermal probing\n\ud83d\udcca \u88ab\u5f15\u6b21\u6570: 137\n\ud83d\udcc8 \u5f71\u54cd\u56e0\u5b50: 8.68\n\ud83d\udcd6 \u671f\u520a: Nano Reviews\n\ud83d\udcc5 \u5e74\u4efd: 2012\n\ud83d\udd17 DOI: 10.3402/nano.v3i0.11586", "x": -50, "y": -30.0}, {"borderWidth": 2, "borderWidthSelected": 4, "color": "reference", "fixed": {"x": true, "y": true}, "font": {"color": "white"}, "id": "10.1038/nature03509", "label": "Lucchetta et al. (2005)", "shape": "dot", "size": 30, "title": "\ud83d\udcc4 Dynamics of Drosophila embryonic patterning network perturbed in space and time using microfluidics\n\ud83d\udcca \u88ab\u5f15\u6b21\u6570: 446\n\ud83d\udcc8 \u5f71\u54cd\u56e0\u5b50: 49.96\n\ud83d\udcd6 \u671f\u520a: Nature\n\ud83d\udcc5 \u5e74\u4efd: 2005\n\ud83d\udd17 DOI: 10.1038/nature03509", "x": -400, "y": -50}, {"borderWidth": 2, "borderWidthSelected": 4, "color": "reference", "fixed": {"x": true, "y": true}, "font": {"color": "white"}, "id": "10.1016/j.cell.2009.11.006", "label": "Kumar et al. (2010)", "shape": "dot", "size": 30, "title": "\ud83d\udcc4 H2A.Z-Containing Nucleosomes Mediate the Thermosensory Response in Arabidopsis\n\ud83d\udcca \u88ab\u5f15\u6b21\u6570: 874\n\ud83d\udcc8 \u5f71\u54cd\u56e0\u5b50: 41.58\n\ud83d\udcd6 \u671f\u520a: Cell\n\ud83d\udcc5 \u5e74\u4efd: 2010\n\ud83d\udd17 DOI: 10.1016/j.cell.2009.11.006", "x": -150, "y": -150}, {"borderWidth": 2, "borderWidthSelected": 4, "color": "reference", "fixed": {"x": true, "y": true}, "font": {"color": "white"}, "id": "10.1038/nature11804", "label": "Lauschke et al. (2013)", "shape": "dot", "size": 30, "title": "\ud83d\udcc4 Scaling of embryonic patterning based on phase-gradient encoding\n\ud83d\udcca \u88ab\u5f15\u6b21\u6570: 188\n\ud83d\udcc8 \u5f71\u54cd\u56e0\u5b50: 49.96\n\ud83d\udcd6 \u671f\u520a: Nature\n\ud83d\udcc5 \u5e74\u4efd: 2013\n\ud83d\udd17 DOI: 10.1038/nature11804", "x": 0, "y": -70.0}, {"borderWidth": 2, "borderWidthSelected": 4, "color": "reference", "fixed": {"x": true, "y": true}, "font": {"color": "white"}, "id": "10.1038/nmeth.1278", "label": "Kamei et al. (2009)", "shape": "dot", "size": 30, "title": "\ud83d\udcc4 Infrared laser\u2013mediated gene induction in targeted single cells in vivo\n\ud83d\udcca \u88ab\u5f15\u6b21\u6570: 173\n\ud83d\udcc8 \u5f71\u54cd\u56e0\u5b50: 49.96\n\ud83d\udcd6 \u671f\u520a: Nature Methods\n\ud83d\udcc5 \u5e74\u4efd: 2009\n\ud83d\udd17 DOI: 10.1038/nmeth.1278", "x": -200, "y": -50}, {"borderWidth": 2, "borderWidthSelected": 4, "color": "reference", "fixed": {"x": true, "y": true}, "font": {"color": "white"}, "id": "10.1007/s10549-012-2393-x", "label": "Vreugdenburg et al. (2013)", "shape": "dot", "size": 30, "title": "\ud83d\udcc4 A systematic review of elastography, electrical impedance scanning, and digital infrared thermography for breast cancer screening and diagnosis\n\ud83d\udcca \u88ab\u5f15\u6b21\u6570: 94\n\ud83d\udcc8 \u5f71\u54cd\u56e0\u5b50: 7.99\n\ud83d\udcd6 \u671f\u520a: Breast Cancer Research and Treatment\n\ud83d\udcc5 \u5e74\u4efd: 2013\n\ud83d\udd17 DOI: 10.1007/s10549-012-2393-x", "x": 0, "y": 50}, {"borderWidth": 2, "borderWidthSelected": 4, "color": "reference", "fixed": {"x": true, "y": true}, "font": {"color": "white"}, "id": "10.1038/nrc3180", "label": "Schroeder et al. (2012)", "shape": "dot", "size": 30, "title": "\ud83d\udcc4 Treating metastatic cancer with nanotechnology\n\ud83d\udcca \u88ab\u5f15\u6b21\u6570: 1084\n\ud83d\udcc8 \u5f71\u54cd\u56e0\u5b50: 49.96\n\ud83d\udcd6 \u671f\u520a: Nature Reviews Cancer\n\ud83d\udcc5 \u5e74\u4efd: 2012\n\ud83d\udd17 DOI: 10.1038/nrc3180", "x": -50, "y": -150}, {"borderWidth": 2, "borderWidthSelected": 4, "color": "reference", "fixed": {"x": true, "y": true}, "font": {"color": "white"}, "id": "10.1016/j.canlet.2004.02.004", "label": "O\u0027Neal et al. (2004)", "shape": "dot", "size": 30, "title": "\ud83d\udcc4 Photo-thermal tumor ablation in mice using near infrared-absorbing nanoparticles\n\ud83d\udcca \u88ab\u5f15\u6b21\u6570: 1780\n\ud83d\udcc8 \u5f71\u54cd\u56e0\u5b50: 9.53\n\ud83d\udcd6 \u671f\u520a: Cancer Letters\n\ud83d\udcc5 \u5e74\u4efd: 2004\n\ud83d\udd17 DOI: 10.1016/j.canlet.2004.02.004", "x": -450, "y": -150}, {"borderWidth": 2, "borderWidthSelected": 4, "color": "reference", "fixed": {"x": true, "y": true}, "font": {"color": "white"}, "id": "10.1146/annurev.matsci.29.1.505", "label": "Majumdar (1999)", "shape": "dot", "size": 30, "title": "\ud83d\udcc4 SCANNING THERMAL MICROSCOPY\n\ud83d\udcca \u88ab\u5f15\u6b21\u6570: 524\n\ud83d\udcc8 \u5f71\u54cd\u56e0\u5b50: 47.73\n\ud83d\udcd6 \u671f\u520a: Annual Review of Materials Science\n\ud83d\udcc5 \u5e74\u4efd: 1999\n\ud83d\udd17 DOI: 10.1146/annurev.matsci.29.1.505", "x": -700, "y": -150}, {"borderWidth": 2, "borderWidthSelected": 4, "color": "reference", "fixed": {"x": true, "y": true}, "font": {"color": "white"}, "id": "10.1088/0960-1317/16/3/007", "label": "Kim et al. (2006)", "shape": "dot", "size": 30, "title": "\ud83d\udcc4 Micro-Raman thermometry for measuring the temperature distribution inside the microchannel of a polymerase chain reaction chip\n\ud83d\udcca \u88ab\u5f15\u6b21\u6570: 81\n\ud83d\udcc8 \u5f71\u54cd\u56e0\u5b50: 3.43\n\ud83d\udcd6 \u671f\u520a: Journal of Micromechanics and Microengineering\n\ud83d\udcc5 \u5e74\u4efd: 2006\n\ud83d\udd17 DOI: 10.1088/0960-1317/16/3/007", "x": -350, "y": 50}, {"borderWidth": 2, "borderWidthSelected": 4, "color": "citation", "fixed": {"x": true, "y": true}, "font": {"color": "white"}, "id": "10.1016/j.diamond.2025.112521", "label": "Zhu et al. (2025)", "shape": "dot", "size": 12, "title": "\ud83d\udcc4 Multimodal sensing based on nitrogen-vacancy center ensembles in deep-sea environments\n\ud83d\udcca \u88ab\u5f15\u6b21\u6570: 0\n\ud83d\udcc8 \u5f71\u54cd\u56e0\u5b50: 6.28\n\ud83d\udcd6 \u671f\u520a: Diamond and Related Materials\n\ud83d\udcc5 \u5e74\u4efd: 2025\n\ud83d\udd17 DOI: 10.1016/j.diamond.2025.112521", "x": 600, "y": -80.0}, {"borderWidth": 2, "borderWidthSelected": 4, "color": "citation", "fixed": {"x": true, "y": true}, "font": {"color": "white"}, "id": "10.1002/tee.70091", "label": "Inomata (2025)", "shape": "dot", "size": 12, "title": "\ud83d\udcc4 Cellular Temperature Sensing Techniques Leveraging Advantages of Micro/Nano\u2010Thermometers\n\ud83d\udcca \u88ab\u5f15\u6b21\u6570: 0\n\ud83d\udcc8 \u5f71\u54cd\u56e0\u5b50: 9.71\n\ud83d\udcd6 \u671f\u520a: IEEJ Transactions on Electrical and Electronic Engineering\n\ud83d\udcc5 \u5e74\u4efd: 2025\n\ud83d\udd17 DOI: 10.1002/tee.70091", "x": 600, "y": -200.0}, {"borderWidth": 2, "borderWidthSelected": 4, "color": "citation", "fixed": {"x": true, "y": true}, "font": {"color": "white"}, "id": "10.1016/j.cap.2025.07.001", "label": "Kim et al. (2025)", "shape": "dot", "size": 12, "title": "\ud83d\udcc4 Magnetic field mediated temperature sensing with a solid-state spin qubit: a proposal for hybrid quantum thermometry with a permanent magnet\n\ud83d\udcca \u88ab\u5f15\u6b21\u6570: 0\n\ud83d\udcc8 \u5f71\u54cd\u56e0\u5b50: 2.56\n\ud83d\udcd6 \u671f\u520a: Current Applied Physics\n\ud83d\udcc5 \u5e74\u4efd: 2025\n\ud83d\udd17 DOI: 10.1016/j.cap.2025.07.001", "x": 600, "y": -160.0}, {"borderWidth": 2, "borderWidthSelected": 4, "color": "citation", "fixed": {"x": true, "y": true}, "font": {"color": "white"}, "id": "10.1016/j.ceramint.2025.07.018", "label": "Shi et al. (2025)", "shape": "dot", "size": 12, "title": "\ud83d\udcc4 \u03b2-Ca3(PO4)2-type Eu2+-doped Phosphor: Applications in optical thermometry and full-spectrum lighting\n\ud83d\udcca \u88ab\u5f15\u6b21\u6570: 0\n\ud83d\udcc8 \u5f71\u54cd\u56e0\u5b50: 6.98\n\ud83d\udcd6 \u671f\u520a: Ceramics International\n\ud83d\udcc5 \u5e74\u4efd: 2025\n\ud83d\udd17 DOI: 10.1016/j.ceramint.2025.07.018", "x": 600, "y": -120.0}, {"borderWidth": 2, "borderWidthSelected": 4, "color": "citation", "fixed": {"x": true, "y": true}, "font": {"color": "white"}, "id": "10.1002/lpor.202500781", "label": "\u0106iri\u0107 et al. (2025)", "shape": "dot", "size": 12, "title": "\ud83d\udcc4 Sensor Fusion in Luminescence Thermometry: A Path to Higher Precision and Broader Applicability\n\ud83d\udcca \u88ab\u5f15\u6b21\u6570: 0\n\ud83d\udcc8 \u5f71\u54cd\u56e0\u5b50: 9.59\n\ud83d\udcd6 \u671f\u520a: Laser \u0026amp; Photonics Reviews\n\ud83d\udcc5 \u5e74\u4efd: 2025\n\ud83d\udd17 DOI: 10.1002/lpor.202500781", "x": 600, "y": -240.0}, {"borderWidth": 2, "borderWidthSelected": 4, "color": "citation", "fixed": {"x": true, "y": true}, "font": {"color": "white"}, "id": "10.1002/adfm.202510764", "label": "Wei et al. (2025)", "shape": "dot", "size": 12, "title": "\ud83d\udcc4 Ultra\u2010Sensitive Low\u2010Temperature Upconversion via Interfacial Energy Transfer Toward Visual Cryogenic Nanothermometry\n\ud83d\udcca \u88ab\u5f15\u6b21\u6570: 0\n\ud83d\udcc8 \u5f71\u54cd\u56e0\u5b50: 5.11\n\ud83d\udcd6 \u671f\u520a: Advanced Functional Materials\n\ud83d\udcc5 \u5e74\u4efd: 2025\n\ud83d\udd17 DOI: 10.1002/adfm.202510764", "x": 600, "y": -250}, {"borderWidth": 2, "borderWidthSelected": 4, "color": "citation", "fixed": {"x": true, "y": true}, "font": {"color": "white"}, "id": "10.1021/acsnano.5c00802", "label": "Roberts et al. (2025)", "shape": "dot", "size": 12, "title": "\ud83d\udcc4 Quantum Sensing with Spin Defects Beyond Diamond\n\ud83d\udcca \u88ab\u5f15\u6b21\u6570: 0\n\ud83d\udcc8 \u5f71\u54cd\u56e0\u5b50: 2.62\n\ud83d\udcd6 \u671f\u520a: ACS Nano\n\ud83d\udcc5 \u5e74\u4efd: 2025\n\ud83d\udd17 DOI: 10.1021/acsnano.5c00802", "x": 600, "y": -40.0}, {"borderWidth": 2, "borderWidthSelected": 4, "color": "citation", "fixed": {"x": true, "y": true}, "font": {"color": "white"}, "id": "10.1063/5.0264044", "label": "Wu et al. (2025)", "shape": "dot", "size": 12, "title": "\ud83d\udcc4 Doping optimization of charge state stability for shallow nitrogen-vacancy center ensembles in HPHT diamonds\n\ud83d\udcca \u88ab\u5f15\u6b21\u6570: 0\n\ud83d\udcc8 \u5f71\u54cd\u56e0\u5b50: 8.27\n\ud83d\udcd6 \u671f\u520a: Applied Physics Letters\n\ud83d\udcc5 \u5e74\u4efd: 2025\n\ud83d\udd17 DOI: 10.1063/5.0264044", "x": 600, "y": 40.0}, {"borderWidth": 2, "borderWidthSelected": 4, "color": "citation", "fixed": {"x": true, "y": true}, "font": {"color": "white"}, "id": "10.1039/d4nr03434a", "label": "Zeng et al. (2025)", "shape": "dot", "size": 17, "title": "\ud83d\udcc4 Ambient pressure response of spin defects in two-dimensional materials\n\ud83d\udcca \u88ab\u5f15\u6b21\u6570: 1\n\ud83d\udcc8 \u5f71\u54cd\u56e0\u5b50: 6.86\n\ud83d\udcd6 \u671f\u520a: Nanoscale\n\ud83d\udcc5 \u5e74\u4efd: 2025\n\ud83d\udd17 DOI: 10.1039/d4nr03434a", "x": 600, "y": 0.0}, {"borderWidth": 2, "borderWidthSelected": 4, "color": "citation", "fixed": {"x": true, "y": true}, "font": {"color": "white"}, "id": "10.1117/1.bios.2.3.030901", "label": "Kanno et al. (2025)", "shape": "dot", "size": 17, "title": "\ud83d\udcc4 High-speed fluorescence lifetime imaging microscopy: techniques, applications, and prospects\n\ud83d\udcca \u88ab\u5f15\u6b21\u6570: 1\n\ud83d\udcc8 \u5f71\u54cd\u56e0\u5b50: 9.73\n\ud83d\udcd6 \u671f\u520a: Biophotonics Discovery\n\ud83d\udcc5 \u5e74\u4efd: 2025\n\ud83d\udd17 DOI: 10.1117/1.bios.2.3.030901", "x": 600, "y": 80.0}, {"color": "#666666", "fixed": {"x": true, "y": true}, "font": {"color": "white"}, "id": "timeline_1999", "label": "1999", "physics": false, "shape": "box", "size": 8, "x": -700, "y": 350}, {"color": "#666666", "fixed": {"x": true, "y": true}, "font": {"color": "white"}, "id": "timeline_2004", "label": "2004", "physics": false, "shape": "box", "size": 8, "x": -450, "y": 350}, {"color": "#666666", "fixed": {"x": true, "y": true}, "font": {"color": "white"}, "id": "timeline_2005", "label": "2005", "physics": false, "shape": "box", "size": 8, "x": -400, "y": 350}, {"color": "#666666", "fixed": {"x": true, "y": true}, "font": {"color": "white"}, "id": "timeline_2006", "label": "2006", "physics": false, "shape": "box", "size": 8, "x": -350, "y": 350}, {"color": "#666666", "fixed": {"x": true, "y": true}, "font": {"color": "white"}, "id": "timeline_2009", "label": "2009", "physics": false, "shape": "box", "size": 8, "x": -200, "y": 350}, {"color": "#666666", "fixed": {"x": true, "y": true}, "font": {"color": "white"}, "id": "timeline_2010", "label": "2010", "physics": false, "shape": "box", "size": 8, "x": -150, "y": 350}, {"color": "#666666", "fixed": {"x": true, "y": true}, "font": {"color": "white"}, "id": "timeline_2012", "label": "2012", "physics": false, "shape": "box", "size": 8, "x": -50, "y": 350}, {"color": "#666666", "fixed": {"x": true, "y": true}, "font": {"color": "white"}, "id": "timeline_2025", "label": "2025", "physics": false, "shape": "box", "size": 8, "x": 600, "y": 350}]);
                  edges = new vis.DataSet([{"arrows": "to", "color": "#666666", "from": "10.1038/nature12373", "to": "10.1016/j.diamond.2025.112521", "width": 2}, {"arrows": "to", "color": "#666666", "from": "10.1038/nature12373", "to": "10.1002/tee.70091", "width": 2}, {"arrows": "to", "color": "#666666", "from": "10.1038/nature12373", "to": "10.1016/j.cap.2025.07.001", "width": 2}, {"arrows": "to", "color": "#666666", "from": "10.1038/nature12373", "to": "10.1016/j.ceramint.2025.07.018", "width": 2}, {"arrows": "to", "color": "#666666", "from": "10.1038/nature12373", "to": "10.1002/lpor.202500781", "width": 2}, {"arrows": "to", "color": "#666666", "from": "10.1038/nature12373", "to": "10.1002/adfm.202510764", "width": 2}, {"arrows": "to", "color": "#666666", "from": "10.1038/nature12373", "to": "10.1021/acsnano.5c00802", "width": 2}, {"arrows": "to", "color": "#666666", "from": "10.1038/nature12373", "to": "10.1063/5.0264044", "width": 2}, {"arrows": "to", "color": "#666666", "from": "10.1038/nature12373", "to": "10.1039/d4nr03434a", "width": 2}, {"arrows": "to", "color": "#666666", "from": "10.1038/nature12373", "to": "10.1117/1.bios.2.3.030901", "width": 2}, {"arrows": "to", "color": "#666666", "from": "10.3402/nano.v3i0.11586", "to": "10.1038/nature12373", "width": 2}, {"arrows": "to", "color": "#666666", "from": "10.1038/nature03509", "to": "10.1038/nature12373", "width": 2}, {"arrows": "to", "color": "#666666", "from": "10.1016/j.cell.2009.11.006", "to": "10.1038/nature12373", "width": 2}, {"arrows": "to", "color": "#666666", "from": "10.1038/nature11804", "to": "10.1038/nature12373", "width": 2}, {"arrows": "to", "color": "#666666", "from": "10.1038/nmeth.1278", "to": "10.1038/nature12373", "width": 2}, {"arrows": "to", "color": "#666666", "from": "10.1007/s10549-012-2393-x", "to": "10.1038/nature12373", "width": 2}, {"arrows": "to", "color": "#666666", "from": "10.1038/nrc3180", "to": "10.1038/nature12373", "width": 2}, {"arrows": "to", "color": "#666666", "from": "10.1016/j.canlet.2004.02.004", "to": "10.1038/nature12373", "width": 2}, {"arrows": "to", "color": "#666666", "from": "10.1146/annurev.matsci.29.1.505", "to": "10.1038/nature12373", "width": 2}, {"arrows": "to", "color": "#666666", "from": "10.1088/0960-1317/16/3/007", "to": "10.1038/nature12373", "width": 2}]);

                  nodeColors = {};
                  allNodes = nodes.get({ returnType: "Object" });
                  for (nodeId in allNodes) {
                    nodeColors[nodeId] = allNodes[nodeId].color;
                  }
                  allEdges = edges.get({ returnType: "Object" });
                  // adding nodes and edges to the graph
                  data = {nodes: nodes, edges: edges};

                  var options = {"physics": {"enabled": false}, "layout": {"hierarchical": {"enabled": false}}, "interaction": {"dragNodes": true, "dragView": true, "zoomView": true}, "edges": {"smooth": {"enabled": true, "type": "continuous", "roundness": 0.5}, "arrows": {"to": {"enabled": true, "scaleFactor": 1.2}}}, "nodes": {"font": {"color": "white", "size": 12}}};

                  


                  

                  network = new vis.Network(container, data, options);

                  

                  

                  


                  

                  return network;

              }
              drawGraph();
        </script>
    </body>
</html>