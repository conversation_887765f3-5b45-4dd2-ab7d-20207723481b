worker_processes 1;
error_log logs/error.log info;
pid logs/nginx.pid;

events {
    worker_connections 1024;
}

http {
    include mime.types;
    default_type application/octet-stream;

    # 启用GZIP压缩
    gzip on;
    gzip_min_length 1k;
    gzip_comp_level 6;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
    gzip_vary on;
    
    # 安全设置
    server_tokens off;
    add_header X-Content-Type-Options nosniff;
    add_header X-Frame-Options SAMEORIGIN;
    add_header X-XSS-Protection "1; mode=block";

    server {
        listen 80;
        server_name _;  # 替换为您的域名或IP
        
        # 反向代理到您本地的NNScholar
        location / {
            # 这里需要替换为您本地机器的实际IP和端口
            proxy_pass http://YOUR_LOCAL_IP:80;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;

            # WebSocket支持
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_read_timeout 86400;
        }

        # 错误页面保持相同风格
        error_page 500 502 503 504 /50x.html;
        location = /50x.html {
            root html;
        }
    }
} 