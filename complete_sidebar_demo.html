<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文献引用网络图谱 - 完整演示</title>
    <script src="https://cdn.jsdelivr.net/npm/vis-network@latest/standalone/umd/vis-network.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            overflow: hidden;
        }

        .container {
            display: flex;
            height: 100vh;
            background: white;
            margin: 10px;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .sidebar {
            width: 300px;
            background: #f8f9fa;
            overflow-y: auto;
            padding: 20px;
            box-sizing: border-box;
        }

        .sidebar.left {
            border-right: 1px solid #e9ecef;
        }

        .sidebar.right {
            border-left: 1px solid #e9ecef;
        }

        .sidebar h3 {
            margin: 0 0 15px 0;
            color: #495057;
            font-size: 18px;
            font-weight: 600;
            text-align: center;
            padding-bottom: 10px;
            border-bottom: 2px solid #007bff;
        }

        .paper-item {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }

        .paper-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            border-color: #007bff;
        }

        .paper-item.selected {
            border-color: #007bff;
            background: #f8f9ff;
            box-shadow: 0 4px 12px rgba(0,123,255,0.2);
        }

        .paper-title {
            font-weight: 600;
            color: #212529;
            font-size: 14px;
            line-height: 1.4;
            margin-bottom: 8px;
        }

        .paper-authors {
            color: #6c757d;
            font-size: 12px;
            margin-bottom: 5px;
        }

        .paper-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 11px;
            color: #868e96;
        }

        .paper-year {
            background: #e9ecef;
            padding: 2px 6px;
            border-radius: 4px;
        }

        .paper-citations {
            color: #007bff;
            font-weight: 500;
        }

        .network-container {
            flex: 1;
            position: relative;
            background: white;
        }

        #network {
            width: 100%;
            height: 100%;
        }

        .network-header {
            position: absolute;
            top: 20px;
            left: 20px;
            right: 20px;
            z-index: 1000;
            background: rgba(255, 255, 255, 0.95);
            padding: 15px 20px;
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }

        .network-title {
            font-size: 20px;
            font-weight: 700;
            color: #212529;
            margin: 0 0 5px 0;
        }

        .network-subtitle {
            font-size: 14px;
            color: #6c757d;
            margin: 0;
        }

        .legend {
            position: absolute;
            bottom: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.95);
            padding: 15px;
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            z-index: 1000;
        }

        .legend-title {
            font-weight: 600;
            margin-bottom: 10px;
            color: #495057;
        }

        .legend-item {
            display: flex;
            align-items: center;
            margin-bottom: 5px;
            font-size: 12px;
        }

        .legend-color {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .stats {
            position: absolute;
            bottom: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.95);
            padding: 15px;
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            z-index: 1000;
            text-align: center;
        }

        .stats-title {
            font-weight: 600;
            margin-bottom: 10px;
            color: #495057;
        }

        .stats-item {
            font-size: 12px;
            margin-bottom: 3px;
            color: #6c757d;
        }

        .stats-number {
            font-weight: 600;
            color: #007bff;
        }

        /* 滚动条样式 */
        .sidebar::-webkit-scrollbar {
            width: 6px;
        }

        .sidebar::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }

        .sidebar::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }

        .sidebar::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 左侧边栏：引用文献 -->
        <div class="sidebar left">
            <h3>📚 引用文献 (Prior Works)</h3>
            <div id="references-list">
                <!-- 引用文献列表将在这里动态生成 -->
            </div>
        </div>

        <!-- 中间网络图谱 -->
        <div class="network-container">
            <div class="network-header">
                <div class="network-title">文献引用网络图谱</div>
                <div class="network-subtitle">基于 Connected Papers 风格的可视化分析</div>
            </div>
            
            <div id="network"></div>
            
            <div class="legend">
                <div class="legend-title">图例</div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #ff6b6b;"></div>
                    <span>中心文献</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #4ecdc4;"></div>
                    <span>引用文献</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #45b7d1;"></div>
                    <span>被引文献</span>
                </div>
            </div>
            
            <div class="stats">
                <div class="stats-title">网络统计</div>
                <div class="stats-item">节点数: <span class="stats-number" id="node-count">0</span></div>
                <div class="stats-item">连接数: <span class="stats-number" id="edge-count">0</span></div>
                <div class="stats-item">密度: <span class="stats-number" id="density">0</span></div>
            </div>
        </div>

        <!-- 右侧边栏：被引文献 -->
        <div class="sidebar right">
            <h3>🔗 被引文献 (Derivative Works)</h3>
            <div id="citations-list">
                <!-- 被引文献列表将在这里动态生成 -->
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let network;
        let nodes, edges;

        // 测试数据
        const networkData = {
            "nodes": [
                {
                    "id": "center_paper",
                    "label": "Jumper et al. (2021)",
                    "color": "#ff6b6b",
                    "size": 30,
                    "node_type": "center",
                    "citation_count": 27312,
                    "impact_factor": 49.96,
                    "journal": "Nature",
                    "year": 2021,
                    "full_title": "Highly accurate protein structure prediction with AlphaFold",
                    "authors": ["John Jumper", "Richard Evans", "Alexander Pritzel"]
                },
                {
                    "id": "ref_paper_1",
                    "label": "Senior et al. (2020)",
                    "color": "#4ecdc4",
                    "size": 20,
                    "node_type": "reference",
                    "citation_count": 1500,
                    "impact_factor": 49.96,
                    "journal": "Nature",
                    "year": 2020,
                    "full_title": "Improved protein structure prediction using potentials from deep learning",
                    "authors": ["Andrew W. Senior", "Richard Evans", "John Jumper"]
                },
                {
                    "id": "ref_paper_2",
                    "label": "Wang et al. (2017)",
                    "color": "#4ecdc4",
                    "size": 18,
                    "node_type": "reference",
                    "citation_count": 800,
                    "impact_factor": 15.25,
                    "journal": "Bioinformatics",
                    "year": 2017,
                    "full_title": "Accurate De Novo Prediction of Protein Contact Map by Ultra-Deep Learning Model",
                    "authors": ["Sheng Wang", "Siqi Sun", "Zhen Li"]
                },
                {
                    "id": "cit_paper_1",
                    "label": "Baek et al. (2021)",
                    "color": "#45b7d1",
                    "size": 25,
                    "node_type": "citation",
                    "citation_count": 2500,
                    "impact_factor": 56.9,
                    "journal": "Science",
                    "year": 2021,
                    "full_title": "Accurate prediction of protein structures and interactions using a three-track neural network",
                    "authors": ["Minkyung Baek", "Frank DiMaio", "Ivan Anishchenko"]
                },
                {
                    "id": "cit_paper_2",
                    "label": "Tunyasuvunakool et al. (2021)",
                    "color": "#45b7d1",
                    "size": 22,
                    "node_type": "citation",
                    "citation_count": 1800,
                    "impact_factor": 49.96,
                    "journal": "Nature",
                    "year": 2021,
                    "full_title": "Highly accurate protein structure prediction for the human proteome",
                    "authors": ["Kathryn Tunyasuvunakool", "Jonas Adler", "Zachary Wu"]
                }
            ],
            "edges": [
                {"from": "ref_paper_1", "to": "center_paper"},
                {"from": "ref_paper_2", "to": "center_paper"},
                {"from": "center_paper", "to": "cit_paper_1"},
                {"from": "center_paper", "to": "cit_paper_2"}
            ]
        };

        // 初始化网络图谱
        function initNetwork() {
            console.log('开始初始化网络图谱');
            const container = document.getElementById('network');

            // 检查vis.js是否加载
            if (typeof vis === 'undefined') {
                console.error('vis.js未加载');
                container.innerHTML = '<div style="display: flex; align-items: center; justify-content: center; height: 100%; color: #666;">vis.js库加载失败</div>';
                return;
            }

            console.log('vis.js已加载');

            // 网络配置
            const options = {
                nodes: {
                    shape: 'dot',
                    font: {
                        size: 12,
                        color: '#333333',
                        face: 'Segoe UI'
                    },
                    borderWidth: 2,
                    shadow: {
                        enabled: true,
                        color: 'rgba(0,0,0,0.2)',
                        size: 5,
                        x: 2,
                        y: 2
                    }
                },
                edges: {
                    arrows: {
                        to: {
                            enabled: true,
                            scaleFactor: 0.8
                        }
                    },
                    color: {
                        color: '#848484',
                        highlight: '#007bff',
                        hover: '#007bff'
                    },
                    width: 2,
                    smooth: {
                        enabled: true,
                        type: 'dynamic',
                        roundness: 0.2
                    }
                },
                physics: {
                    enabled: true,
                    stabilization: {
                        enabled: true,
                        iterations: 200
                    },
                    barnesHut: {
                        gravitationalConstant: -8000,
                        centralGravity: 0.3,
                        springLength: 120,
                        springConstant: 0.04,
                        damping: 0.09,
                        avoidOverlap: 0.5
                    }
                },
                layout: {
                    improvedLayout: true,
                    clusterThreshold: 150
                },
                interaction: {
                    hover: true,
                    tooltipDelay: 200,
                    hideEdgesOnDrag: false,
                    hideNodesOnDrag: false
                }
            };

            // 创建网络
            try {
                network = new vis.Network(container, {}, options);
                console.log('网络对象创建成功');

                // 添加事件监听器
                network.on('click', onNodeClick);
                network.on('hoverNode', onNodeHover);
                network.on('blurNode', onNodeBlur);

                console.log('网络初始化完成');
            } catch (error) {
                console.error('创建网络时出错:', error);
                container.innerHTML = '<div style="display: flex; align-items: center; justify-content: center; height: 100%; color: #666;">网络创建失败: ' + error.message + '</div>';
            }
        }

        // 节点点击事件
        function onNodeClick(params) {
            if (params.nodes.length > 0) {
                const nodeId = params.nodes[0];
                highlightNode(nodeId);
                console.log('点击节点:', nodeId);
            }
        }

        // 节点悬停事件
        function onNodeHover(params) {
            console.log('悬停节点:', params.node);
        }

        // 节点失焦事件
        function onNodeBlur(params) {
            console.log('失焦节点:', params.node);
        }

        // 高亮节点
        function highlightNode(nodeId) {
            // 移除之前的高亮
            document.querySelectorAll('.paper-item.selected').forEach(item => {
                item.classList.remove('selected');
            });

            // 高亮对应的侧边栏项目
            const paperItem = document.querySelector(`[data-node-id="${nodeId}"]`);
            if (paperItem) {
                paperItem.classList.add('selected');
                paperItem.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
        }

        // 生成侧边栏文献列表
        function generateSidebarLists(data) {
            const referencesContainer = document.getElementById('references-list');
            const citationsContainer = document.getElementById('citations-list');

            // 清空现有内容
            referencesContainer.innerHTML = '';
            citationsContainer.innerHTML = '';

            // 分类节点
            const references = [];
            const citations = [];

            data.nodes.forEach(node => {
                if (node.node_type === 'reference') {
                    references.push(node);
                } else if (node.node_type === 'citation') {
                    citations.push(node);
                }
            });

            // 按被引次数排序
            references.sort((a, b) => (b.citation_count || 0) - (a.citation_count || 0));
            citations.sort((a, b) => (b.citation_count || 0) - (a.citation_count || 0));

            // 生成引用文献列表
            references.forEach(node => {
                const paperItem = createPaperItem(node);
                referencesContainer.appendChild(paperItem);
            });

            // 生成被引文献列表
            citations.forEach(node => {
                const paperItem = createPaperItem(node);
                citationsContainer.appendChild(paperItem);
            });

            // 如果没有数据，显示提示
            if (references.length === 0) {
                referencesContainer.innerHTML = '<div style="text-align: center; color: #6c757d; padding: 20px;">暂无引用文献数据</div>';
            }

            if (citations.length === 0) {
                citationsContainer.innerHTML = '<div style="text-align: center; color: #6c757d; padding: 20px;">暂无被引文献数据</div>';
            }
        }

        // 创建文献项目元素
        function createPaperItem(node) {
            const paperItem = document.createElement('div');
            paperItem.className = 'paper-item';
            paperItem.setAttribute('data-node-id', node.id);

            // 提取作者信息
            const authors = node.authors || [];
            const authorsText = authors.length > 0 ? authors.slice(0, 3).join(', ') + (authors.length > 3 ? ' et al.' : '') : '未知作者';

            paperItem.innerHTML = `
                <div class="paper-title">${node.full_title || node.label || '未知标题'}</div>
                <div class="paper-authors">${authorsText}</div>
                <div class="paper-meta">
                    <span class="paper-year">${node.year || '未知年份'}</span>
                    <span class="paper-citations">被引 ${node.citation_count || 0} 次</span>
                </div>
            `;

            // 添加点击事件
            paperItem.addEventListener('click', () => {
                highlightNode(node.id);
                // 在网络图中聚焦到该节点
                if (network) {
                    network.focus(node.id, {
                        scale: 1.5,
                        animation: {
                            duration: 1000,
                            easingFunction: 'easeInOutQuad'
                        }
                    });
                }
            });

            return paperItem;
        }

        // 更新统计信息
        function updateStats(data) {
            const nodeCount = data.nodes.length;
            const edgeCount = data.edges.length;
            const density = nodeCount > 1 ? (2 * edgeCount) / (nodeCount * (nodeCount - 1)) : 0;

            document.getElementById('node-count').textContent = nodeCount;
            document.getElementById('edge-count').textContent = edgeCount;
            document.getElementById('density').textContent = density.toFixed(3);
        }

        // 加载网络数据
        function loadNetworkData(data) {
            console.log('加载网络数据:', data);

            // 创建vis.js数据集
            nodes = new vis.DataSet(data.nodes);
            edges = new vis.DataSet(data.edges);

            console.log('节点数据:', nodes.get());
            console.log('边数据:', edges.get());

            // 更新网络
            if (network) {
                network.setData({ nodes: nodes, edges: edges });

                // 生成侧边栏列表
                generateSidebarLists(data);

                // 更新统计信息
                updateStats(data);

                // 网络稳定后聚焦到中心节点
                network.once('stabilizationIterationsDone', () => {
                    console.log('网络稳定完成');
                    const centerNode = data.nodes.find(node => node.node_type === 'center');
                    if (centerNode) {
                        console.log('聚焦到中心节点:', centerNode.id);
                        network.focus(centerNode.id, {
                            scale: 1.0,
                            animation: {
                                duration: 1500,
                                easingFunction: 'easeInOutQuad'
                            }
                        });
                    }
                });
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成，开始初始化');
            initNetwork();

            // 延迟加载数据，确保网络已初始化
            setTimeout(() => {
                loadNetworkData(networkData);
            }, 100);
        });
    </script>
</body>
</html>
