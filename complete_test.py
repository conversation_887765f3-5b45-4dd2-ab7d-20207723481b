#!/usr/bin/env python3
"""
完整的文献追踪功能测试
"""

import sys
import os
import json
import time
import requests
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_citation_network_service():
    """测试文献引用网络服务"""
    print("🧪 测试文献引用网络服务")
    
    try:
        from services.citation_network_service import citation_network_service
        
        # 测试DOI解析
        test_doi = "10.1038/nature12373"
        print(f"📄 测试DOI: {test_doi}")
        
        # 测试输入解析
        parsed_doi = citation_network_service.parse_input(test_doi)
        print(f"✅ DOI解析成功: {parsed_doi}")
        
        # 测试网络构建
        print("🔄 开始构建引用网络...")
        start_time = time.time()
        
        network_data = citation_network_service.build_citation_network(test_doi)
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"✅ 网络构建完成，耗时: {duration:.2f}秒")
        print(f"📊 网络统计:")
        print(f"   - 节点数: {len(network_data['nodes'])}")
        print(f"   - 边数: {len(network_data['edges'])}")
        
        # 分析节点类型
        node_types = {}
        for node in network_data['nodes']:
            node_type = node.get('node_type', 'unknown')
            node_types[node_type] = node_types.get(node_type, 0) + 1
        
        print(f"   - 节点类型分布:")
        for node_type, count in node_types.items():
            print(f"     * {node_type}: {count}")
        
        # 显示中心文献信息
        center_node = next((node for node in network_data['nodes'] if node.get('node_type') == 'center'), None)
        if center_node:
            print(f"\n🎯 中心文献:")
            print(f"   - 标题: {center_node.get('full_title', 'N/A')[:80]}...")
            print(f"   - 作者: {', '.join(center_node.get('authors', [])[:3])}")
            print(f"   - 年份: {center_node.get('year', 'N/A')}")
            print(f"   - 被引次数: {center_node.get('citation_count', 'N/A')}")
            print(f"   - 影响因子: {center_node.get('impact_factor', 'N/A')}")
        
        # 保存结果
        output_file = f"citation_network_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(network_data, f, ensure_ascii=False, indent=2)
        print(f"\n💾 结果已保存到: {output_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ 服务测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_api_endpoint():
    """测试API端点"""
    print("\n🌐 测试API端点")
    
    try:
        # 启动应用（如果还没启动）
        import subprocess
        import threading
        import time
        
        def start_app():
            try:
                subprocess.run([sys.executable, "app.py"], cwd=os.path.dirname(os.path.abspath(__file__)))
            except:
                pass
        
        # 在后台启动应用
        app_thread = threading.Thread(target=start_app, daemon=True)
        app_thread.start()
        
        # 等待应用启动
        print("⏳ 等待应用启动...")
        time.sleep(5)
        
        # 测试API
        test_doi = "10.1038/nature12373"
        api_url = "http://127.0.0.1:5000/api/citation-network"
        
        print(f"📡 发送API请求: {api_url}")
        response = requests.post(api_url, json={"input": test_doi}, timeout=300)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("✅ API调用成功")
                network_data = result.get('data', {})
                print(f"📊 返回数据包含 {len(network_data.get('nodes', []))} 个节点")
                return True
            else:
                print(f"❌ API返回错误: {result.get('error', '未知错误')}")
        else:
            print(f"❌ API调用失败: {response.status_code}")
            print(f"   响应: {response.text[:200]}")
        
        return False
        
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到应用，请手动启动 python app.py")
        return False
    except Exception as e:
        print(f"❌ API测试失败: {e}")
        return False

def generate_demo_html():
    """生成演示HTML文件"""
    print("\n📄 生成演示HTML文件")
    
    try:
        # 使用我们之前创建的完整演示文件
        demo_file = "complete_sidebar_demo.html"
        if os.path.exists(demo_file):
            print(f"✅ 演示文件已存在: {demo_file}")
            print(f"🌐 可以直接在浏览器中打开: file:///{os.path.abspath(demo_file)}")
            return True
        else:
            print(f"❌ 演示文件不存在: {demo_file}")
            return False
            
    except Exception as e:
        print(f"❌ 生成演示文件失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 NNScholar 文献追踪功能完整测试")
    print("=" * 50)
    
    results = []
    
    # 1. 测试服务
    print("\n1️⃣ 测试后端服务")
    service_ok = test_citation_network_service()
    results.append(("后端服务", service_ok))
    
    # 2. 测试API
    print("\n2️⃣ 测试API端点")
    api_ok = test_api_endpoint()
    results.append(("API端点", api_ok))
    
    # 3. 检查演示文件
    print("\n3️⃣ 检查演示文件")
    demo_ok = generate_demo_html()
    results.append(("演示文件", demo_ok))
    
    # 总结
    print("\n" + "=" * 50)
    print("📋 测试结果总结:")
    
    all_passed = True
    for test_name, passed in results:
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"   {test_name}: {status}")
        if not passed:
            all_passed = False
    
    if all_passed:
        print("\n🎉 所有测试通过！文献追踪功能已成功集成到NNScholar中！")
        print("\n📋 使用说明:")
        print("1. 启动应用: python app.py")
        print("2. 打开浏览器: http://127.0.0.1:5000/")
        print("3. 点击'文献追踪'标签")
        print("4. 输入DOI并生成网络图谱")
    else:
        print("\n⚠️ 部分测试失败，请检查错误信息并修复")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
