"""Security configuration for NNScholar."""

import os
from typing import Optional, List

class SecurityConfig:
    """Security settings for the application."""
    
    # Frame options configuration
    # Options: 'DENY', 'SAMEORIGIN', 'ALLOWALL', or specific domain
    FRAME_OPTIONS: str = os.getenv('FRAME_OPTIONS', 'SAMEORIGIN')
    
    # List of allowed iframe domains (if using custom allow list)
    ALLOWED_IFRAME_DOMAINS: List[str] = os.getenv('ALLOWED_IFRAME_DOMAINS', '').split(',') if os.getenv('ALLOWED_IFRAME_DOMAINS') else []
    
    # Content Security Policy
    CSP_ENABLED: bool = os.getenv('CSP_ENABLED', 'False').lower() == 'true'
    CSP_FRAME_ANCESTORS: str = os.getenv('CSP_FRAME_ANCESTORS', "'self'")
    
    @classmethod
    def get_frame_options_header(cls) -> Optional[tuple]:
        """Get X-Frame-Options header based on configuration."""
        if cls.FRAME_OPTIONS == 'ALLOWALL':
            # Don't set X-Frame-Options header to allow all iframes
            return None
        elif cls.FRAME_OPTIONS in ['DENY', 'SAMEORIGIN']:
            return ('X-Frame-Options', cls.FRAME_OPTIONS)
        else:
            # For backward compatibility, default to SAMEORIGIN
            return ('X-Frame-Options', 'SAMEORIGIN')
    
    @classmethod
    def get_csp_header(cls) -> Optional[tuple]:
        """Get Content-Security-Policy header."""
        if cls.CSP_ENABLED:
            csp_directives = []
            
            # Frame ancestors directive
            if cls.CSP_FRAME_ANCESTORS:
                csp_directives.append(f"frame-ancestors {cls.CSP_FRAME_ANCESTORS}")
            
            if csp_directives:
                return ('Content-Security-Policy', '; '.join(csp_directives))
        
        return None

# Global security config instance
security_config = SecurityConfig()