# iframe嵌入配置指南

## 问题说明
默认情况下，应用会阻止iframe嵌入以防止点击劫持攻击。如果需要在其他网站中通过iframe嵌入本应用，需要进行以下配置。

## 解决方案

### 1. 环境变量配置

在Railway或本地环境中设置以下环境变量：

#### 选项A：完全允许iframe（最简单）
```
IFRAME_MODE=ALLOW
```

#### 选项B：只允许同源iframe
```
IFRAME_MODE=SAMEORIGIN
```

#### 选项C：允许特定域名
```
IFRAME_MODE=CUSTOM
IFRAME_ALLOWED_DOMAINS=https://trusted-site1.com https://trusted-site2.com
```

### 2. Railway配置步骤

1. 登录Railway控制台
2. 进入项目设置
3. 找到"Variables"部分
4. 添加环境变量：
   - Key: `IFRAME_MODE`
   - Value: `ALLOW`
5. 保存并重新部署

### 3. 本地测试

在`.env`文件中添加：
```
IFRAME_MODE=ALLOW
```

然后重启应用。

### 4. 测试iframe嵌入

```html
<!DOCTYPE html>
<html>
<head>
    <title>iframe测试</title>
</head>
<body>
    <iframe 
        src="https://nnscholarweb-production.up.railway.app/" 
        width="100%" 
        height="600"
        frameborder="0">
    </iframe>
</body>
</html>
```

## 注意事项

1. **安全性**：允许iframe嵌入会降低安全性，请确保只在信任的环境中使用
2. **HTTPS**：确保父页面和嵌入页面都使用HTTPS
3. **浏览器限制**：某些浏览器可能有额外的安全限制

## 故障排除

如果仍然无法嵌入，检查：
1. 浏览器控制台是否有错误信息
2. 网络请求是否被阻止
3. 是否有其他安全策略（如CSP）干扰