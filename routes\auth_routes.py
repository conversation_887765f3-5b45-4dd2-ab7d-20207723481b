"""
用户认证路由 - 处理用户注册、登录、认证
"""
from flask import Blueprint, request, jsonify, session
from functools import wraps
from models.user import user_manager
from services.payment_service import payment_service
import logging

logger = logging.getLogger(__name__)

auth_bp = Blueprint('auth', __name__)

def login_required(f):
    """登录验证装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        session_token = session.get('session_token')
        if not session_token:
            return jsonify({'error': '未登录'}), 401
        
        user = user_manager.validate_session(session_token)
        if not user:
            return jsonify({'error': '会话无效'}), 401
        
        return f(user, *args, **kwargs)
    
    return decorated_function

@auth_bp.route('/api/register', methods=['POST'])
def register():
    """用户注册"""
    try:
        data = request.get_json()
        email = data.get('email')
        username = data.get('username')
        password = data.get('password')
        
        if not all([email, username, password]):
            return jsonify({'error': '缺少必要参数'}), 400
        
        # 创建用户
        user = user_manager.create_user(email, username, password)
        if not user:
            return jsonify({'error': '用户已存在'}), 409
        
        # 创建会话
        session_token = user_manager.create_session(user.user_id)
        session['session_token'] = session_token
        
        return jsonify({
            'message': '注册成功',
            'user': {
                'id': user.user_id,
                'email': user.email,
                'username': user.username,
                'subscription_type': user.subscription_type,
                'is_premium': user.is_premium
            }
        })
        
    except Exception as e:
        logger.error(f"注册失败: {e}")
        return jsonify({'error': '注册失败'}), 500

@auth_bp.route('/api/login', methods=['POST'])
def login():
    """用户登录"""
    try:
        data = request.get_json()
        email = data.get('email')
        password = data.get('password')
        
        if not all([email, password]):
            return jsonify({'error': '缺少必要参数'}), 400
        
        # 验证用户
        user = user_manager.authenticate_user(email, password)
        if not user:
            return jsonify({'error': '邮箱或密码错误'}), 401
        
        # 创建会话
        session_token = user_manager.create_session(user.user_id)
        session['session_token'] = session_token
        
        return jsonify({
            'message': '登录成功',
            'user': {
                'id': user.user_id,
                'email': user.email,
                'username': user.username,
                'subscription_type': user.subscription_type,
                'is_premium': user.is_premium,
                'api_calls_count': user.api_calls_count,
                'monthly_api_limit': user.monthly_api_limit,
                'can_make_api_call': user.can_make_api_call
            }
        })
        
    except Exception as e:
        logger.error(f"登录失败: {e}")
        return jsonify({'error': '登录失败'}), 500

@auth_bp.route('/api/logout', methods=['POST'])
def logout():
    """用户登出"""
    try:
        session_token = session.get('session_token')
        if session_token:
            # 清理会话
            session.pop('session_token', None)
        
        return jsonify({'message': '登出成功'})
        
    except Exception as e:
        logger.error(f"登出失败: {e}")
        return jsonify({'error': '登出失败'}), 500

@auth_bp.route('/api/user/profile', methods=['GET'])
@login_required
def get_profile(user):
    """获取用户信息"""
    try:
        subscription_status = payment_service.get_subscription_status(user.user_id)
        
        return jsonify({
            'user': {
                'id': user.user_id,
                'email': user.email,
                'username': user.username,
                'subscription_type': user.subscription_type,
                'is_premium': user.is_premium,
                'subscription_end': subscription_status.get('subscription_end'),
                'api_calls_count': user.api_calls_count,
                'monthly_api_limit': user.monthly_api_limit,
                'can_make_api_call': user.can_make_api_call
            }
        })
        
    except Exception as e:
        logger.error(f"获取用户信息失败: {e}")
        return jsonify({'error': '获取用户信息失败'}), 500

@auth_bp.route('/api/user/subscription', methods=['GET'])
@login_required
def get_subscription_status(user):
    """获取用户订阅状态"""
    try:
        status = payment_service.get_subscription_status(user.user_id)
        return jsonify(status)
        
    except Exception as e:
        logger.error(f"获取订阅状态失败: {e}")
        return jsonify({'error': '获取订阅状态失败'}), 500

@auth_bp.route('/api/user/payments', methods=['GET'])
@login_required
def get_payment_history(user):
    """获取用户支付历史"""
    try:
        payments = payment_service.get_payment_history(user.user_id)
        return jsonify({'payments': payments})
        
    except Exception as e:
        logger.error(f"获取支付历史失败: {e}")
        return jsonify({'error': '获取支付历史失败'}), 500

@auth_bp.route('/api/check-auth', methods=['GET'])
def check_auth():
    """检查用户登录状态"""
    try:
        session_token = session.get('session_token')
        if not session_token:
            return jsonify({'authenticated': False})
        
        user = user_manager.validate_session(session_token)
        if not user:
            return jsonify({'authenticated': False})
        
        return jsonify({
            'authenticated': True,
            'user': {
                'id': user.user_id,
                'email': user.email,
                'username': user.username,
                'subscription_type': user.subscription_type,
                'is_premium': user.is_premium,
                'api_calls_count': user.api_calls_count,
                'monthly_api_limit': user.monthly_api_limit,
                'can_make_api_call': user.can_make_api_call
            }
        })
        
    except Exception as e:
        logger.error(f"检查认证状态失败: {e}")
        return jsonify({'authenticated': False})
