"""
支付路由 - 处理支付相关功能
"""
from flask import Blueprint, request, jsonify, session, current_app
from models.user import user_manager
from services.payment_service import payment_service
import logging

logger = logging.getLogger(__name__)

payment_bp = Blueprint('payment', __name__)

@payment_bp.route('/api/create-checkout-session', methods=['POST'])
def create_checkout_session():
    """创建Stripe结账会话"""
    try:
        session_token = session.get('session_token')
        if not session_token:
            return jsonify({'error': '未登录'}), 401
        
        user = user_manager.validate_session(session_token)
        if not user:
            return jsonify({'error': '会话无效'}), 401
        
        data = request.get_json()
        price_id = data.get('price_id')
        success_url = data.get('success_url', 'https://nnscholar.com/success')
        cancel_url = data.get('cancel_url', 'https://nnscholar.com/cancel')
        
        if not price_id:
            return jsonify({'error': '缺少价格ID'}), 400
        
        checkout_session = payment_service.create_checkout_session(
            user.user_id, price_id, success_url, cancel_url
        )
        
        if not checkout_session:
            return jsonify({'error': '创建结账会话失败'}), 500
        
        return jsonify(checkout_session)
        
    except Exception as e:
        logger.error(f"创建结账会话失败: {e}")
        return jsonify({'error': '创建结账会话失败'}), 500

@payment_bp.route('/api/create-payment-session', methods=['POST'])
def create_payment_session():
    """创建一次性支付会话"""
    try:
        session_token = session.get('session_token')
        if not session_token:
            return jsonify({'error': '未登录'}), 401
        
        user = user_manager.validate_session(session_token)
        if not user:
            return jsonify({'error': '会话无效'}), 401
        
        data = request.get_json()
        amount = data.get('amount', 9900)  # 默认$99.00
        currency = data.get('currency', 'usd')
        success_url = data.get('success_url', 'https://nnscholar.com/success')
        cancel_url = data.get('cancel_url', 'https://nnscholar.com/cancel')
        
        payment_session = payment_service.create_one_time_payment(
            user.user_id, amount, currency, success_url, cancel_url
        )
        
        if not payment_session:
            return jsonify({'error': '创建支付会话失败'}), 500
        
        return jsonify(payment_session)
        
    except Exception as e:
        logger.error(f"创建支付会话失败: {e}")
        return jsonify({'error': '创建支付会话失败'}), 500

@payment_bp.route('/api/webhook/stripe', methods=['POST'])
def stripe_webhook():
    """处理Stripe webhook"""
    try:
        payload = request.get_data()
        signature = request.headers.get('stripe-signature')
        
        if not signature:
            return jsonify({'error': '缺少签名'}), 400
        
        event = payment_service.verify_webhook(payload, signature)
        if not event:
            return jsonify({'error': '无效的webhook'}), 400
        
        # 处理不同的事件类型
        event_type = event.get('type')
        
        if event_type == 'checkout.session.completed':
            payment_service.handle_payment_success(event)
        elif event_type == 'customer.subscription.deleted':
            payment_service.handle_subscription_cancelled(event)
        
        return jsonify({'received': True})
        
    except Exception as e:
        logger.error(f"处理webhook失败: {e}")
        return jsonify({'error': '处理webhook失败'}), 500

@payment_bp.route('/api/pricing', methods=['GET'])
def get_pricing():
    """获取定价信息"""
    try:
        pricing = {
            'plans': [
                {
                    'name': '免费版',
                    'price': 0,
                    'currency': 'USD',
                    'features': [
                        '每月100次API调用',
                        '基础文献检索',
                        '标准分析功能',
                        '社区支持'
                    ],
                    'popular': False
                },
                {
                    'name': '专业版',
                    'price': 99,
                    'currency': 'USD',
                    'features': [
                        '无限API调用',
                        '高级文献检索',
                        'AI智能推荐',
                        '深度分析工具',
                        '优先支持',
                        '无限制导出'
                    ],
                    'popular': True
                }
            ],
            'stripe_publishable_key': payment_service.stripe_publishable_key
        }
        
        return jsonify(pricing)
        
    except Exception as e:
        logger.error(f"获取定价信息失败: {e}")
        return jsonify({'error': '获取定价信息失败'}), 500
