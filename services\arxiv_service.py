"""arXiv API service for literature search."""

import requests
import xml.etree.ElementTree as ET
from typing import List, Dict, Any, Optional, Tuple
import time
import logging
from concurrent.futures import ThreadPoolExecutor, as_completed
from urllib.parse import quote, urlencode
import re
from datetime import datetime

from config.api_config import api_config
from models.paper import Paper, PaperDetail
from utils.text_processing import preprocess_text, extract_keywords
from utils.cache_utils import cache_result, RateLimiter

logger = logging.getLogger(__name__)


class ArxivService:
    """Service for interacting with arXiv API."""
    
    def __init__(self):
        self.base_url = "http://export.arxiv.org/api/query"
        self.rate_limiter = RateLimiter(
            max_requests=30,  # arXiv recommends max 3 requests per second
            window_seconds=10
        )
        self.session = requests.Session()
        
        # Set default headers
        self.session.headers.update({
            'User-Agent': 'NNScholar/1.0 (https://github.com/nnscholar)',
        })
    
    def search_papers(self, query: str, max_results: int = 100, 
                     start: int = 0, sort_by: str = 'relevance') -> Tuple[List[Dict], int]:
        """Search arXiv for papers.
        
        Args:
            query: Search query
            max_results: Maximum number of results
            start: Starting position for results
            sort_by: Sort order ('relevance', 'lastUpdatedDate', 'submittedDate')
        
        Returns:
            Tuple of (list of paper data, total count)
        """
        try:
            # Wait for rate limiting
            self.rate_limiter.wait_if_needed()
            
            # Prepare search parameters
            params = {
                'search_query': self._format_query(query),
                'start': start,
                'max_results': min(max_results, 2000),  # arXiv API limit
                'sortBy': sort_by,
                'sortOrder': 'descending'
            }
            
            logger.info(f"Searching arXiv with query: {query}")
            
            # Make API request
            response = self.session.get(self.base_url, params=params, timeout=30)
            response.raise_for_status()
            
            # Parse XML response
            papers, total_count = self._parse_search_response(response.text)
            
            logger.info(f"Found {len(papers)} papers from arXiv")
            return papers, total_count
            
        except Exception as e:
            logger.error(f"Error searching arXiv: {str(e)}")
            return [], 0
    
    def _format_query(self, query: str) -> str:
        """Format query for arXiv API."""
        # Clean and format the query
        query = query.strip()
        
        # If it's a simple query, search in title, abstract, and comments
        if not any(field in query.lower() for field in ['ti:', 'abs:', 'au:', 'cat:']):
            # Search in title, abstract, and comments
            formatted_query = f'all:"{query}"'
        else:
            formatted_query = query
            
        return formatted_query
    
    def _parse_search_response(self, xml_content: str) -> Tuple[List[Dict], int]:
        """Parse arXiv search response XML."""
        try:
            root = ET.fromstring(xml_content)
            
            # Define namespace
            ns = {'atom': 'http://www.w3.org/2005/Atom',
                  'arxiv': 'http://arxiv.org/schemas/atom'}
            
            # Get total results
            total_results = root.find('.//atom:totalResults', ns)
            total_count = int(total_results.text) if total_results is not None else 0
            
            papers = []
            entries = root.findall('.//atom:entry', ns)
            
            for entry in entries:
                try:
                    paper_data = self._parse_entry(entry, ns)
                    if paper_data:
                        papers.append(paper_data)
                except Exception as e:
                    logger.warning(f"Error parsing arXiv entry: {str(e)}")
                    continue
            
            return papers, total_count
            
        except Exception as e:
            logger.error(f"Error parsing arXiv response: {str(e)}")
            return [], 0
    
    def _parse_entry(self, entry, ns) -> Optional[Dict]:
        """Parse a single arXiv entry."""
        try:
            # Extract basic information
            arxiv_id = entry.find('atom:id', ns).text.split('/')[-1] if entry.find('atom:id', ns) is not None else ""
            title = entry.find('atom:title', ns).text.strip() if entry.find('atom:title', ns) is not None else ""
            summary = entry.find('atom:summary', ns).text.strip() if entry.find('atom:summary', ns) is not None else ""
            
            # Extract authors
            authors = []
            author_elements = entry.findall('atom:author', ns)
            for author_elem in author_elements:
                name_elem = author_elem.find('atom:name', ns)
                if name_elem is not None:
                    authors.append(name_elem.text.strip())
            
            # Extract publication date
            published = entry.find('atom:published', ns)
            pub_date = ""
            year = ""
            if published is not None:
                try:
                    pub_datetime = datetime.fromisoformat(published.text.replace('Z', '+00:00'))
                    pub_date = pub_datetime.strftime('%Y-%m-%d')
                    year = str(pub_datetime.year)
                except:
                    pub_date = published.text[:10]  # Take first 10 chars as date
                    year = published.text[:4]
            
            # Extract categories
            categories = []
            category_elements = entry.findall('arxiv:primary_category', ns)
            for cat_elem in category_elements:
                term = cat_elem.get('term')
                if term:
                    categories.append(term)
            
            # Additional categories
            for cat_elem in entry.findall('atom:category', ns):
                term = cat_elem.get('term')
                if term and term not in categories:
                    categories.append(term)
            
            # Extract DOI if available
            doi = ""
            doi_elem = entry.find('arxiv:doi', ns)
            if doi_elem is not None:
                doi = doi_elem.text.strip()
            
            # Extract PDF link
            pdf_url = ""
            links = entry.findall('atom:link', ns)
            for link in links:
                if link.get('type') == 'application/pdf':
                    pdf_url = link.get('href', '')
                    break
            
            # Create paper data structure
            paper_data = {
                'id': arxiv_id,
                'arxiv_id': arxiv_id,
                'title': title,
                'abstract': summary,
                'authors': authors,
                'author_list': ', '.join(authors),
                'publication_date': pub_date,
                'year': year,
                'journal': 'arXiv',
                'categories': categories,
                'category_list': ', '.join(categories),
                'doi': doi,
                'pdf_url': pdf_url,
                'url': f"https://arxiv.org/abs/{arxiv_id}",
                'source': 'arxiv',
                'citation_count': 0,  # arXiv doesn't provide citation counts
                'impact_factor': 'N/A',
                'jcr_quartile': 'N/A',
                'cas_quartile': 'N/A'
            }
            
            return paper_data
            
        except Exception as e:
            logger.error(f"Error parsing arXiv entry: {str(e)}")
            return None
    
    def get_paper_details(self, arxiv_id: str) -> Optional[Dict]:
        """Get detailed information for a specific arXiv paper."""
        try:
            # Wait for rate limiting
            self.rate_limiter.wait_if_needed()
            
            params = {
                'id_list': arxiv_id,
                'max_results': 1
            }
            
            response = self.session.get(self.base_url, params=params, timeout=30)
            response.raise_for_status()
            
            papers, _ = self._parse_search_response(response.text)
            return papers[0] if papers else None
            
        except Exception as e:
            logger.error(f"Error getting arXiv paper details for {arxiv_id}: {str(e)}")
            return None


# Create global instance
arxiv_service = ArxivService()
