#!/usr/bin/env python3
"""
高速文献引用网络服务

基于Connected Papers风格的文献网络图谱功能：
1. 输入DOI/PMID/标题解析文献
2. 高速获取引用文献和被引文献（10秒内完成）
3. 生成带侧边栏的交互式网络图谱
4. 支持节点点击和文献详情展示

优化策略：
- 并发请求多个API
- 批量处理PMID
- 智能缓存和连接复用
- 最小化API调用
"""

import logging
import re
import json
import requests
import time
import xml.etree.ElementTree as ET
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed
from dataclasses import dataclass

from services.embedding_service import embedding_service
from services.journal_service import JournalService

logger = logging.getLogger(__name__)

@dataclass
class PaperInfo:
    """文献信息数据类"""
    title: str = ""
    authors: List[str] = None
    year: Optional[int] = None
    journal: str = ""
    doi: str = ""
    pmid: str = ""
    citation_count: int = 0
    abstract: str = ""
    impact_factor: float = 0.0
    
    def __post_init__(self):
        if self.authors is None:
            self.authors = []

class FastCitationNetworkService:
    """高速文献引用网络服务"""
    
    def __init__(self):
        self.timeout = 8  # 减少超时时间
        self.max_workers = 3  # 减少并发数，避免API限制
        self.max_nodes = 50  # 每种类型的最大节点数量
        self.session = requests.Session()  # 复用连接
        self.session.headers.update({
            'User-Agent': 'NNScholar/1.0 (<EMAIL>)'
        })
        self.journal_service = JournalService()  # 期刊信息服务
        
    def parse_input(self, user_input: str) -> str:
        """解析用户输入，提取DOI"""
        user_input = user_input.strip()
        
        # 检查是否为DOI
        doi_pattern = r'10\.\d{4,}/[^\s]+'
        doi_match = re.search(doi_pattern, user_input)
        if doi_match:
            return doi_match.group()
        
        # 检查是否为PMID
        if user_input.isdigit():
            # 通过PMID获取DOI
            return self.pmid_to_doi(user_input)
        
        # 如果是标题，尝试搜索获取DOI
        return self.search_doi_by_title(user_input)
    
    def pmid_to_doi(self, pmid: str) -> Optional[str]:
        """通过PMID获取DOI"""
        try:
            url = "https://eutils.ncbi.nlm.nih.gov/entrez/eutils/efetch.fcgi"
            params = {
                "db": "pubmed",
                "id": pmid,
                "retmode": "xml",
                "tool": "nnscholar",
                "email": "<EMAIL>"
            }
            
            response = self.session.get(url, params=params, timeout=self.timeout)
            if response.status_code == 200:
                root = ET.fromstring(response.text)
                doi_elem = root.find('.//ArticleId[@IdType="doi"]')
                if doi_elem is not None:
                    return doi_elem.text
        except Exception as e:
            logger.warning(f"PMID转DOI失败: {e}")
        return None
    
    def search_doi_by_title(self, title: str) -> Optional[str]:
        """通过标题搜索DOI"""
        try:
            # 使用Crossref搜索
            url = "https://api.crossref.org/works"
            params = {
                "query": title,
                "rows": 1
            }
            
            response = self.session.get(url, params=params, timeout=self.timeout)
            if response.status_code == 200:
                data = response.json()
                items = data.get("message", {}).get("items", [])
                if items:
                    return items[0].get("DOI")
        except Exception as e:
            logger.warning(f"标题搜索DOI失败: {e}")
        return None
    
    def make_request(self, url: str, params: Dict = None) -> Optional[Dict]:
        """发送HTTP请求"""
        try:
            response = self.session.get(url, params=params, timeout=self.timeout)
            if response.status_code == 200:
                return response.json()
            return None
        except Exception:
            return None
    
    def get_semantic_scholar_fast(self, doi: str) -> Dict[str, Any]:
        """快速获取Semantic Scholar数据（只要核心字段）"""
        logger.info("🚀 步骤1: 快速获取引用数据...")
        
        # 请求完整的必要字段
        fields = [
            "title", "authors", "year", "journal", "citationCount", "abstract",
            "citations.title", "citations.authors", "citations.year", "citations.journal", 
            "citations.citationCount", "citations.externalIds",
            "references.externalIds"  # 参考文献只要PMID，详细信息通过PubMed获取
        ]
        
        url = f"https://api.semanticscholar.org/graph/v1/paper/DOI:{doi}"
        params = {"fields": ",".join(fields)}
        
        data = self.make_request(url, params)
        if not data:
            return {"paper_info": None, "citations": [], "reference_pmids": []}
        
        # 解析基本信息
        paper_info = PaperInfo(
            title=data.get("title", ""),
            authors=[author.get("name", "") for author in data.get("authors", []) if author.get("name")],
            year=data.get("year"),
            journal=data.get("journal", {}).get("name", "") if data.get("journal") else "",
            doi=doi,
            citation_count=data.get("citationCount", 0),
            abstract=data.get("abstract", "")
        )
        
        # 快速解析被引文献（只提取核心信息）
        citations = []
        raw_citations = data.get("citations") or []
        
        for cit in raw_citations[:self.max_nodes]:  # 限制数量
            citation_info = PaperInfo(
                title=cit.get("title", ""),
                authors=[author.get("name", "") for author in cit.get("authors", []) if author.get("name")],
                year=cit.get("year"),
                journal=cit.get("journal", {}).get("name", "") if cit.get("journal") else "",
                citation_count=cit.get("citationCount", 0)
            )

            # 提取DOI和PMID
            if "externalIds" in cit and cit["externalIds"]:
                external_ids = cit["externalIds"]
                citation_info.pmid = external_ids.get("PubMed", "")
                citation_info.doi = external_ids.get("DOI", "")

            citations.append(citation_info)
        
        # 提取参考文献的PMID
        reference_pmids = []
        raw_references = data.get("references") or []
        for ref in raw_references:
            if "externalIds" in ref and ref["externalIds"]:
                pmid = ref["externalIds"].get("PubMed", "")
                if pmid:
                    reference_pmids.append(pmid)
        
        logger.info(f"   ✅ 获取到 {len(citations)} 篇被引文献, {len(reference_pmids)} 个参考文献PMID")
        return {
            "paper_info": paper_info, 
            "citations": citations, 
            "reference_pmids": reference_pmids
        }
    
    def get_crossref_reference_dois(self, doi: str) -> List[str]:
        """快速获取Crossref参考文献DOI"""
        logger.info("📚 步骤2: 获取参考文献DOI...")
        
        url = f"https://api.crossref.org/works/{doi}"
        data = self.make_request(url)
        
        if not data or "message" not in data:
            return []
        
        work = data["message"]
        raw_references = work.get("reference", [])
        
        reference_dois = []
        for ref in raw_references:
            ref_doi = ref.get("DOI", "")
            if ref_doi:
                reference_dois.append(ref_doi)
        
        logger.info(f"   ✅ 获取到 {len(reference_dois)} 个参考文献DOI")
        return reference_dois

    def convert_dois_to_pmids(self, dois: List[str]) -> List[str]:
        """将DOI批量转换为PMID"""
        if not dois:
            return []

        logger.info(f"   🔄 转换 {len(dois)} 个DOI为PMID...")
        pmids = []

        # 批量搜索，每次最多10个DOI
        batch_size = 10
        for i in range(0, len(dois), batch_size):
            batch_dois = dois[i:i + batch_size]

            # 构建搜索查询
            search_terms = " OR ".join([f"{doi}[DOI]" for doi in batch_dois])

            url = "https://eutils.ncbi.nlm.nih.gov/entrez/eutils/esearch.fcgi"
            params = {
                "db": "pubmed",
                "term": search_terms,
                "retmode": "json",
                "retmax": len(batch_dois),
                "tool": "nnscholar",
                "email": "<EMAIL>"
            }

            try:
                response = self.session.get(url, params=params, timeout=5)
                if response.status_code == 200:
                    data = response.json()
                    batch_pmids = data.get("esearchresult", {}).get("idlist", [])
                    pmids.extend(batch_pmids)
                    logger.info(f"      ✅ 批次 {i//batch_size + 1}: 找到 {len(batch_pmids)} 个PMID")
                else:
                    logger.warning(f"      ❌ 批次 {i//batch_size + 1}: 搜索失败")
            except Exception as e:
                logger.warning(f"      ❌ 批次 {i//batch_size + 1}: {str(e)[:30]}")

        logger.info(f"   📊 总计转换得到 {len(pmids)} 个PMID")
        return pmids

    def batch_get_pubmed_abstracts(self, pmids: List[str]) -> Dict[str, Dict[str, str]]:
        """批量获取PubMed摘要"""
        if not pmids:
            return {}

        logger.info(f"📖 步骤3: 批量获取 {len(pmids)} 个PMID的摘要...")

        # 批量请求，最多一次50个PMID
        batch_size = 50
        all_abstracts = {}

        for i in range(0, len(pmids), batch_size):
            batch_pmids = pmids[i:i + batch_size]
            pmid_str = ",".join(batch_pmids)

            url = "https://eutils.ncbi.nlm.nih.gov/entrez/eutils/efetch.fcgi"
            params = {
                "db": "pubmed",
                "id": pmid_str,
                "retmode": "xml",
                "rettype": "abstract",
                "tool": "nnscholar",
                "email": "<EMAIL>"
            }

            try:
                response = self.session.get(url, params=params, timeout=8)
                if response.status_code == 200:
                    papers_info = self.parse_pubmed_xml(response.text)
                    all_abstracts.update(papers_info)
                    logger.info(f"   ✅ 批次 {i//batch_size + 1}: 获取到 {len(papers_info)} 篇文献信息")
                else:
                    logger.warning(f"   ❌ 批次 {i//batch_size + 1}: HTTP {response.status_code}")
            except Exception as e:
                logger.warning(f"   ❌ 批次 {i//batch_size + 1}: {str(e)[:50]}")

        logger.info(f"   📊 总计获取到 {len(all_abstracts)} 个摘要")
        return all_abstracts

    def parse_pubmed_xml(self, xml_content: str) -> Dict[str, Dict[str, str]]:
        """解析PubMed XML响应，提取完整信息"""
        papers_info = {}

        try:
            root = ET.fromstring(xml_content)

            for article in root.findall('.//PubmedArticle'):
                # 获取PMID
                pmid_elem = article.find('.//PMID')
                if pmid_elem is not None:
                    pmid = pmid_elem.text

                    # 获取标题
                    title_elem = article.find('.//ArticleTitle')
                    title = title_elem.text if title_elem is not None else ""

                    # 获取摘要
                    abstract_elem = article.find('.//Abstract/AbstractText')
                    abstract = abstract_elem.text if abstract_elem is not None else ""

                    # 获取期刊
                    journal_elem = article.find('.//Journal/Title')
                    journal = journal_elem.text if journal_elem is not None else ""

                    # 获取年份
                    year_elem = article.find('.//PubDate/Year')
                    year = int(year_elem.text) if year_elem is not None and year_elem.text else None

                    # 获取作者
                    authors = []
                    for author_elem in article.findall('.//Author'):
                        lastname = author_elem.find('LastName')
                        forename = author_elem.find('ForeName')
                        if lastname is not None:
                            author_name = lastname.text
                            if forename is not None:
                                author_name = f"{forename.text} {author_name}"
                            authors.append(author_name)

                    papers_info[pmid] = {
                        "title": title,
                        "abstract": abstract,
                        "journal": journal,
                        "year": year,
                        "authors": authors
                    }

        except ET.ParseError:
            pass

        return papers_info

    def get_crossref_basic_info(self, doi: str) -> Dict[str, Any]:
        """从Crossref获取DOI的基础信息（标题、作者、期刊、年份）"""
        try:
            url = f"https://api.crossref.org/works/{doi}"
            response = self.session.get(url, timeout=self.timeout)

            if response.status_code == 200:
                data = response.json()
                work = data.get('message', {})

                # 提取标题
                title = ''
                if 'title' in work and work['title']:
                    title = work['title'][0]

                # 提取作者
                authors = []
                if 'author' in work:
                    for author in work['author']:
                        given = author.get('given', '')
                        family = author.get('family', '')
                        if given and family:
                            authors.append(f"{given} {family}")
                        elif family:
                            authors.append(family)

                # 提取期刊
                journal = ''
                if 'container-title' in work and work['container-title']:
                    journal = work['container-title'][0]

                # 提取年份
                year = None
                if 'published-print' in work:
                    date_parts = work['published-print'].get('date-parts', [])
                    if date_parts and date_parts[0]:
                        year = date_parts[0][0]
                elif 'published-online' in work:
                    date_parts = work['published-online'].get('date-parts', [])
                    if date_parts and date_parts[0]:
                        year = date_parts[0][0]

                return {
                    'title': title,
                    'authors': authors,
                    'journal': journal,
                    'year': year
                }
        except:
            pass

        return {}

    def fetch_basic_citation_data(self, doi: str) -> Dict[str, Any]:
        """快速获取基础引用数据（不包含摘要，提高速度）"""
        logger.info(f"⚡ 开始快速获取DOI {doi} 的基础引用数据...")

        start_time = time.time()

        # 并发执行多个任务
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交任务
            semantic_future = executor.submit(self.get_semantic_scholar_fast, doi)
            crossref_future = executor.submit(self.get_crossref_reference_dois, doi)

            # 获取结果
            semantic_result = semantic_future.result()
            reference_dois = crossref_future.result()

        # 收集所有PMID用于后续的相关度计算
        all_pmids = []

        # 被引文献的PMID
        citation_pmids = [cit.pmid for cit in semantic_result["citations"] if cit.pmid]
        all_pmids.extend(citation_pmids)

        # 参考文献的PMID（从Semantic Scholar）
        reference_pmids = semantic_result["reference_pmids"]
        all_pmids.extend(reference_pmids)

        # 如果参考文献PMID不足，尝试通过DOI转换为PMID
        if len(reference_pmids) < 10 and reference_dois:
            logger.info(f"   🔄 参考文献PMID不足({len(reference_pmids)})，尝试DOI转PMID...")
            additional_pmids = self.convert_dois_to_pmids(reference_dois[:10])
            all_pmids.extend(additional_pmids)
            reference_pmids.extend(additional_pmids)

        # 创建参考文献对象（优先使用DOI，达到max_nodes限制）
        references = []

        # 首先使用有PMID的参考文献
        for pmid in reference_pmids[:self.max_nodes]:
            ref_info = PaperInfo(
                title=f"Reference paper {len(references) + 1}",  # 临时标题，后续会更新
                authors=[],
                year=None,
                journal="",
                doi="",
                pmid=pmid,
                abstract=""  # 暂时为空，后续根据相关度获取
            )
            references.append(ref_info)

        # 如果参考文献数量不足max_nodes，用DOI补充
        remaining_slots = self.max_nodes - len(references)
        if remaining_slots > 0 and reference_dois:
            logger.info(f"   📚 参考文献不足，用DOI补充 {remaining_slots} 篇")

            # 跳过已经有PMID的DOI
            used_dois = set()
            for i, doi_ref in enumerate(reference_dois[:len(references)]):
                if i < len(references):
                    references[i].doi = doi_ref
                    used_dois.add(doi_ref)

            # 添加额外的DOI参考文献，尝试获取基础信息
            for doi_ref in reference_dois:
                if len(references) >= self.max_nodes:
                    break
                if doi_ref not in used_dois:
                    # 尝试从Crossref获取基础信息
                    try:
                        crossref_info = self.get_crossref_basic_info(doi_ref)
                        ref_info = PaperInfo(
                            title=crossref_info.get('title', f"Reference paper {len(references) + 1}"),
                            authors=crossref_info.get('authors', []),
                            year=crossref_info.get('year'),
                            journal=crossref_info.get('journal', ''),
                            doi=doi_ref,
                            pmid="",  # 没有PMID
                            abstract=""
                        )
                    except:
                        # 如果获取失败，使用默认信息
                        ref_info = PaperInfo(
                            title=f"Reference paper {len(references) + 1}",
                            authors=[],
                            year=None,
                            journal="",
                            doi=doi_ref,
                            pmid="",
                            abstract=""
                        )
                    references.append(ref_info)
        else:
            # 为现有参考文献补充DOI
            for i, doi_ref in enumerate(reference_dois[:len(references)]):
                if i < len(references):
                    references[i].doi = doi_ref

        # 整理结果
        result = {
            "success": True,
            "paper_info": semantic_result["paper_info"],
            "references": references,
            "citations": semantic_result["citations"][:self.max_nodes],  # 限制数量
            "statistics": {
                "total_references": len(references),
                "total_citations": len(semantic_result["citations"][:self.max_nodes]),
                "processing_time": round(time.time() - start_time, 2)
            }
        }

        logger.info("⚡ 基础数据获取完成!")
        logger.info(f"   📄 文献标题: {result['paper_info'].title if result['paper_info'] else 'N/A'}")
        logger.info(f"   📥 参考文献: {result['statistics']['total_references']} 篇")
        logger.info(f"   📤 被引文献: {result['statistics']['total_citations']} 篇")
        logger.info(f"   ⚡ 处理时间: {result['statistics']['processing_time']} 秒")

        return result

    def fetch_fast_citation_data(self, doi: str) -> Dict[str, Any]:
        """快速获取完整引用数据（10秒内）"""
        logger.info(f"⚡ 开始快速获取DOI {doi} 的引用数据...")

        start_time = time.time()

        # 并发执行多个任务
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交任务
            semantic_future = executor.submit(self.get_semantic_scholar_fast, doi)
            crossref_future = executor.submit(self.get_crossref_reference_dois, doi)

            # 获取结果
            semantic_result = semantic_future.result()
            reference_dois = crossref_future.result()

        # 收集所有PMID
        all_pmids = []

        # 被引文献的PMID
        citation_pmids = [cit.pmid for cit in semantic_result["citations"] if cit.pmid]
        all_pmids.extend(citation_pmids)

        # 参考文献的PMID（从Semantic Scholar）
        reference_pmids = semantic_result["reference_pmids"]
        all_pmids.extend(reference_pmids)

        # 如果参考文献PMID不足，尝试通过DOI转换为PMID
        if len(reference_pmids) < 10 and reference_dois:
            logger.info(f"   🔄 参考文献PMID不足({len(reference_pmids)})，尝试DOI转PMID...")
            additional_pmids = self.convert_dois_to_pmids(reference_dois[:10])
            all_pmids.extend(additional_pmids)
            reference_pmids.extend(additional_pmids)

        # 去重
        unique_pmids = list(set(all_pmids))

        # 批量获取完整信息
        pubmed_info = self.batch_get_pubmed_abstracts(unique_pmids)

        # 为被引文献添加摘要和补充信息
        for cit in semantic_result["citations"]:
            if cit.pmid and cit.pmid in pubmed_info:
                pmid_data = pubmed_info[cit.pmid]
                cit.abstract = pmid_data.get("abstract", "")
                if not cit.journal and pmid_data.get("journal"):
                    cit.journal = pmid_data["journal"]
                if not cit.authors and pmid_data.get("authors"):
                    cit.authors = pmid_data["authors"]

        # 创建参考文献对象（使用PubMed完整信息）
        references = []
        for pmid in reference_pmids:
            if pmid in pubmed_info:
                pmid_data = pubmed_info[pmid]
                ref_info = PaperInfo(
                    title=pmid_data.get("title", ""),
                    authors=pmid_data.get("authors", []),
                    year=pmid_data.get("year"),
                    journal=pmid_data.get("journal", ""),
                    pmid=pmid,
                    abstract=pmid_data.get("abstract", "")
                )
                references.append(ref_info)

        # 补充参考文献的DOI
        for i, doi_ref in enumerate(reference_dois[:len(references)]):
            if i < len(references):
                references[i].doi = doi_ref

        # 整理结果
        result = {
            "success": True,
            "paper_info": semantic_result["paper_info"],
            "references": references,
            "citations": semantic_result["citations"],
            "statistics": {
                "total_references": len(references),
                "total_citations": len(semantic_result["citations"]),
                "total_pubmed_info": len(pubmed_info),
                "processing_time": round(time.time() - start_time, 2)
            }
        }

        logger.info("⚡ 快速数据获取完成!")
        logger.info(f"   📄 文献标题: {result['paper_info'].title if result['paper_info'] else 'N/A'}")
        logger.info(f"   📥 参考文献: {result['statistics']['total_references']} 篇")
        logger.info(f"   📤 被引文献: {result['statistics']['total_citations']} 篇")
        logger.info(f"   ⚡ 处理时间: {result['statistics']['processing_time']} 秒")

        return result

    def build_citation_network(self, user_input: str) -> Dict[str, Any]:
        """构建文献引用网络图谱（高速版本）"""
        try:
            logger.info(f"🌐 开始构建高速文献网络图谱: {user_input}")
            start_time = time.time()

            # 解析输入获取DOI
            doi = self.parse_input(user_input)
            if not doi:
                raise ValueError(f"无法解析输入: {user_input}")

            logger.info(f"   📄 解析得到DOI: {doi}")

            # 快速获取基础引用数据（不包含摘要）
            citation_data = self.fetch_basic_citation_data(doi)

            if not citation_data["success"]:
                raise ValueError("获取引用数据失败")

            # 构建基础网络节点和边
            nodes_data = []
            edges_data = []

            # 中心节点
            center_paper = citation_data["paper_info"]
            if center_paper:
                # 获取中心文献的期刊指标
                journal_metrics = None
                if center_paper.journal:
                    journal_metrics = self.journal_service.get_journal_metrics_by_name(center_paper.journal)

                center_node = {
                    'id': doi,
                    'label': self.create_author_label(center_paper.authors, center_paper.year),
                    'color': '#ff6b6b',  # 红色 - 中心文献
                    'size': 25,
                    'node_type': 'center',
                    'citation_count': center_paper.citation_count,
                    'impact_factor': journal_metrics.impact_factor if journal_metrics else center_paper.impact_factor,
                    'jcr_quartile': journal_metrics.jcr_quartile if journal_metrics else 'N/A',
                    'cas_quartile': journal_metrics.cas_quartile if journal_metrics else 'N/A',
                    'journal': center_paper.journal,
                    'year': center_paper.year,
                    'full_title': center_paper.title,
                    'authors': center_paper.authors,
                    'abstract': center_paper.abstract,
                    'doi': center_paper.doi,
                    'pmid': center_paper.pmid
                }
                nodes_data.append(center_node)

            # 参考文献节点（基础信息）
            for ref in citation_data["references"]:
                ref_id = ref.doi or ref.pmid or f"ref_{len(nodes_data)}"
                ref_node = {
                    'id': ref_id,
                    'label': self.create_author_label(ref.authors, ref.year),
                    'color': '#4ecdc4',  # 青色 - 引用文献
                    'size': 15,
                    'node_type': 'reference',
                    'citation_count': ref.citation_count,
                    'impact_factor': ref.impact_factor,
                    'journal': ref.journal,
                    'year': ref.year,
                    'full_title': ref.title,
                    'authors': ref.authors,
                    'abstract': "",  # 暂时为空，后续根据相关度填充
                    'doi': ref.doi,
                    'pmid': ref.pmid
                }
                nodes_data.append(ref_node)
                edges_data.append({'from': ref_id, 'to': doi})

            # 被引文献节点（基础信息）
            for cit in citation_data["citations"]:
                cit_id = cit.doi or cit.pmid or f"cit_{len(nodes_data)}"
                cit_node = {
                    'id': cit_id,
                    'label': self.create_author_label(cit.authors, cit.year),
                    'color': '#45b7d1',  # 蓝色 - 被引文献
                    'size': 15,
                    'node_type': 'citation',
                    'citation_count': cit.citation_count,
                    'impact_factor': cit.impact_factor,
                    'journal': cit.journal,
                    'year': cit.year,
                    'full_title': cit.title,
                    'authors': cit.authors,
                    'abstract': "",  # 暂时为空，后续根据相关度填充
                    'doi': cit.doi,
                    'pmid': cit.pmid
                }
                nodes_data.append(cit_node)
                edges_data.append({'from': doi, 'to': cit_id})

            # 计算相关度分数
            if center_paper and len(nodes_data) > 1:
                logger.info("🧠 开始计算文献相关度...")
                self.calculate_similarity_scores(center_paper, nodes_data)

                # 只为相关度≥53%的文献获取摘要
                logger.info("📖 开始获取高相关度文献的摘要...")
                self.fetch_abstracts_for_relevant_papers(nodes_data)

            # 计算处理时间
            processing_time = time.time() - start_time

            # 构建返回结果
            result = {
                'nodes': nodes_data,
                'edges': edges_data,
                'statistics': {
                    'total_nodes': len(nodes_data),
                    'total_edges': len(edges_data),
                    'processing_time': round(processing_time, 2)
                }
            }

            logger.info(f"🎉 高速网络图谱构建成功!")
            logger.info(f"   📊 总节点数: {len(nodes_data)} (中心:1, 参考:{len(citation_data['references'])}, 被引:{len(citation_data['citations'])})")
            logger.info(f"   🔗 总边数: {len(edges_data)}")
            logger.info(f"   ⏱️ 处理时间: {processing_time:.2f}秒")

            # 调试信息：显示参考文献的标题情况
            ref_with_titles = sum(1 for node in nodes_data if node['node_type'] == 'reference' and node.get('full_title'))
            cit_with_titles = sum(1 for node in nodes_data if node['node_type'] == 'citation' and node.get('full_title'))
            logger.info(f"   📝 有标题的参考文献: {ref_with_titles}/{len(citation_data['references'])}")
            logger.info(f"   📝 有标题的被引文献: {cit_with_titles}/{len(citation_data['citations'])}")

            return result

        except Exception as e:
            logger.error(f"构建高速网络图谱失败: {e}")
            raise e

    def create_author_label(self, authors: List[str], year: Optional[int]) -> str:
        """创建简洁的作者标签：作者 + 年份"""
        if authors and len(authors) > 0 and authors[0]:
            # 提取第一作者的姓氏
            first_author = authors[0]
            if ' ' in first_author:
                # 假设格式是 "名 姓"
                parts = first_author.split()
                last_name = parts[-1]
            else:
                last_name = first_author

            # 如果有多个作者，添加 "et al."
            if len(authors) > 1:
                author_part = f"{last_name} et al."
            else:
                author_part = last_name
        else:
            author_part = "Unknown"

        # 添加年份
        if year:
            return f"{author_part} ({year})"
        else:
            return author_part

    def calculate_similarity_scores(self, center_paper: PaperInfo, nodes_data: List[Dict]) -> None:
        """计算相关度分数"""
        try:
            # 准备中心文献标题
            center_title = center_paper.title
            if not center_title:
                logger.warning("中心文献标题为空，跳过相关度计算")
                return

            # 收集候选文献标题
            candidate_papers = []
            paper_indices = {}

            for i, node in enumerate(nodes_data):
                if node['node_type'] != 'center':
                    title = node.get('full_title', '') or node.get('title', '')
                    if title and len(title.strip()) > 5:  # 降低标题长度要求
                        candidate_papers.append({'title': title})
                        paper_indices[len(candidate_papers) - 1] = i
                    else:
                        logger.debug(f"   ⚠️ 跳过无效标题的节点: {node['node_type']}, title='{title}'")

            if not candidate_papers:
                logger.warning("没有有效的候选文献，跳过相关度计算")
                return

            logger.info(f"   📝 收集到 {len(candidate_papers)} 个有效文献")

            # 使用嵌入服务批量计算相关度
            paper_titles = [paper['title'] for paper in candidate_papers]
            similarities = embedding_service.calculate_relevance_scores(center_title, paper_titles)

            # 将相关度分数添加到节点数据中
            for paper_idx, similarity in enumerate(similarities):
                if paper_idx in paper_indices:
                    node_idx = paper_indices[paper_idx]
                    nodes_data[node_idx]['similarity_score'] = similarity

            logger.info(f"   📊 相关度计算完成: {len(similarities)} 个分数")

        except Exception as e:
            logger.error(f"计算相关度失败: {e}")
            # 为所有节点设置默认相关度
            for node in nodes_data:
                if node['node_type'] != 'center':
                    node['similarity_score'] = 0.5

    def fetch_abstracts_for_relevant_papers(self, nodes_data: List[Dict]) -> None:
        """按不同阈值获取摘要：祖先文献≥40%，后代文献≥53%"""
        try:
            # 筛选出高相关度的文献，分别处理引用文献和被引文献
            relevant_references = [
                node for node in nodes_data
                if node['node_type'] == 'reference' and
                node.get('similarity_score', 0) >= 0.4  # 祖先文献≥40%
            ]

            relevant_citations = [
                node for node in nodes_data
                if node['node_type'] == 'citation' and
                node.get('similarity_score', 0) >= 0.53  # 后代文献≥53%
            ]

            total_relevant = len(relevant_references) + len(relevant_citations)

            if total_relevant == 0:
                logger.info("   📊 没有相关度≥53%的文献需要获取摘要")
                return

            logger.info(f"   📊 找到 {total_relevant} 篇相关度≥53%的文献（引用:{len(relevant_references)}, 被引:{len(relevant_citations)}），开始获取摘要...")

            # 收集所有相关文献的PMID和DOI
            all_relevant_nodes = relevant_references + relevant_citations

            # 分离有PMID和只有DOI的节点
            nodes_with_pmid = [node for node in all_relevant_nodes if node.get('pmid')]
            nodes_with_only_doi = [node for node in all_relevant_nodes if not node.get('pmid') and node.get('doi')]

            pmids = [node['pmid'] for node in nodes_with_pmid]

            # 尝试将DOI转换为PMID
            if nodes_with_only_doi:
                logger.info(f"   🔄 尝试将 {len(nodes_with_only_doi)} 个DOI转换为PMID...")
                dois_to_convert = [node['doi'] for node in nodes_with_only_doi]
                additional_pmids = self.convert_dois_to_pmids(dois_to_convert)

                # 更新节点的PMID
                for i, pmid in enumerate(additional_pmids):
                    if i < len(nodes_with_only_doi) and pmid:
                        nodes_with_only_doi[i]['pmid'] = pmid
                        pmids.append(pmid)
                        nodes_with_pmid.append(nodes_with_only_doi[i])

            if not pmids:
                logger.info("   📊 没有有效的PMID，跳过摘要获取")
                return

            # 批量获取摘要和完整信息
            pubmed_info = self.batch_get_pubmed_abstracts(pmids)

            # 更新节点信息（只处理有PMID的节点）
            updated_count = 0
            for node in nodes_with_pmid:
                pmid = node.get('pmid')
                if pmid and pmid in pubmed_info:
                    pmid_data = pubmed_info[pmid]
                    node['abstract'] = pmid_data.get('abstract', '')

                    # 补充完整信息
                    if pmid_data.get('title'):
                        node['full_title'] = pmid_data['title']
                    if pmid_data.get('journal'):
                        node['journal'] = pmid_data['journal']
                        # 获取期刊指标信息
                        journal_metrics = self.journal_service.get_journal_metrics_by_name(pmid_data['journal'])
                        if journal_metrics:
                            node['impact_factor'] = journal_metrics.impact_factor
                            node['jcr_quartile'] = journal_metrics.jcr_quartile
                            node['cas_quartile'] = journal_metrics.cas_quartile
                    if pmid_data.get('authors'):
                        node['authors'] = pmid_data['authors']
                    if pmid_data.get('year'):
                        node['year'] = pmid_data['year']
                        # 更新标签
                        node['label'] = self.create_author_label(node['authors'], node['year'])

                    updated_count += 1

            logger.info(f"   ✅ 成功更新 {updated_count} 篇文献的完整信息")
            logger.info(f"   📊 其中引用文献: {len([n for n in relevant_references if n.get('abstract')])}")
            logger.info(f"   📊 其中被引文献: {len([n for n in relevant_citations if n.get('abstract')])}")

        except Exception as e:
            logger.error(f"获取相关文献摘要失败: {e}")


# 创建全局服务实例
citation_network_service = FastCitationNetworkService()
