"""Database selector service for choosing the best literature database."""

import logging
from typing import List, Dict, Any, Optional, Tuple
from enum import Enum

from services.pubmed_service import pubmed_service
from services.arxiv_service import arxiv_service
from services.semantic_scholar_service import semantic_scholar_service

logger = logging.getLogger(__name__)


class DatabaseType(Enum):
    """Supported literature databases."""
    PUBMED = "pubmed"
    ARXIV = "arxiv"
    SEMANTIC_SCHOLAR = "semantic_scholar"


class DatabaseSelectorService:
    """Service for selecting and using appropriate literature databases."""
    
    def __init__(self):
        self.services = {
            DatabaseType.PUBMED: pubmed_service,
            DatabaseType.ARXIV: arxiv_service,
            DatabaseType.SEMANTIC_SCHOLAR: semantic_scholar_service
        }
        
        # Database characteristics for recommendation
        self.database_info = {
            DatabaseType.PUBMED: {
                'name': 'PubMed',
                'description': '生物医学和生命科学文献数据库',
                'strengths': ['医学', '生物学', '药学', '护理学', '牙科学', '兽医学', '健康科学'],
                'coverage': '主要覆盖生物医学领域的同行评议期刊文章',
                'size': '超过3400万条记录',
                'update_frequency': '每日更新',
                'full_text': False,
                'citation_data': True,
                'impact_factor': True
            },
            DatabaseType.ARXIV: {
                'name': 'arXiv',
                'description': '预印本论文数据库',
                'strengths': ['物理学', '数学', '计算机科学', '量化生物学', '量化金融', '统计学'],
                'coverage': '主要覆盖STEM领域的预印本论文',
                'size': '超过200万篇论文',
                'update_frequency': '每日更新',
                'full_text': True,
                'citation_data': False,
                'impact_factor': False
            },
            DatabaseType.SEMANTIC_SCHOLAR: {
                'name': 'Semantic Scholar',
                'description': '跨学科学术文献数据库',
                'strengths': ['计算机科学', '神经科学', '生物医学', '跨学科研究'],
                'coverage': '覆盖多个学科的学术论文，包括开放获取内容',
                'size': '超过2亿篇论文',
                'update_frequency': '持续更新',
                'full_text': True,
                'citation_data': True,
                'impact_factor': False
            }
        }
    
    def recommend_database(self, query: str) -> DatabaseType:
        """根据查询内容推荐最适合的数据库."""
        query_lower = query.lower()
        
        # 医学生物学关键词
        medical_keywords = [
            'medical', 'medicine', 'clinical', 'patient', 'disease', 'therapy', 'treatment',
            'drug', 'pharmaceutical', 'biology', 'biological', 'gene', 'protein', 'cell',
            'cancer', 'tumor', 'diagnosis', 'surgery', 'nursing', 'health', 'healthcare',
            '医学', '临床', '患者', '疾病', '治疗', '药物', '生物', '基因', '蛋白质', '细胞',
            '癌症', '肿瘤', '诊断', '手术', '护理', '健康', '医疗'
        ]
        
        # 计算机科学关键词
        cs_keywords = [
            'computer', 'algorithm', 'machine learning', 'artificial intelligence', 'ai',
            'deep learning', 'neural network', 'programming', 'software', 'data science',
            'computer vision', 'nlp', 'natural language processing', 'robotics',
            '计算机', '算法', '机器学习', '人工智能', '深度学习', '神经网络', '编程',
            '软件', '数据科学', '计算机视觉', '自然语言处理', '机器人'
        ]
        
        # 物理数学关键词
        physics_math_keywords = [
            'physics', 'quantum', 'mathematics', 'mathematical', 'statistics', 'statistical',
            'probability', 'algebra', 'geometry', 'calculus', 'topology', 'analysis',
            '物理', '量子', '数学', '统计', '概率', '代数', '几何', '微积分', '拓扑', '分析'
        ]
        
        # 计算匹配分数
        medical_score = sum(1 for keyword in medical_keywords if keyword in query_lower)
        cs_score = sum(1 for keyword in cs_keywords if keyword in query_lower)
        physics_math_score = sum(1 for keyword in physics_math_keywords if keyword in query_lower)
        
        # 根据分数推荐数据库
        if medical_score > 0 and medical_score >= cs_score and medical_score >= physics_math_score:
            return DatabaseType.PUBMED
        elif physics_math_score > 0 and physics_math_score >= cs_score:
            return DatabaseType.ARXIV
        elif cs_score > 0:
            return DatabaseType.SEMANTIC_SCHOLAR
        else:
            # 默认推荐Semantic Scholar，因为它覆盖面最广
            return DatabaseType.SEMANTIC_SCHOLAR
    
    def get_database_info(self, db_type: DatabaseType) -> Dict[str, Any]:
        """获取数据库信息."""
        return self.database_info.get(db_type, {})
    
    def search_papers(self, query: str, database: DatabaseType, 
                     max_results: int = 100, **kwargs) -> Tuple[List[Dict], int]:
        """在指定数据库中搜索文献."""
        try:
            service = self.services.get(database)
            if not service:
                logger.error(f"Unsupported database: {database}")
                return [], 0
            
            logger.info(f"Searching in {database.value} database")
            
            if database == DatabaseType.PUBMED:
                # PubMed specific parameters
                pmids, total_count = service.search_papers(
                    query=query,
                    max_results=max_results,
                    retstart=kwargs.get('offset', 0),
                    sort=kwargs.get('sort', 'relevance')
                )
                
                if pmids:
                    # Get paper details
                    papers = service.get_papers_details(pmids)
                    return papers, total_count
                else:
                    return [], total_count
                    
            elif database == DatabaseType.ARXIV:
                # arXiv specific parameters
                return service.search_papers(
                    query=query,
                    max_results=max_results,
                    start=kwargs.get('offset', 0),
                    sort_by=kwargs.get('sort', 'relevance')
                )
                
            elif database == DatabaseType.SEMANTIC_SCHOLAR:
                # Semantic Scholar specific parameters
                return service.search_papers(
                    query=query,
                    max_results=max_results,
                    offset=kwargs.get('offset', 0),
                    sort=kwargs.get('sort', 'relevance')
                )
            
        except Exception as e:
            logger.error(f"Error searching {database.value}: {str(e)}")
            return [], 0
    
    def get_paper_details(self, paper_id: str, database: DatabaseType) -> Optional[Dict]:
        """获取特定论文的详细信息."""
        try:
            service = self.services.get(database)
            if not service:
                logger.error(f"Unsupported database: {database}")
                return None
            
            if database == DatabaseType.PUBMED:
                return service.get_paper_detail(paper_id)
            elif database == DatabaseType.ARXIV:
                return service.get_paper_details(paper_id)
            elif database == DatabaseType.SEMANTIC_SCHOLAR:
                return service.get_paper_details(paper_id)
                
        except Exception as e:
            logger.error(f"Error getting paper details from {database.value}: {str(e)}")
            return None
    
    def search_multiple_databases(self, query: str, databases: List[DatabaseType], 
                                 max_results_per_db: int = 50) -> Dict[str, Tuple[List[Dict], int]]:
        """在多个数据库中搜索并返回结果."""
        results = {}
        
        for db_type in databases:
            try:
                papers, total_count = self.search_papers(
                    query=query,
                    database=db_type,
                    max_results=max_results_per_db
                )
                results[db_type.value] = (papers, total_count)
                logger.info(f"Found {len(papers)} papers in {db_type.value}")
                
            except Exception as e:
                logger.error(f"Error searching {db_type.value}: {str(e)}")
                results[db_type.value] = ([], 0)
        
        return results
    
    def get_available_databases(self) -> List[Dict[str, Any]]:
        """获取所有可用数据库的信息."""
        return [
            {
                'type': db_type.value,
                'info': self.database_info[db_type]
            }
            for db_type in DatabaseType
        ]


# Create global instance
database_selector = DatabaseSelectorService()
