#!/usr/bin/env python3
"""
文献追踪服务

基于Connected Papers风格的文献网络图谱功能：
1. 输入DOI/PMID/标题解析文献
2. 获取引用文献和被引文献
3. 生成带侧边栏的交互式网络图谱
4. 支持节点点击和文献详情展示
"""

import logging
import re
import json
import requests
import time
import math
import random
from typing import List, Dict, Any, Optional, Tuple, Set
from datetime import datetime
from collections import defaultdict

from services.embedding_service import embedding_service

logger = logging.getLogger(__name__)


class LiteratureTrackingService:
    """文献追踪服务 - Connected Papers风格"""

    def __init__(self):
        self.max_nodes = 10  # 每种类型的最大节点数量
        self.request_delay = 1.0  # 请求间隔（秒）
        self.max_retries = 3  # 最大重试次数
        self.semantic_scholar_base_url = "https://api.semanticscholar.org/graph/v1/paper"
        self.opencitations_base_url = "https://opencitations.net/index/coci/api/v1"
    
    def parse_input(self, user_input: str) -> str:
        """解析用户输入，提取DOI"""
        user_input = user_input.strip()

        # 检查是否为DOI
        doi_pattern = r'10\.\d{4,}/[^\s]+'
        doi_match = re.search(doi_pattern, user_input)
        if doi_match:
            return doi_match.group()

        # 检查是否为PMID
        pmid_pattern = r'^\d{8,}$'
        if re.match(pmid_pattern, user_input):
            # 通过PubMed API转换PMID为DOI
            try:
                pmid_url = f"https://eutils.ncbi.nlm.nih.gov/entrez/eutils/esummary.fcgi?db=pubmed&id={user_input}&retmode=json"
                response = requests.get(pmid_url, timeout=10)
                if response.status_code == 200:
                    data = response.json()
                    if 'result' in data and user_input in data['result']:
                        # 这里可以进一步处理PMID转DOI的逻辑
                        pass
            except:
                pass

        # 如果是标题，需要通过搜索API找到对应的DOI
        # 这里简化处理，直接抛出异常要求用户提供DOI
        raise ValueError("请提供有效的DOI，例如：10.1038/nature12373")

    def make_request(self, url: str, max_retries: int = 3) -> Optional[Dict]:
        """发送HTTP请求，带重试机制"""
        for attempt in range(max_retries):
            try:
                logger.info(f"   🌐 请求 {url} (尝试 {attempt + 1}/{max_retries})")
                response = requests.get(url, timeout=30)
                if response.status_code == 200:
                    logger.info("   ✅ 请求成功")
                    return response.json()
                else:
                    logger.warning(f"   ❌ 请求失败，状态码: {response.status_code}")
            except Exception as e:
                logger.warning(f"   ❌ 尝试 {attempt + 1} 失败：{str(e)[:100]}")
                if attempt < max_retries - 1:
                    time.sleep(self.request_delay * (attempt + 1))
        return None

    def get_crossref_work(self, doi: str) -> Optional[Dict]:
        """从Crossref获取文献基本信息"""
        url = f"https://api.crossref.org/works/{doi}"
        data = self.make_request(url)
        return data.get("message") if data else None

    def get_semantic_scholar_data(self, doi: str) -> Optional[Dict]:
        """从Semantic Scholar获取引用数据"""
        url = f"https://api.semanticscholar.org/graph/v1/paper/DOI:{doi}?fields=citationCount,references,citations"
        return self.make_request(url)

    def generate_citation_network(self, seed_paper: Dict[str, Any], 
                                all_papers: List[Dict[str, Any]]) -> Dict[str, Any]:
        """生成文献网络图谱
        
        Args:
            seed_paper: 种子文献信息
            all_papers: 所有相关文献列表
            
        Returns:
            包含网络图数据的字典
        """
        try:
            logger.info("开始生成文献网络图谱...")
            
            # 如果没有其他文献，生成演示网络
            if not all_papers or len(all_papers) <= 1:
                logger.info("只有种子文献，生成演示网络")
                return self._generate_demo_network(seed_paper)

            # 构建文献索引
            papers_index = self._build_papers_index(all_papers)

            # 分析引用关系
            citation_graph = self._analyze_citation_relationships(seed_paper, papers_index)

            # 计算相关度分数
            relatedness_scores = self._calculate_relatedness_scores(citation_graph, seed_paper)

            # 选择最相关的文献节点
            selected_papers = self._select_relevant_papers(citation_graph, relatedness_scores)

            # 生成网络图数据
            network_data = self._generate_network_data(selected_papers, citation_graph, seed_paper)

            # 分析Prior和Derivative Works
            prior_works = self._analyze_prior_works(seed_paper, papers_index)
            derivative_works = self._analyze_derivative_works(seed_paper, papers_index)

            logger.info(f"网络图生成完成: {len(network_data['nodes'])} 个节点, {len(network_data['edges'])} 条边")

            return {
                "success": True,
                "seed_paper": seed_paper,
                "network": network_data,
                "prior_works": prior_works,
                "derivative_works": derivative_works,
                "statistics": {
                    "total_nodes": len(network_data['nodes']),
                    "total_edges": len(network_data['edges']),
                    "prior_count": len(prior_works),
                    "derivative_count": len(derivative_works)
                }
            }
            
        except Exception as e:
            logger.error(f"生成文献网络图失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    def _generate_demo_network(self, seed_paper: Dict[str, Any]) -> Dict[str, Any]:
        """生成演示网络图（当只有种子文献时）"""
        try:
            # 检查是否有DOI，如果有则尝试获取真实引用数据
            doi = seed_paper.get('doi')
            if doi:
                logger.info(f"种子文献有DOI，尝试获取真实引用网络: {doi}")
                try:
                    from services.citation_network_service import citation_network_service
                    real_network = citation_network_service.build_citation_network(doi)

                    # 转换网络数据格式以匹配预期的返回格式
                    if real_network and 'nodes' in real_network:
                        logger.info("成功获取真实引用网络，转换格式")

                        # 分离不同类型的节点
                        prior_works = []
                        derivative_works = []

                        for node in real_network['nodes']:
                            node_type = node.get('node_type', '')
                            if node_type == 'reference':
                                # 转换为prior work格式
                                prior_work = {
                                    'title': node.get('full_title', '') or node.get('title', ''),
                                    'authors': node.get('authors', []),
                                    'journal': node.get('journal', ''),
                                    'pub_year': node.get('year', ''),
                                    'similarity_score': node.get('similarity_score', 0.5),  # 使用计算的相关度，默认0.5
                                    'is_real_citation': True
                                }
                                prior_works.append(prior_work)
                                logger.debug(f"   📊 Prior work: {prior_work['title'][:50]}... 相关度: {prior_work['similarity_score']:.3f}")
                            elif node_type == 'citation':
                                # 转换为derivative work格式
                                derivative_work = {
                                    'title': node.get('full_title', '') or node.get('title', ''),
                                    'authors': node.get('authors', []),
                                    'journal': node.get('journal', ''),
                                    'pub_year': node.get('year', ''),
                                    'similarity_score': node.get('similarity_score', 0.5),  # 使用计算的相关度，默认0.5
                                    'is_real_citation': True
                                }
                                derivative_works.append(derivative_work)
                                logger.debug(f"   📊 Derivative work: {derivative_work['title'][:50]}... 相关度: {derivative_work['similarity_score']:.3f}")

                        # 按相关度排序
                        prior_works.sort(key=lambda x: x.get('similarity_score', 0), reverse=True)
                        derivative_works.sort(key=lambda x: x.get('similarity_score', 0), reverse=True)

                        logger.info(f"   📊 转换完成: {len(prior_works)} 个祖先文献, {len(derivative_works)} 个后代文献")
                        if prior_works:
                            logger.info(f"   📈 最高相关度祖先文献: {prior_works[0]['similarity_score']:.3f}")
                        if derivative_works:
                            logger.info(f"   📈 最高相关度后代文献: {derivative_works[0]['similarity_score']:.3f}")

                        return {
                            "success": True,
                            "seed_paper": seed_paper,
                            "network": real_network,
                            "prior_works": prior_works,
                            "derivative_works": derivative_works,
                            "statistics": {
                                "total_nodes": len(real_network['nodes']),
                                "total_edges": len(real_network['edges']),
                                "prior_count": len(prior_works),
                                "derivative_count": len(derivative_works)
                            },
                            "is_real_network": True
                        }

                except Exception as e:
                    logger.warning(f"获取真实引用网络失败，回退到演示网络: {e}")

            # 创建种子节点
            seed_node = {
                "id": seed_paper.get('pmid', 'seed'),
                "title": seed_paper.get('title', 'Seed Paper'),
                "authors": seed_paper.get('authors', ['Unknown Author']),
                "year": seed_paper.get('year', '2023'),
                "journal": seed_paper.get('journal', 'Unknown Journal'),
                "impact_factor": seed_paper.get('impact_factor', '0.0'),
                "jcr_quartile": seed_paper.get('jcr_quartile', 'Q4'),
                "type": "seed",
                "size": 20,
                "color": "#ff9500"
            }

            # 创建一些演示相关文献节点
            demo_papers = [
                {
                    "id": "demo_1",
                    "title": "相关研究 1: 基础理论研究",
                    "authors": ["研究者 A", "研究者 B"],
                    "year": "2022",
                    "journal": "顶级期刊 A",
                    "impact_factor": "8.5",
                    "jcr_quartile": "Q1",
                    "type": "prior",
                    "size": 15,
                    "color": "#ff6b6b"
                },
                {
                    "id": "demo_2",
                    "title": "相关研究 2: 方法学改进",
                    "authors": ["研究者 C", "研究者 D"],
                    "year": "2021",
                    "journal": "权威期刊 B",
                    "impact_factor": "6.2",
                    "jcr_quartile": "Q1",
                    "type": "prior",
                    "size": 12,
                    "color": "#ff6b6b"
                },
                {
                    "id": "demo_3",
                    "title": "后续研究 1: 应用拓展",
                    "authors": ["研究者 E", "研究者 F"],
                    "year": "2024",
                    "journal": "新兴期刊 C",
                    "impact_factor": "4.1",
                    "jcr_quartile": "Q2",
                    "type": "derivative",
                    "size": 10,
                    "color": "#4ecdc4"
                }
            ]

            # 创建边（引用关系）
            edges = [
                {
                    "source": "demo_1",
                    "target": seed_node["id"],
                    "type": "citation",
                    "weight": 1
                },
                {
                    "source": "demo_2",
                    "target": seed_node["id"],
                    "type": "citation",
                    "weight": 1
                },
                {
                    "source": seed_node["id"],
                    "target": "demo_3",
                    "type": "citation",
                    "weight": 1
                }
            ]

            network_data = {
                "nodes": [seed_node] + demo_papers,
                "edges": edges
            }

            # 创建Prior和Derivative Works
            prior_works = demo_papers[:2]  # 前两个作为Prior Works
            derivative_works = demo_papers[2:]  # 后面的作为Derivative Works

            logger.info("演示网络图生成完成")

            return {
                "success": True,
                "seed_paper": seed_paper,
                "network": network_data,
                "prior_works": prior_works,
                "derivative_works": derivative_works,
                "statistics": {
                    "total_nodes": len(network_data['nodes']),
                    "total_edges": len(network_data['edges']),
                    "prior_count": len(prior_works),
                    "derivative_count": len(derivative_works)
                },
                "is_demo": True
            }

        except Exception as e:
            logger.error(f"生成演示网络图失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def _build_papers_index(self, papers: List[Dict[str, Any]]) -> Dict[str, Dict[str, Any]]:
        """构建文献索引"""
        index = {}
        for paper in papers:
            # 使用PMID作为主键，标题作为备用键
            pmid = paper.get('pmid')
            title = paper.get('title', '').strip().lower()
            
            if pmid:
                index[str(pmid)] = paper
            if title:
                index[title] = paper
                
        return index
    
    def _analyze_citation_relationships(self, seed_paper: Dict[str, Any], 
                                      papers_index: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """分析引用关系"""
        citation_graph = {
            'papers': {},
            'citations': defaultdict(set),  # paper_id -> set of cited paper_ids
            'cited_by': defaultdict(set)    # paper_id -> set of citing paper_ids
        }
        
        seed_id = str(seed_paper.get('pmid', seed_paper.get('title', '').strip().lower()))
        citation_graph['papers'][seed_id] = seed_paper
        
        # 模拟引用关系分析（在实际应用中，这里需要真实的引用数据）
        # 由于我们的数据可能没有完整的引用关系，我们基于相似度和时间来模拟
        seed_year = self._extract_year(seed_paper.get('pub_year'))
        seed_keywords = self._extract_keywords(seed_paper)
        
        for paper_id, paper in papers_index.items():
            if paper_id == seed_id:
                continue
                
            paper_year = self._extract_year(paper.get('pub_year'))
            paper_keywords = self._extract_keywords(paper)
            
            # 计算相似度
            similarity = self._calculate_keyword_similarity(seed_keywords, paper_keywords)
            
            if similarity > 0.3:  # 相似度阈值
                citation_graph['papers'][paper_id] = paper
                
                # 基于时间关系模拟引用
                if paper_year and seed_year:
                    if paper_year < seed_year:
                        # 较早的文献可能被种子文献引用
                        citation_graph['citations'][seed_id].add(paper_id)
                        citation_graph['cited_by'][paper_id].add(seed_id)
                    elif paper_year > seed_year:
                        # 较晚的文献可能引用种子文献
                        citation_graph['citations'][paper_id].add(seed_id)
                        citation_graph['cited_by'][seed_id].add(paper_id)
        
        return citation_graph
    
    def _calculate_relatedness_scores(self, citation_graph: Dict[str, Any], 
                                    seed_paper: Dict[str, Any]) -> Dict[str, float]:
        """计算相关度分数"""
        scores = {}
        seed_id = str(seed_paper.get('pmid', seed_paper.get('title', '').strip().lower()))
        
        for paper_id, paper in citation_graph['papers'].items():
            if paper_id == seed_id:
                scores[paper_id] = 1.0  # 种子文献相关度最高
                continue
            
            # 共引强度（被同一文献引用的次数）
            co_citation_score = len(citation_graph['cited_by'][paper_id] & 
                                  citation_graph['cited_by'][seed_id])
            
            # 共参数量（引用相同文献的数量）
            co_reference_score = len(citation_graph['citations'][paper_id] & 
                                   citation_graph['citations'][seed_id])
            
            # 直接引用关系
            direct_citation_score = 0
            if paper_id in citation_graph['citations'][seed_id] or \
               seed_id in citation_graph['citations'][paper_id]:
                direct_citation_score = 1
            
            # 综合相关度分数
            relatedness = (co_citation_score * 0.4 + 
                         co_reference_score * 0.4 + 
                         direct_citation_score * 0.2)
            
            # 加入时间衰减因子
            paper_year = self._extract_year(paper.get('pub_year'))
            seed_year = self._extract_year(seed_paper.get('pub_year'))
            if paper_year and seed_year:
                year_diff = abs(paper_year - seed_year)
                time_decay = math.exp(-year_diff / 10)  # 10年半衰期
                relatedness *= time_decay
            
            scores[paper_id] = relatedness
        
        return scores
    
    def _select_relevant_papers(self, citation_graph: Dict[str, Any], 
                              relatedness_scores: Dict[str, float]) -> List[Dict[str, Any]]:
        """选择最相关的文献"""
        # 按相关度排序
        sorted_papers = sorted(
            citation_graph['papers'].items(),
            key=lambda x: relatedness_scores.get(x[0], 0),
            reverse=True
        )
        
        # 选择前N篇最相关的文献
        selected = []
        for paper_id, paper in sorted_papers[:self.max_nodes]:
            paper_copy = paper.copy()
            paper_copy['relatedness_score'] = relatedness_scores.get(paper_id, 0)
            paper_copy['node_id'] = paper_id
            selected.append(paper_copy)
        
        return selected
    
    def _generate_network_data(self, selected_papers: List[Dict[str, Any]], 
                             citation_graph: Dict[str, Any], 
                             seed_paper: Dict[str, Any]) -> Dict[str, Any]:
        """生成网络图数据"""
        nodes = []
        edges = []
        
        # 创建节点ID映射
        paper_ids = {paper['node_id'] for paper in selected_papers}
        seed_id = str(seed_paper.get('pmid', seed_paper.get('title', '').strip().lower()))
        
        # 生成节点
        for paper in selected_papers:
            node_id = paper['node_id']
            
            # 计算节点大小（基于被引用次数）
            citation_count = len(citation_graph['cited_by'].get(node_id, set()))
            node_size = max(10, min(50, 10 + citation_count * 2))
            
            # 计算节点颜色（基于发表年份）
            year = self._extract_year(paper.get('pub_year'))
            color_intensity = self._calculate_color_intensity(year)
            
            node = {
                "id": node_id,
                "label": paper.get('title', '')[:50] + "...",
                "title": paper.get('title', ''),
                "authors": paper.get('authors', []),
                "journal": paper.get('journal', ''),
                "year": paper.get('pub_year', ''),
                "pmid": paper.get('pmid', ''),
                "abstract": paper.get('abstract', ''),
                "size": node_size,
                "color": color_intensity,
                "is_seed": node_id == seed_id,
                "relatedness_score": paper.get('relatedness_score', 0),
                "citation_count": citation_count
            }
            nodes.append(node)
        
        # 生成边
        for paper_id in paper_ids:
            # 引用关系
            for cited_id in citation_graph['citations'].get(paper_id, set()):
                if cited_id in paper_ids:
                    edge = {
                        "source": paper_id,
                        "target": cited_id,
                        "type": "citation",
                        "weight": 1
                    }
                    edges.append(edge)
        
        return {
            "nodes": nodes,
            "edges": edges
        }
    
    def _analyze_prior_works(self, seed_paper: Dict[str, Any],
                           papers_index: Dict[str, Dict[str, Any]]) -> List[Dict[str, Any]]:
        """分析祖先文献（Prior Works）"""
        prior_works = []

        try:
            # 首先尝试获取真实的引用数据
            citation_data = self.get_real_citations(seed_paper)
            real_references = citation_data.get('references', [])

            if real_references:
                logger.info(f"使用真实引用数据，找到 {len(real_references)} 个引用文献")
                # 使用真实的引用数据，并计算嵌入相关度
                for ref in real_references[:20]:  # 限制数量
                    ref['is_real_citation'] = True
                    # 计算嵌入相关度
                    embedding_score = self._calculate_embedding_similarity(seed_paper, ref)
                    ref['similarity_score'] = embedding_score if embedding_score is not None else 1.0
                    prior_works.append(ref)
            else:
                # 如果没有真实数据，使用嵌入模型分析相关度
                logger.info("未找到真实引用数据，使用嵌入模型相关度分析")
                seed_year = self._extract_year(seed_paper.get('pub_year'))

                # 收集候选文献
                candidate_papers = []
                for paper_id, paper in papers_index.items():
                    paper_year = self._extract_year(paper.get('pub_year'))
                    # 只考虑更早发表的文献
                    if paper_year and seed_year and paper_year < seed_year:
                        candidate_papers.append(paper)

                # 使用嵌入模型批量计算相关度
                if candidate_papers:
                    similarity_scores = self._calculate_batch_embedding_similarity(seed_paper, candidate_papers)

                    for i, paper in enumerate(candidate_papers):
                        similarity = similarity_scores.get(i, 0)
                        if similarity > 0.6:  # 较高的相似度阈值
                            paper_copy = paper.copy()
                            paper_copy['similarity_score'] = similarity
                            paper_copy['is_real_citation'] = False
                            prior_works.append(paper_copy)

            # 按相似度排序，返回前20篇（所有情况都排序）
            prior_works.sort(key=lambda x: x.get('similarity_score', 0), reverse=True)
            prior_works = prior_works[:20]

        except Exception as e:
            logger.error(f"分析祖先文献失败: {e}")

        return prior_works
    
    def _analyze_derivative_works(self, seed_paper: Dict[str, Any],
                                papers_index: Dict[str, Dict[str, Any]]) -> List[Dict[str, Any]]:
        """分析后代文献（Derivative Works）"""
        derivative_works = []

        try:
            # 首先尝试获取真实的引用数据
            citation_data = self.get_real_citations(seed_paper)
            real_citations = citation_data.get('citations', [])

            if real_citations:
                logger.info(f"使用真实被引用数据，找到 {len(real_citations)} 个引用该文献的文献")
                # 使用真实的被引用数据，并计算嵌入相关度
                for cit in real_citations[:20]:  # 限制数量
                    cit['is_real_citation'] = True
                    # 计算嵌入相关度
                    embedding_score = self._calculate_embedding_similarity(seed_paper, cit)
                    cit['similarity_score'] = embedding_score if embedding_score is not None else 1.0
                    derivative_works.append(cit)
            else:
                # 如果没有真实数据，使用嵌入模型分析相关度
                logger.info("未找到真实被引用数据，使用嵌入模型相关度分析")
                seed_year = self._extract_year(seed_paper.get('pub_year'))

                # 收集候选文献
                candidate_papers = []
                for paper_id, paper in papers_index.items():
                    paper_year = self._extract_year(paper.get('pub_year'))
                    # 只考虑更晚发表的文献
                    if paper_year and seed_year and paper_year > seed_year:
                        candidate_papers.append(paper)

                # 使用嵌入模型批量计算相关度
                if candidate_papers:
                    similarity_scores = self._calculate_batch_embedding_similarity(seed_paper, candidate_papers)

                    for i, paper in enumerate(candidate_papers):
                        similarity = similarity_scores.get(i, 0)
                        if similarity > 0.6:  # 较高的相似度阈值
                            paper_copy = paper.copy()
                            paper_copy['similarity_score'] = similarity
                            paper_copy['is_real_citation'] = False
                            derivative_works.append(paper_copy)

            # 按相似度排序，返回前20篇（所有情况都排序）
            derivative_works.sort(key=lambda x: x.get('similarity_score', 0), reverse=True)
            derivative_works = derivative_works[:20]

        except Exception as e:
            logger.error(f"分析后代文献失败: {e}")

        return derivative_works
    
    def _extract_year(self, pub_year: Any) -> int:
        """提取发表年份"""
        try:
            if isinstance(pub_year, int):
                return pub_year
            elif isinstance(pub_year, str):
                # 提取年份数字
                year_match = re.search(r'\d{4}', pub_year)
                if year_match:
                    return int(year_match.group())
            return None
        except:
            return None
    
    def _extract_keywords(self, paper: Dict[str, Any]) -> Set[str]:
        """提取文献关键词"""
        keywords = set()
        
        # 从标题提取
        title = paper.get('title', '').lower()
        if title:
            # 简单的关键词提取（实际应用中可以使用更复杂的NLP技术）
            words = title.split()
            keywords.update([word.strip('.,!?;:()[]{}') for word in words if len(word) > 3])
        
        # 从摘要提取（如果有）
        abstract = paper.get('abstract', '').lower()
        if abstract:
            words = abstract.split()[:50]  # 只取前50个词
            keywords.update([word.strip('.,!?;:()[]{}') for word in words if len(word) > 4])
        
        return keywords
    
    def _calculate_keyword_similarity(self, keywords1: Set[str], keywords2: Set[str]) -> float:
        """计算关键词相似度"""
        if not keywords1 or not keywords2:
            return 0.0

        intersection = len(keywords1 & keywords2)
        union = len(keywords1 | keywords2)

        return intersection / union if union > 0 else 0.0

    def _calculate_embedding_similarity(self, paper1: Dict[str, Any], paper2: Dict[str, Any]) -> float:
        """使用嵌入模型计算两篇文献的相似度"""
        try:
            # 提取文本内容
            title1 = paper1.get('title', '').strip()
            title2 = paper2.get('title', '').strip()

            if not title1 or not title2:
                return 0.0

            # 使用嵌入服务计算相似度
            similarity = embedding_service.calculate_similarity(title1, title2)

            # 将相似度转换为0-1的分数
            return max(0.0, min(1.0, similarity))

        except Exception as e:
            logger.error(f"计算嵌入相似度失败: {e}")
            return 0.0

    def _calculate_batch_embedding_similarity(self, seed_paper: Dict[str, Any],
                                           papers: List[Dict[str, Any]]) -> Dict[int, float]:
        """批量计算嵌入相似度"""
        try:
            # 提取种子文献标题
            seed_title = seed_paper.get('title', '').strip()
            if not seed_title:
                return {}

            # 提取所有文献标题
            paper_titles = []
            for paper in papers:
                title = paper.get('title', '').strip()
                paper_titles.append(title if title else "")

            # 过滤空标题
            valid_indices = [i for i, title in enumerate(paper_titles) if title]
            valid_titles = [paper_titles[i] for i in valid_indices]

            if not valid_titles:
                return {}

            # 准备所有文本
            all_texts = [seed_title] + valid_titles

            # 获取嵌入向量
            embeddings = embedding_service.get_embeddings_batch(all_texts)

            if not embeddings or len(embeddings) < 2:
                logger.warning("获取嵌入向量失败")
                return {}

            # 提取种子文献的嵌入向量
            seed_embedding = embeddings[0]

            # 计算相似度
            similarities = {}
            for i, idx in enumerate(valid_indices):
                if i + 1 < len(embeddings) and embeddings[i + 1] is not None:
                    from sklearn.metrics.pairwise import cosine_similarity
                    sim = cosine_similarity([seed_embedding], [embeddings[i + 1]])[0][0]
                    similarities[idx] = max(0.0, min(1.0, sim))

            return similarities

        except Exception as e:
            logger.error(f"批量计算嵌入相似度失败: {e}")
            return {}

    def _calculate_color_intensity(self, year: int) -> str:
        """计算节点颜色强度（基于年份）"""
        if not year:
            return "#888888"
        
        current_year = datetime.now().year
        age = current_year - year
        
        if age <= 2:
            return "#FF6B6B"  # 最新文献 - 红色
        elif age <= 5:
            return "#4ECDC4"  # 较新文献 - 青色
        elif age <= 10:
            return "#45B7D1"  # 中等年龄 - 蓝色
        else:
            return "#96CEB4"  # 较老文献 - 绿色

    def get_real_citations(self, paper: Dict[str, Any]) -> Dict[str, List[Dict[str, Any]]]:
        """
        获取真实的引用关系数据

        Args:
            paper: 文献信息

        Returns:
            包含references（引用的文献）和citations（被引用的文献）的字典
        """
        result = {
            'references': [],  # 该文献引用的其他文献（祖先文献）
            'citations': []    # 引用该文献的其他文献（后代文献）
        }

        try:
            # 尝试通过多种方式获取引用数据
            pmid = paper.get('pmid')
            doi = paper.get('doi')
            title = paper.get('title', '')

            # 1. 尝试Semantic Scholar API
            if pmid or doi or title:
                semantic_data = self._get_semantic_scholar_citations(pmid, doi, title)
                if semantic_data:
                    result['references'].extend(semantic_data.get('references', []))
                    result['citations'].extend(semantic_data.get('citations', []))

            # 2. 如果Semantic Scholar没有数据，尝试OpenCitations
            if not result['references'] and not result['citations'] and doi:
                opencitations_data = self._get_opencitations_data(doi)
                if opencitations_data:
                    result['references'].extend(opencitations_data.get('references', []))
                    result['citations'].extend(opencitations_data.get('citations', []))

            logger.info(f"获取到引用数据: {len(result['references'])} 个引用, {len(result['citations'])} 个被引用")

        except Exception as e:
            logger.error(f"获取引用关系失败: {e}")

        return result

    def _get_semantic_scholar_citations(self, pmid: str = None, doi: str = None, title: str = None) -> Dict[str, List[Dict[str, Any]]]:
        """通过Semantic Scholar API获取引用数据"""
        try:
            # 构建查询参数
            paper_id = None
            if pmid:
                paper_id = f"PMID:{pmid}"
            elif doi:
                paper_id = f"DOI:{doi}"
            elif title:
                # 通过标题搜索
                search_url = f"{self.semantic_scholar_base_url}/search"
                search_params = {
                    'query': title,
                    'limit': 1,
                    'fields': 'paperId,title,authors,year,journal,citationCount,referenceCount'
                }

                response = requests.get(search_url, params=search_params, timeout=10)
                if response.status_code == 200:
                    search_data = response.json()
                    if search_data.get('data') and len(search_data['data']) > 0:
                        paper_id = search_data['data'][0]['paperId']

            if not paper_id:
                return {}

            # 获取引用和被引用数据
            result = {'references': [], 'citations': []}

            # 获取该文献引用的其他文献（references）
            ref_url = f"{self.semantic_scholar_base_url}/{paper_id}/references"
            ref_params = {
                'fields': 'paperId,title,authors,year,journal,citationCount',
                'limit': 50
            }

            time.sleep(0.1)  # 避免请求过快
            ref_response = requests.get(ref_url, params=ref_params, timeout=10)
            if ref_response.status_code == 200:
                ref_data = ref_response.json()
                for ref in ref_data.get('data', []):
                    if ref.get('citedPaper'):
                        cited_paper = ref['citedPaper']
                        result['references'].append({
                            'id': cited_paper.get('paperId'),
                            'title': cited_paper.get('title', ''),
                            'authors': [author.get('name', '') for author in cited_paper.get('authors', [])],
                            'year': str(cited_paper.get('year', '')),
                            'journal': cited_paper.get('journal', {}).get('name', ''),
                            'citation_count': cited_paper.get('citationCount', 0)
                        })

            # 获取引用该文献的其他文献（citations）
            cit_url = f"{self.semantic_scholar_base_url}/{paper_id}/citations"
            cit_params = {
                'fields': 'paperId,title,authors,year,journal,citationCount',
                'limit': 50
            }

            time.sleep(0.1)  # 避免请求过快
            cit_response = requests.get(cit_url, params=cit_params, timeout=10)
            if cit_response.status_code == 200:
                cit_data = cit_response.json()
                for cit in cit_data.get('data', []):
                    if cit.get('citingPaper'):
                        citing_paper = cit['citingPaper']
                        result['citations'].append({
                        'id': citing_paper.get('paperId'),
                        'title': citing_paper.get('title', ''),
                        'authors': [author.get('name', '') for author in citing_paper.get('authors', [])],
                        'year': str(citing_paper.get('year', '')),
                        'journal': citing_paper.get('journal', {}).get('name', ''),
                        'citation_count': citing_paper.get('citationCount', 0)
                    })

            return result

        except Exception as e:
            logger.error(f"Semantic Scholar API请求失败: {e}")
            return {}

    def _get_opencitations_data(self, doi: str) -> Dict[str, List[Dict[str, Any]]]:
        """通过OpenCitations API获取引用数据"""
        try:
            result = {'references': [], 'citations': []}

            # 获取该文献引用的其他文献
            ref_url = f"{self.opencitations_base_url}/references/{doi}"
            try:
                ref_response = requests.get(ref_url, timeout=10)
                if ref_response.status_code == 200:
                    ref_data = ref_response.json()
                    for ref in ref_data:
                        result['references'].append({
                            'id': ref.get('cited', ''),
                            'title': ref.get('title', ''),
                            'authors': ref.get('author', '').split('; ') if ref.get('author') else [],
                            'year': ref.get('year', ''),
                            'journal': ref.get('source_title', ''),
                            'citation_count': 0  # OpenCitations不提供引用次数
                        })
            except Exception as e:
                logger.warning(f"OpenCitations references请求失败: {e}")

            # 获取引用该文献的其他文献
            cit_url = f"{self.opencitations_base_url}/citations/{doi}"
            try:
                cit_response = requests.get(cit_url, timeout=10)
                if cit_response.status_code == 200:
                    cit_data = cit_response.json()
                    for cit in cit_data:
                        result['citations'].append({
                            'id': cit.get('citing', ''),
                            'title': cit.get('title', ''),
                            'authors': cit.get('author', '').split('; ') if cit.get('author') else [],
                            'year': cit.get('year', ''),
                            'journal': cit.get('source_title', ''),
                            'citation_count': 0
                        })
            except Exception as e:
                logger.warning(f"OpenCitations citations请求失败: {e}")

            return result

        except Exception as e:
            logger.error(f"OpenCitations API请求失败: {e}")
            return {}

    def search_seed_paper(self, user_input: str) -> Optional[Dict[str, Any]]:
        """搜索种子文献（统一入口）"""
        try:
            logger.info(f"🔍 搜索种子文献: {user_input}")

            # 判断输入类型并搜索
            if user_input.isdigit():
                # PMID
                logger.info(f"   📄 按PMID搜索: {user_input}")
                from services.pubmed_service import pubmed_service
                papers = pubmed_service.fetch_paper_details([user_input])
                return papers[0] if papers else None

            elif user_input.startswith('10.') and '/' in user_input:
                # DOI
                logger.info(f"   📄 按DOI搜索: {user_input}")
                crossref_data = self.get_crossref_work(user_input)
                if crossref_data:
                    return self._convert_crossref_to_paper(crossref_data, user_input)

            else:
                # 标题搜索
                logger.info(f"   📄 按标题搜索: {user_input[:50]}...")
                from services.pubmed_service import pubmed_service
                papers = pubmed_service.search_papers(user_input, max_results=1)
                return papers[0] if papers else None

            return None

        except Exception as e:
            logger.error(f"搜索种子文献失败: {e}")
            return None

    def build_unified_citation_network(self, seed_paper: Dict[str, Any]) -> Dict[str, Any]:
        """构建统一的引用网络（整合两套服务的功能）"""
        try:
            logger.info(f"🌐 构建统一引用网络: {seed_paper.get('title', '')[:50]}...")

            # 使用 citation_network_service 的核心逻辑
            from services.citation_network_service import citation_network_service

            # 构建DOI用于网络构建
            doi = seed_paper.get('doi', '')
            if not doi:
                # 如果没有DOI，尝试通过标题搜索获取
                title = seed_paper.get('title', '')
                if title:
                    doi = title  # citation_network_service 可以处理标题输入
                else:
                    raise ValueError("无法获取文献标识符")

            # 调用 citation_network_service 构建网络
            network_data = citation_network_service.build_citation_network(doi)

            # 转换为统一格式，包含侧边栏数据
            result = self._convert_to_unified_format(network_data, seed_paper)

            logger.info(f"✅ 统一网络构建完成")
            return result

        except Exception as e:
            logger.error(f"构建统一引用网络失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'network': {},
                'statistics': {},
                'prior_works': [],
                'derivative_works': []
            }

    def _convert_crossref_to_paper(self, crossref_data: Dict, doi: str) -> Dict[str, Any]:
        """将Crossref数据转换为标准文献格式"""
        try:
            authors = []
            if 'author' in crossref_data:
                for author in crossref_data['author']:
                    given = author.get('given', '')
                    family = author.get('family', '')
                    if given and family:
                        authors.append(f"{given} {family}")
                    elif family:
                        authors.append(family)

            # 提取期刊名
            journal = ''
            if 'container-title' in crossref_data and crossref_data['container-title']:
                journal = crossref_data['container-title'][0]

            # 提取年份
            year = ''
            if 'published-print' in crossref_data:
                date_parts = crossref_data['published-print'].get('date-parts', [])
                if date_parts and date_parts[0]:
                    year = str(date_parts[0][0])
            elif 'published-online' in crossref_data:
                date_parts = crossref_data['published-online'].get('date-parts', [])
                if date_parts and date_parts[0]:
                    year = str(date_parts[0][0])

            return {
                'title': crossref_data.get('title', [''])[0] if crossref_data.get('title') else '',
                'authors': authors,
                'journal': journal,
                'pub_year': year,
                'doi': doi,
                'pmid': '',  # Crossref通常没有PMID
                'abstract': ''  # Crossref通常没有摘要
            }

        except Exception as e:
            logger.error(f"转换Crossref数据失败: {e}")
            return None

    def _convert_to_unified_format(self, network_data: Dict, seed_paper: Dict[str, Any]) -> Dict[str, Any]:
        """将网络数据转换为统一格式，包含侧边栏数据"""
        try:
            # 提取网络中的节点数据
            nodes = network_data.get('nodes', [])

            # 分离祖先文献和后代文献
            prior_works = []
            derivative_works = []

            for node in nodes:
                if node.get('node_type') == 'reference':
                    prior_works.append({
                        'title': node.get('full_title', '') or node.get('title', ''),
                        'authors': node.get('authors', []),
                        'journal': node.get('journal', ''),
                        'pub_year': node.get('year', ''),
                        'similarity_score': node.get('similarity_score', 0.5),
                        'is_real_citation': True
                    })
                elif node.get('node_type') == 'citation':
                    derivative_works.append({
                        'title': node.get('full_title', '') or node.get('title', ''),
                        'authors': node.get('authors', []),
                        'journal': node.get('journal', ''),
                        'pub_year': node.get('year', ''),
                        'similarity_score': node.get('similarity_score', 0.5),
                        'is_real_citation': True
                    })

            # 按相关度排序
            prior_works.sort(key=lambda x: x.get('similarity_score', 0), reverse=True)
            derivative_works.sort(key=lambda x: x.get('similarity_score', 0), reverse=True)

            # 调试日志
            logger.info(f"📊 转换统一格式完成:")
            logger.info(f"   📄 祖先文献: {len(prior_works)} 篇")
            logger.info(f"   📄 后代文献: {len(derivative_works)} 篇")
            if prior_works:
                logger.info(f"   📊 祖先文献相关度范围: {min(p.get('similarity_score', 0) for p in prior_works):.3f} - {max(p.get('similarity_score', 0) for p in prior_works):.3f}")
            if derivative_works:
                logger.info(f"   📊 后代文献相关度范围: {min(d.get('similarity_score', 0) for d in derivative_works):.3f} - {max(d.get('similarity_score', 0) for d in derivative_works):.3f}")

            # 统计信息
            statistics = {
                'total_nodes': len(nodes),
                'total_edges': len(network_data.get('edges', [])),
                'prior_works_count': len(prior_works),
                'derivative_works_count': len(derivative_works)
            }

            return {
                'success': True,
                'network': network_data,
                'statistics': statistics,
                'prior_works': prior_works,
                'derivative_works': derivative_works,
                'seed_paper': seed_paper
            }

        except Exception as e:
            logger.error(f"转换统一格式失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'network': network_data,
                'statistics': {},
                'prior_works': [],
                'derivative_works': []
            }

# 全局服务实例
literature_tracking_service = LiteratureTrackingService()
