"""
NBIB文件导出服务

支持将验证后的参考文献导出为.nbib格式，用于EndNote、Zotero等文献管理软件
"""

import logging
import requests
from typing import List, Dict, Any
from datetime import datetime

logger = logging.getLogger(__name__)


class NBIBExportService:
    """NBIB文件导出服务"""

    @staticmethod
    def generate_nbib_content(references: List[Dict[str, Any]]) -> str:
        """
        生成NBIB格式内容 - 使用PubMed官方API获取完整数据

        Args:
            references: 验证后的参考文献列表

        Returns:
            str: NBIB格式的文件内容
        """
        try:
            # 收集所有验证成功的PMID
            verified_pmids = []
            for ref in references:
                if ref.get('verified', False) and ref.get('pmid'):
                    pmid = str(ref.get('pmid', '')).strip()
                    if pmid and pmid.isdigit():
                        verified_pmids.append(pmid)

            if not verified_pmids:
                logger.warning("没有找到有效的PMID用于导出")
                return ""

            logger.info(f"开始从PubMed API获取 {len(verified_pmids)} 篇文献的完整NBIB数据")

            # 使用PubMed API获取完整的NBIB数据
            nbib_content = NBIBExportService._fetch_from_pubmed_api(verified_pmids)

            if nbib_content:
                logger.info(f"成功从PubMed API获取NBIB数据，包含 {len(verified_pmids)} 篇文献")
                return nbib_content
            else:
                # 如果API失败，回退到本地生成
                logger.warning("PubMed API获取失败，回退到本地生成NBIB格式")
                return NBIBExportService._generate_local_nbib(references)

        except Exception as e:
            logger.error(f"生成NBIB文件失败: {str(e)}")
            # 回退到本地生成
            try:
                return NBIBExportService._generate_local_nbib(references)
            except Exception as fallback_error:
                logger.error(f"本地生成NBIB也失败: {str(fallback_error)}")
                raise
    
    @staticmethod
    def _fetch_from_pubmed_api(pmids: List[str]) -> str:
        """
        从PubMed API获取完整的NBIB格式数据

        Args:
            pmids: PMID列表

        Returns:
            str: 完整的NBIB格式内容
        """
        try:
            if not pmids:
                return ""

            # PubMed API URL
            url = 'https://eutils.ncbi.nlm.nih.gov/entrez/eutils/efetch.fcgi'

            # 分批处理，每次最多200个PMID
            batch_size = 200
            all_content = []

            for i in range(0, len(pmids), batch_size):
                batch_pmids = pmids[i:i + batch_size]

                params = {
                    'db': 'pubmed',
                    'id': ','.join(batch_pmids),
                    'retmode': 'text',
                    'rettype': 'medline'
                }

                logger.info(f"正在获取第 {i//batch_size + 1} 批文献数据，包含 {len(batch_pmids)} 篇文献")

                try:
                    response = requests.get(url, params=params, timeout=30)
                    response.raise_for_status()

                    if response.text.strip():
                        all_content.append(response.text.strip())
                        logger.info(f"成功获取第 {i//batch_size + 1} 批数据")
                    else:
                        logger.warning(f"第 {i//batch_size + 1} 批数据为空")

                except requests.exceptions.RequestException as e:
                    logger.error(f"获取第 {i//batch_size + 1} 批数据失败: {str(e)}")
                    continue

            if all_content:
                # 合并所有内容
                final_content = '\n\n'.join(all_content)
                logger.info(f"成功从PubMed API获取了 {len(all_content)} 批数据")
                return final_content
            else:
                logger.error("所有批次的API请求都失败了")
                return ""

        except Exception as e:
            logger.error(f"从PubMed API获取数据失败: {str(e)}")
            return ""

    @staticmethod
    def _generate_local_nbib(references: List[Dict[str, Any]]) -> str:
        """
        本地生成NBIB格式内容（回退方案）

        Args:
            references: 验证后的参考文献列表

        Returns:
            str: NBIB格式的文件内容
        """
        try:
            nbib_entries = []

            for ref in references:
                # 只导出验证成功的文献
                if not ref.get('verified', False):
                    continue

                entry = NBIBExportService._create_nbib_entry(ref)
                if entry:
                    nbib_entries.append(entry)

            # 添加文件头部信息
            header = f"""PMID- Version- "1.0"
Owner- NNScholar
Status- PubMed-not-MEDLINE
DA  - {datetime.now().strftime('%Y%m%d')}

"""

            content = header + "\n\n".join(nbib_entries)

            logger.info(f"本地生成NBIB文件成功，包含 {len(nbib_entries)} 篇文献")
            return content

        except Exception as e:
            logger.error(f"本地生成NBIB文件失败: {str(e)}")
            raise

    @staticmethod
    def _create_nbib_entry(ref: Dict[str, Any]) -> str:
        """
        创建单个文献的NBIB条目
        
        Args:
            ref: 单个文献的信息字典
            
        Returns:
            str: NBIB格式的单个条目
        """
        try:
            lines = []
            
            # PMID (必需)
            pmid = ref.get('pmid', '')
            if pmid:
                lines.append(f"PMID- {pmid}")
            
            # 状态信息
            lines.append("STAT- PubMed-not-MEDLINE")
            
            # 日期
            lines.append(f"DA  - {datetime.now().strftime('%Y%m%d')}")
            
            # 标题 (TI)
            title = ref.get('title', '').strip()
            if title:
                # 移除末尾的句号（如果有）
                if title.endswith('.'):
                    title = title[:-1]
                lines.append(f"TI  - {title}")
            
            # 作者 (AU)
            authors = ref.get('authors', '')
            if authors and authors != '基于标题引用' and authors != '作者信息待查证':
                # 处理作者列表
                if isinstance(authors, str):
                    author_list = [author.strip() for author in authors.split(',')]
                else:
                    author_list = authors
                
                for author in author_list:
                    if author:
                        # 转换为Last, First格式
                        formatted_author = NBIBExportService._format_author_name(author)
                        lines.append(f"AU  - {formatted_author}")
            
            # 期刊 (TA)
            journal = ref.get('journal', '').strip()
            if journal and journal != '期刊信息待查证' and journal != '待查证':
                lines.append(f"TA  - {journal}")
            
            # 发表年份 (DP)
            year = ref.get('year', '').strip()
            if year and year != '年份待查证' and year != '待查证':
                lines.append(f"DP  - {year}")
            
            # 摘要 (AB)
            abstract = ref.get('abstract', '').strip()
            if abstract and abstract != '摘要信息待查证' and not abstract.startswith('该文献'):
                # 限制摘要长度，避免过长
                if len(abstract) > 2000:
                    abstract = abstract[:2000] + "..."
                lines.append(f"AB  - {abstract}")
            
            # DOI
            doi = ref.get('doi', '').strip()
            if doi:
                lines.append(f"AID - {doi} [doi]")
            
            # 文献类型
            lines.append("PT  - Journal Article")
            
            # 语言
            lines.append("LA  - eng")
            
            # 结束标记
            lines.append("")
            
            return "\n".join(lines)
            
        except Exception as e:
            logger.error(f"创建NBIB条目失败: {str(e)}")
            return ""
    
    @staticmethod
    def _format_author_name(author_name: str) -> str:
        """
        格式化作者姓名为NBIB标准格式
        
        Args:
            author_name: 原始作者姓名
            
        Returns:
            str: 格式化后的作者姓名
        """
        try:
            # 移除多余空格
            author_name = author_name.strip()
            
            # 如果已经是"Last, First"格式，直接返回
            if ',' in author_name:
                return author_name
            
            # 尝试分割姓名
            parts = author_name.split()
            if len(parts) >= 2:
                # 假设最后一个是姓，其他是名
                last_name = parts[-1]
                first_names = ' '.join(parts[:-1])
                return f"{last_name}, {first_names}"
            else:
                # 只有一个名字，直接返回
                return author_name
                
        except Exception:
            return author_name
    
    @staticmethod
    def get_filename(prefix: str = "nnscholar_references") -> str:
        """
        生成NBIB文件名
        
        Args:
            prefix: 文件名前缀
            
        Returns:
            str: 文件名
        """
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        return f"{prefix}_{timestamp}.nbib"
