"""
支付服务 - 集成Stripe支付系统
"""
try:
    import stripe
    STRIPE_AVAILABLE = True
except ImportError:
    stripe = None
    STRIPE_AVAILABLE = False

import os
import logging
from typing import Dict, Any, Optional
from datetime import datetime
import sqlite3
from models.user import user_manager

logger = logging.getLogger(__name__)

class PaymentService:
    """支付服务类"""
    
    def __init__(self):
        self.stripe_secret_key = os.getenv('STRIPE_SECRET_KEY')
        self.stripe_publishable_key = os.getenv('STRIPE_PUBLISHABLE_KEY')
        
        if self.stripe_secret_key and STRIPE_AVAILABLE:
            stripe.api_key = self.stripe_secret_key
            logger.info("Stripe支付服务已初始化")
        else:
            logger.warning("Stripe密钥未配置或模块不可用，支付功能将不可用")
    
    def create_checkout_session(self, user_id: int, price_id: str, 
                              success_url: str, cancel_url: str) -> Optional[Dict[str, Any]]:
        """创建Stripe结账会话"""
        if not self.stripe_secret_key or not STRIPE_AVAILABLE:
            return None
        
        try:
            user = user_manager.get_user_by_id(user_id)
            if not user:
                return None
            
            session = stripe.checkout.Session.create(
                payment_method_types=['card'],
                line_items=[{
                    'price': price_id,
                    'quantity': 1,
                }],
                mode='subscription',
                customer_email=user.email,
                success_url=success_url,
                cancel_url=cancel_url,
                metadata={
                    'user_id': str(user_id)
                }
            )
            
            return {
                'session_id': session.id,
                'url': session.url
            }
            
        except Exception as e:
            logger.error(f"创建结账会话失败: {e}")
            return None
    
    def create_one_time_payment(self, user_id: int, amount: int, currency: str = 'usd',
                              success_url: str = None, cancel_url: str = None) -> Optional[Dict[str, Any]]:
        """创建一次性支付会话"""
        if not self.stripe_secret_key or not STRIPE_AVAILABLE:
            return None
        
        try:
            user = user_manager.get_user_by_id(user_id)
            if not user:
                return None
            
            session = stripe.checkout.Session.create(
                payment_method_types=['card'],
                line_items=[{
                    'price_data': {
                        'currency': currency,
                        'unit_amount': amount,
                        'product_data': {
                            'name': 'NNScholar Premium Subscription',
                            'description': 'Premium access to NNScholar academic research tools',
                        },
                    },
                    'quantity': 1,
                }],
                mode='payment',
                customer_email=user.email,
                success_url=success_url or 'https://nnscholar.com/success',
                cancel_url=cancel_url or 'https://nnscholar.com/cancel',
                metadata={
                    'user_id': str(user_id),
                    'type': 'premium_subscription'
                }
            )
            
            return {
                'session_id': session.id,
                'url': session.url
            }
            
        except Exception as e:
            logger.error(f"创建一次性支付会话失败: {e}")
            return None
    
    def verify_webhook(self, payload: bytes, signature: str) -> Optional[Dict[str, Any]]:
        """验证Stripe webhook"""
        if not self.stripe_secret_key or not STRIPE_AVAILABLE:
            return None
        
        try:
            endpoint_secret = os.getenv('STRIPE_WEBHOOK_SECRET')
            if not endpoint_secret:
                logger.warning("Stripe webhook密钥未配置")
                return None
            
            event = stripe.Webhook.construct_event(
                payload, signature, endpoint_secret
            )
            return event
            
        except Exception as e:
            logger.error(f"验证webhook失败: {e}")
            return None
    
    def handle_payment_success(self, event: Dict[str, Any]) -> bool:
        """处理支付成功"""
        if not STRIPE_AVAILABLE:
            logger.warning("Stripe模块不可用，无法处理支付成功")
            return False
            
        try:
            session = event['data']['object']
            user_id = int(session['metadata']['user_id'])
            
            # 更新用户订阅
            if session['mode'] == 'subscription':
                # 订阅模式
                subscription_id = session['subscription']
                subscription = stripe.Subscription.retrieve(subscription_id)
                
                # 计算订阅结束时间
                current_period_end = datetime.fromtimestamp(subscription['current_period_end'])
                months = 1  # 默认按月订阅
                
                user_manager.update_subscription(user_id, 'premium', months)
                
            else:
                # 一次性支付模式
                months = 1  # 默认1个月
                user_manager.update_subscription(user_id, 'premium', months)
            
            # 记录支付
            self._record_payment(
                user_id=user_id,
                stripe_payment_id=session['id'],
                amount=session['amount_total'],
                currency=session['currency'],
                status='completed',
                subscription_type='premium',
                subscription_months=1
            )
            
            logger.info(f"用户 {user_id} 支付成功，已升级为premium")
            return True
            
        except Exception as e:
            logger.error(f"处理支付成功失败: {e}")
            return False
    
    def handle_subscription_cancelled(self, event: Dict[str, Any]) -> bool:
        """处理订阅取消"""
        if not STRIPE_AVAILABLE:
            logger.warning("Stripe模块不可用，无法处理订阅取消")
            return False
            
        try:
            subscription = event['data']['object']
            customer_id = subscription['customer']
            
            # 获取用户ID
            customer = stripe.Customer.retrieve(customer_id)
            user_id = int(customer['metadata'].get('user_id', 0))
            
            if user_id:
                # 更新用户订阅状态
                user_manager.update_subscription(user_id, 'free', 0)
                logger.info(f"用户 {user_id} 订阅已取消")
            
            return True
            
        except Exception as e:
            logger.error(f"处理订阅取消失败: {e}")
            return False
    
    def get_subscription_status(self, user_id: int) -> Dict[str, Any]:
        """获取用户订阅状态"""
        try:
            user = user_manager.get_user_by_id(user_id)
            if not user:
                return {'error': '用户不存在'}
            
            return {
                'subscription_type': user.subscription_type,
                'is_premium': user.is_premium,
                'subscription_end': user.subscription_end.isoformat() if user.subscription_end else None,
                'api_calls_count': user.api_calls_count,
                'monthly_api_limit': user.monthly_api_limit,
                'can_make_api_call': user.can_make_api_call
            }
            
        except Exception as e:
            logger.error(f"获取订阅状态失败: {e}")
            return {'error': str(e)}
    
    def _record_payment(self, user_id: int, stripe_payment_id: str, amount: int,
                       currency: str, status: str, subscription_type: str,
                       subscription_months: int):
        """记录支付信息"""
        try:
            conn = sqlite3.connect(user_manager.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO payments (user_id, stripe_payment_id, amount, currency, 
                                    status, subscription_type, subscription_months)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (user_id, stripe_payment_id, amount, currency, status,
                  subscription_type, subscription_months))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"记录支付信息失败: {e}")
    
    def get_payment_history(self, user_id: int) -> list:
        """获取用户支付历史"""
        try:
            conn = sqlite3.connect(user_manager.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT id, stripe_payment_id, amount, currency, status,
                       subscription_type, subscription_months, created_at
                FROM payments
                WHERE user_id = ?
                ORDER BY created_at DESC
            ''', (user_id,))
            
            payments = []
            for row in cursor.fetchall():
                payments.append({
                    'id': row[0],
                    'stripe_payment_id': row[1],
                    'amount': row[2],
                    'currency': row[3],
                    'status': row[4],
                    'subscription_type': row[5],
                    'subscription_months': row[6],
                    'created_at': row[7]
                })
            
            conn.close()
            return payments
            
        except Exception as e:
            logger.error(f"获取支付历史失败: {e}")
            return []

# 全局支付服务实例
payment_service = PaymentService()
