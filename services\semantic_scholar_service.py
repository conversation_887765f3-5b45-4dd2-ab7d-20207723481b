"""Semantic Scholar API service for literature search."""

import requests
import json
from typing import List, Dict, Any, Optional, Tuple
import time
import logging
from concurrent.futures import ThreadPoolExecutor, as_completed
from urllib.parse import quote, urlencode
import re

from config.api_config import api_config
from models.paper import Paper, PaperDetail
from utils.text_processing import preprocess_text, extract_keywords
from utils.cache_utils import cache_result, RateLimiter

logger = logging.getLogger(__name__)


class SemanticScholarService:
    """Service for interacting with Semantic Scholar API."""
    
    def __init__(self):
        self.base_url = "https://api.semanticscholar.org/graph/v1"
        self.rate_limiter = RateLimiter(
            max_requests=100,  # Semantic Scholar allows 100 requests per 5 minutes
            window_seconds=300  # 5 minutes
        )
        self.session = requests.Session()
        
        # Set default headers
        self.session.headers.update({
            'User-Agent': 'NNScholar/1.0 (https://github.com/nnscholar)',
            'Content-Type': 'application/json'
        })

        # Add API key if available
        if hasattr(api_config, 'SEMANTIC_SCHOLAR_API_KEY') and api_config.SEMANTIC_SCHOLAR_API_KEY:
            self.session.headers['x-api-key'] = api_config.SEMANTIC_SCHOLAR_API_KEY

        # 默认返回字段，参考优秀脚本的字段设置
        self.default_fields = [
            'paperId', 'title', 'authors', 'year', 'abstract', 'venue',
            'isOpenAccess', 'openAccessPdf', 'url', 'citationCount',
            'influentialCitationCount', 'externalIds', 'fieldsOfStudy',
            'publicationTypes', 'publicationDate'
        ]
    
    def search_papers(self, query: str, max_results: int = 100,
                     offset: int = 0, sort: str = 'relevance',
                     year_start: int = None, year_end: int = None,
                     fields: List[str] = None) -> Tuple[List[Dict], int]:
        """Search Semantic Scholar for papers with enhanced parameters.

        Args:
            query: Search query
            max_results: Maximum number of results
            offset: Starting position for results
            sort: Sort order ('relevance', 'citationCount', 'publicationDate')
            year_start: Starting year for filtering
            year_end: Ending year for filtering
            fields: Custom fields to return

        Returns:
            Tuple of (list of paper data, total count)
        """
        try:
            # 使用分页方式获取更多结果，参考优秀脚本的方法
            all_papers = []
            current_offset = offset

            while len(all_papers) < max_results:
                # Wait for rate limiting
                self.rate_limiter.wait_if_needed()

                # 计算本次请求的数量
                batch_size = min(100, max_results - len(all_papers))  # API limit per request

                # Prepare search parameters
                params = {
                    'query': query,
                    'limit': batch_size,
                    'offset': current_offset,
                    'fields': ','.join(fields or self.default_fields)
                }

                # 添加年份过滤
                if year_start:
                    year_filter = f"{year_start}-{year_end or ''}"
                    params['year'] = year_filter
            
                logger.info(f"Searching Semantic Scholar batch: offset={current_offset}, limit={batch_size}")

                # Make API request
                url = f"{self.base_url}/paper/search"
                response = self.session.get(url, params=params, timeout=30)

                if response.status_code != 200:
                    logger.error(f"API error: {response.status_code}, {response.text}")
                    break

                # Parse JSON response
                data = response.json()
                batch_papers = self._parse_search_response(data)

                if not batch_papers:
                    logger.info("No more papers found, stopping pagination")
                    break

                all_papers.extend(batch_papers)
                current_offset += len(batch_papers)

                # 如果返回的数量少于请求的数量，说明已经到最后一页
                if len(batch_papers) < batch_size:
                    logger.info("Reached last page of results")
                    break

            # Get total count (Semantic Scholar doesn't always provide this)
            total_count = data.get('total', len(all_papers)) if 'data' in locals() else len(all_papers)

            # 限制返回结果数量
            final_papers = all_papers[:max_results]

            logger.info(f"Found {len(final_papers)} papers from Semantic Scholar (total available: {total_count})")
            return final_papers, total_count
            
        except Exception as e:
            logger.error(f"Error searching Semantic Scholar: {str(e)}")
            return [], 0
    
    def _parse_search_response(self, data: Dict) -> List[Dict]:
        """Parse Semantic Scholar search response."""
        papers = []
        
        if 'data' not in data:
            return papers
        
        for paper_data in data['data']:
            try:
                parsed_paper = self._parse_paper(paper_data)
                if parsed_paper:
                    papers.append(parsed_paper)
            except Exception as e:
                logger.warning(f"Error parsing Semantic Scholar paper: {str(e)}")
                continue
        
        return papers
    
    def _parse_paper(self, paper_data: Dict) -> Optional[Dict]:
        """Parse a single Semantic Scholar paper."""
        try:
            # Extract basic information
            paper_id = paper_data.get('paperId', '')
            title = paper_data.get('title', '').strip()
            abstract = paper_data.get('abstract', '').strip() if paper_data.get('abstract') else ''
            
            # Extract authors
            authors = []
            author_data = paper_data.get('authors', [])
            for author in author_data:
                if isinstance(author, dict) and 'name' in author:
                    authors.append(author['name'])
                elif isinstance(author, str):
                    authors.append(author)
            
            # Extract publication information
            year = paper_data.get('year', '')
            pub_date = paper_data.get('publicationDate', '')
            
            # Extract journal/venue information
            journal = ''
            venue_data = paper_data.get('venue')
            journal_data = paper_data.get('journal')
            
            if venue_data:
                journal = venue_data
            elif journal_data:
                if isinstance(journal_data, dict):
                    journal = journal_data.get('name', '')
                else:
                    journal = str(journal_data)
            
            # Extract citation information
            citation_count = paper_data.get('citationCount', 0)
            influential_citation_count = paper_data.get('influentialCitationCount', 0)
            
            # Extract external IDs
            external_ids = paper_data.get('externalIds', {})
            doi = external_ids.get('DOI', '') if external_ids else ''
            pmid = external_ids.get('PubMed', '') if external_ids else ''
            arxiv_id = external_ids.get('ArXiv', '') if external_ids else ''
            
            # Extract fields of study
            fields_of_study = paper_data.get('fieldsOfStudy', [])
            if not isinstance(fields_of_study, list):
                fields_of_study = []
            
            # Extract publication types
            pub_types = paper_data.get('publicationTypes', [])
            if not isinstance(pub_types, list):
                pub_types = []
            
            # Extract URLs
            url = paper_data.get('url', '')
            pdf_url = ''
            open_access_pdf = paper_data.get('openAccessPdf')
            if open_access_pdf and isinstance(open_access_pdf, dict):
                pdf_url = open_access_pdf.get('url', '')
            
            # Create paper data structure
            parsed_paper = {
                'id': paper_id,
                'semantic_scholar_id': paper_id,
                'title': title,
                'abstract': abstract,
                'authors': authors,
                'author_list': ', '.join(authors),
                'publication_date': pub_date,
                'year': str(year) if year else '',
                'journal': journal,
                'citation_count': citation_count,
                'influential_citation_count': influential_citation_count,
                'fields_of_study': fields_of_study,
                'field_list': ', '.join(fields_of_study),
                'publication_types': pub_types,
                'doi': doi,
                'pmid': pmid,
                'arxiv_id': arxiv_id,
                'url': url,
                'pdf_url': pdf_url,
                'source': 'semantic_scholar',
                'impact_factor': 'N/A',  # Semantic Scholar doesn't provide impact factors
                'jcr_quartile': 'N/A',
                'cas_quartile': 'N/A'
            }
            
            return parsed_paper
            
        except Exception as e:
            logger.error(f"Error parsing Semantic Scholar paper: {str(e)}")
            return None
    
    def get_paper_details(self, paper_id: str) -> Optional[Dict]:
        """Get detailed information for a specific Semantic Scholar paper."""
        try:
            # Wait for rate limiting
            self.rate_limiter.wait_if_needed()
            
            url = f"{self.base_url}/paper/{paper_id}"
            params = {
                'fields': 'paperId,title,abstract,authors,year,publicationDate,journal,citationCount,influentialCitationCount,url,openAccessPdf,externalIds,fieldsOfStudy,publicationTypes,venue,references,citations'
            }
            
            response = self.session.get(url, params=params, timeout=30)
            response.raise_for_status()
            
            paper_data = response.json()
            return self._parse_paper(paper_data)
            
        except Exception as e:
            logger.error(f"Error getting Semantic Scholar paper details for {paper_id}: {str(e)}")
            return None
    
    def get_paper_by_doi(self, doi: str) -> Optional[Dict]:
        """Get paper information by DOI."""
        try:
            # Wait for rate limiting
            self.rate_limiter.wait_if_needed()
            
            url = f"{self.base_url}/paper/DOI:{doi}"
            params = {
                'fields': 'paperId,title,abstract,authors,year,publicationDate,journal,citationCount,influentialCitationCount,url,openAccessPdf,externalIds,fieldsOfStudy,publicationTypes,venue'
            }
            
            response = self.session.get(url, params=params, timeout=30)
            response.raise_for_status()
            
            paper_data = response.json()
            return self._parse_paper(paper_data)
            
        except Exception as e:
            logger.error(f"Error getting Semantic Scholar paper by DOI {doi}: {str(e)}")
            return None
    
    def get_citations(self, paper_id: str, limit: int = 100) -> List[Dict]:
        """Get papers that cite the given paper."""
        try:
            # Wait for rate limiting
            self.rate_limiter.wait_if_needed()
            
            url = f"{self.base_url}/paper/{paper_id}/citations"
            params = {
                'fields': 'paperId,title,abstract,authors,year,publicationDate,journal,citationCount,url',
                'limit': min(limit, 1000)  # API limit
            }
            
            response = self.session.get(url, params=params, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            citations = []
            
            for citation_data in data.get('data', []):
                citing_paper = citation_data.get('citingPaper')
                if citing_paper:
                    parsed_paper = self._parse_paper(citing_paper)
                    if parsed_paper:
                        citations.append(parsed_paper)
            
            return citations
            
        except Exception as e:
            logger.error(f"Error getting citations for {paper_id}: {str(e)}")
            return []
    
    def get_references(self, paper_id: str, limit: int = 100) -> List[Dict]:
        """Get papers referenced by the given paper."""
        try:
            # Wait for rate limiting
            self.rate_limiter.wait_if_needed()
            
            url = f"{self.base_url}/paper/{paper_id}/references"
            params = {
                'fields': 'paperId,title,abstract,authors,year,publicationDate,journal,citationCount,url',
                'limit': min(limit, 1000)  # API limit
            }
            
            response = self.session.get(url, params=params, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            references = []
            
            for ref_data in data.get('data', []):
                cited_paper = ref_data.get('citedPaper')
                if cited_paper:
                    parsed_paper = self._parse_paper(cited_paper)
                    if parsed_paper:
                        references.append(parsed_paper)
            
            return references
            
        except Exception as e:
            logger.error(f"Error getting references for {paper_id}: {str(e)}")
            return []

    def export_to_csv(self, papers: List[Dict], filename: str) -> bool:
        """导出文献到 CSV 文件，参考优秀脚本的导出方法"""
        try:
            import csv

            # 定义 CSV 字段
            csv_fields = [
                'paperId', 'title', 'authors', 'year', 'abstract', 'venue',
                'citationCount', 'influentialCitationCount', 'isOpenAccess',
                'openAccessPdf', 'url', 'doi', 'pmid', 'arxiv_id', 'field_list'
            ]

            with open(filename, 'w', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=csv_fields)
                writer.writeheader()

                for paper in papers:
                    # 处理作者列表
                    authors_str = '; '.join([
                        author.get('name', 'Unknown')
                        for author in paper.get('authors', [])
                    ])

                    # 处理开放获取 PDF
                    pdf_url = ''
                    if paper.get('openAccessPdf') and isinstance(paper['openAccessPdf'], dict):
                        pdf_url = paper['openAccessPdf'].get('url', '')

                    # 准备行数据
                    row_data = {
                        'paperId': paper.get('semantic_scholar_id', ''),
                        'title': paper.get('title', ''),
                        'authors': authors_str,
                        'year': paper.get('year', ''),
                        'abstract': paper.get('abstract', ''),
                        'venue': paper.get('journal', ''),
                        'citationCount': paper.get('citation_count', 0),
                        'influentialCitationCount': paper.get('influential_citation_count', 0),
                        'isOpenAccess': paper.get('isOpenAccess', False),
                        'openAccessPdf': pdf_url,
                        'url': paper.get('url', ''),
                        'doi': paper.get('doi', ''),
                        'pmid': paper.get('pmid', ''),
                        'arxiv_id': paper.get('arxiv_id', ''),
                        'field_list': paper.get('field_list', '')
                    }

                    writer.writerow(row_data)

            logger.info(f"Successfully exported {len(papers)} papers to {filename}")
            return True

        except Exception as e:
            logger.error(f"Error exporting to CSV: {str(e)}")
            return False

    def export_to_json(self, papers: List[Dict], filename: str) -> bool:
        """导出文献到 JSON 文件"""
        try:
            import json

            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(papers, f, ensure_ascii=False, indent=2)

            logger.info(f"Successfully exported {len(papers)} papers to {filename}")
            return True

        except Exception as e:
            logger.error(f"Error exporting to JSON: {str(e)}")
            return False

    def export_to_bibtex(self, papers: List[Dict], filename: str) -> bool:
        """导出文献到 BibTeX 文件，参考优秀脚本的方法"""
        try:
            def _escape_bibtex(s: str) -> str:
                """转义 BibTeX 特殊字符"""
                if not s:
                    return ""
                return s.replace("&", r"\&").replace("_", r"\_").replace("%", r"\%")

            with open(filename, 'w', encoding='utf-8') as f:
                for paper in papers:
                    paper_id = paper.get('semantic_scholar_id', 'unknown')
                    title = _escape_bibtex(paper.get('title', ''))
                    year = paper.get('year', 'n.d.')
                    doi = paper.get('doi', '')
                    url = paper.get('url', '')
                    venue = _escape_bibtex(paper.get('journal', ''))

                    # 处理作者
                    authors = paper.get('authors', [])
                    if isinstance(authors, list) and authors:
                        author_names = [
                            author.get('name', 'Unknown') if isinstance(author, dict) else str(author)
                            for author in authors
                        ]
                        authors_str = ' and '.join(author_names)
                    else:
                        authors_str = 'Unknown'

                    # 写入 BibTeX 条目
                    f.write(f"@article{{{paper_id},\n")
                    f.write(f"  title={{{title}}},\n")
                    f.write(f"  author={{{authors_str}}},\n")
                    f.write(f"  year={{{year}}},\n")
                    if venue:
                        f.write(f"  journal={{{venue}}},\n")
                    if doi:
                        f.write(f"  DOI={{{doi}}},\n")
                    if url:
                        f.write(f"  url={{{url}}},\n")
                    f.write("}\n\n")

            logger.info(f"Successfully exported {len(papers)} papers to {filename}")
            return True

        except Exception as e:
            logger.error(f"Error exporting to BibTeX: {str(e)}")
            return False


# Create global instance
semantic_scholar_service = SemanticScholarService()
