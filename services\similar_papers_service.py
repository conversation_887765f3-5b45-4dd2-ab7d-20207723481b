#!/usr/bin/env python3
"""
相似论文推荐服务

基于PubMed数据的智能相似论文推荐系统：
1. 根据DOI/标题/PMID获取种子文献信息
2. 提取关键词和主题信息
3. 生成相关检索式
4. 在PubMed搜索相关文献
5. 计算相关度，推荐相关度>70%的前25篇文献
"""

import logging
import re
from typing import List, Dict, Any, Set, Tuple
from datetime import datetime
from collections import Counter
import math

logger = logging.getLogger(__name__)


class SimilarPapersService:
    """相似论文推荐服务"""
    
    def __init__(self):
        self.max_recommendations = 25  # 最大推荐数量
        self.similarity_threshold = 0.7  # 相似度阈值
        self.max_search_results = 100  # 最大搜索结果数
    
    def recommend_similar_papers(self, seed_paper: Dict[str, Any], 
                               pubmed_service) -> Dict[str, Any]:
        """
        推荐相似论文
        
        Args:
            seed_paper: 种子文献信息
            pubmed_service: PubMed服务实例
            
        Returns:
            包含推荐文献列表的结果
        """
        try:
            logger.info(f"开始推荐相似论文，种子文献: {seed_paper.get('title', '')}")
            
            # 1. 提取种子文献的关键信息
            keywords = self._extract_keywords(seed_paper)
            mesh_terms = self._extract_mesh_terms(seed_paper)
            
            # 2. 生成检索式
            search_queries = self._generate_search_queries(keywords, mesh_terms, seed_paper)
            
            # 3. 在PubMed搜索相关文献
            candidate_papers = []
            for query in search_queries:
                try:
                    pmids, _ = pubmed_service.search_papers(query, max_results=50)
                    if pmids:
                        papers = pubmed_service.fetch_paper_details(pmids)
                        candidate_papers.extend(papers)
                except Exception as e:
                    logger.warning(f"搜索查询失败 '{query}': {e}")
                    continue
            
            # 4. 去重并排除种子文献
            unique_papers = self._deduplicate_papers(candidate_papers, seed_paper)
            
            # 5. 计算相关度
            scored_papers = self._calculate_similarity_scores(unique_papers, seed_paper)
            
            # 6. 筛选高相关度文献
            high_similarity_papers = [
                paper for paper in scored_papers 
                if paper.get('similarity_score', 0) >= self.similarity_threshold
            ]
            
            # 7. 按相关度排序并取前25篇
            recommendations = sorted(
                high_similarity_papers,
                key=lambda x: x.get('similarity_score', 0),
                reverse=True
            )[:self.max_recommendations]
            
            logger.info(f"推荐完成，找到 {len(recommendations)} 篇高相关度文献")
            
            return {
                'success': True,
                'seed_paper': seed_paper,
                'recommendations': recommendations,
                'total_candidates': len(unique_papers),
                'high_similarity_count': len(high_similarity_papers),
                'search_queries': search_queries,
                'statistics': {
                    'similarity_threshold': self.similarity_threshold,
                    'max_recommendations': self.max_recommendations,
                    'avg_similarity': sum(p.get('similarity_score', 0) for p in recommendations) / len(recommendations) if recommendations else 0
                }
            }
            
        except Exception as e:
            logger.error(f"推荐相似论文失败: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'seed_paper': seed_paper
            }
    
    def _extract_keywords(self, paper: Dict[str, Any]) -> List[str]:
        """提取文献关键词"""
        keywords = []
        
        # 从标题提取关键词
        title = paper.get('title', '')
        if title:
            title_keywords = self._extract_keywords_from_text(title)
            keywords.extend(title_keywords)
        
        # 从摘要提取关键词
        abstract = paper.get('abstract', '')
        if abstract:
            abstract_keywords = self._extract_keywords_from_text(abstract)
            keywords.extend(abstract_keywords)
        
        # 从已有的关键词字段
        if paper.get('keywords'):
            if isinstance(paper['keywords'], list):
                keywords.extend(paper['keywords'])
            elif isinstance(paper['keywords'], str):
                keywords.extend([kw.strip() for kw in paper['keywords'].split(',')])
        
        # 去重并过滤
        unique_keywords = list(set(keywords))
        filtered_keywords = self._filter_keywords(unique_keywords)
        
        return filtered_keywords[:20]  # 限制关键词数量
    
    def _extract_keywords_from_text(self, text: str) -> List[str]:
        """从文本中提取关键词"""
        if not text:
            return []
        
        # 清理文本
        text = re.sub(r'[^\w\s-]', ' ', text.lower())
        words = text.split()
        
        # 过滤停用词和短词
        stop_words = {
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 
            'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'being',
            'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could',
            'should', 'may', 'might', 'can', 'this', 'that', 'these', 'those',
            'we', 'us', 'our', 'ours', 'you', 'your', 'yours', 'he', 'him', 'his',
            'she', 'her', 'hers', 'it', 'its', 'they', 'them', 'their', 'theirs'
        }
        
        keywords = []
        for word in words:
            if (len(word) >= 3 and 
                word not in stop_words and 
                not word.isdigit()):
                keywords.append(word)
        
        # 统计词频，返回高频词
        word_counts = Counter(keywords)
        return [word for word, count in word_counts.most_common(10)]
    
    def _extract_mesh_terms(self, paper: Dict[str, Any]) -> List[str]:
        """提取MeSH主题词"""
        mesh_terms = []
        
        # 从paper的mesh_terms字段获取
        if paper.get('mesh_terms'):
            if isinstance(paper['mesh_terms'], list):
                mesh_terms.extend(paper['mesh_terms'])
            elif isinstance(paper['mesh_terms'], str):
                mesh_terms.extend([term.strip() for term in paper['mesh_terms'].split(';')])
        
        return mesh_terms[:10]  # 限制MeSH词数量
    
    def _filter_keywords(self, keywords: List[str]) -> List[str]:
        """过滤关键词"""
        filtered = []
        
        for keyword in keywords:
            # 过滤太短或太长的词
            if 3 <= len(keyword) <= 30:
                # 过滤纯数字
                if not keyword.isdigit():
                    # 过滤常见无意义词
                    if keyword not in ['study', 'analysis', 'research', 'method', 'result', 'conclusion']:
                        filtered.append(keyword)
        
        return filtered
    
    def _generate_search_queries(self, keywords: List[str], mesh_terms: List[str], 
                               seed_paper: Dict[str, Any]) -> List[str]:
        """生成PubMed检索式"""
        queries = []
        
        # 1. 基于关键词的查询
        if keywords:
            # 主要关键词组合
            main_keywords = keywords[:5]
            if len(main_keywords) >= 2:
                keyword_query = ' AND '.join([f'"{kw}"[Title/Abstract]' for kw in main_keywords[:3]])
                queries.append(keyword_query)
            
            # 单个重要关键词
            for keyword in main_keywords[:3]:
                queries.append(f'"{keyword}"[Title/Abstract]')
        
        # 2. 基于MeSH词的查询
        if mesh_terms:
            for mesh_term in mesh_terms[:3]:
                queries.append(f'"{mesh_term}"[MeSH Terms]')
        
        # 3. 基于期刊的查询
        journal = seed_paper.get('journal', '')
        if journal and len(keywords) > 0:
            journal_query = f'"{journal}"[Journal] AND "{keywords[0]}"[Title/Abstract]'
            queries.append(journal_query)
        
        # 4. 基于作者的查询
        authors = seed_paper.get('authors', [])
        if authors and len(keywords) > 0:
            first_author = authors[0] if isinstance(authors, list) else str(authors).split(',')[0]
            if first_author:
                author_query = f'"{first_author}"[Author] AND "{keywords[0]}"[Title/Abstract]'
                queries.append(author_query)
        
        # 去重
        unique_queries = list(set(queries))
        
        logger.info(f"生成了 {len(unique_queries)} 个检索式")
        return unique_queries[:5]  # 限制查询数量

    def _deduplicate_papers(self, papers: List[Dict[str, Any]],
                          seed_paper: Dict[str, Any]) -> List[Dict[str, Any]]:
        """去重文献并排除种子文献"""
        seen_pmids = set()
        seen_titles = set()
        unique_papers = []

        # 种子文献的标识
        seed_pmid = str(seed_paper.get('pmid', ''))
        seed_title = seed_paper.get('title', '').strip().lower()

        for paper in papers:
            pmid = str(paper.get('pmid', ''))
            title = paper.get('title', '').strip().lower()

            # 跳过种子文献
            if pmid == seed_pmid or title == seed_title:
                continue

            # 去重
            if pmid and pmid not in seen_pmids:
                seen_pmids.add(pmid)
                unique_papers.append(paper)
            elif title and title not in seen_titles:
                seen_titles.add(title)
                unique_papers.append(paper)

        return unique_papers

    def _calculate_similarity_scores(self, papers: List[Dict[str, Any]],
                                   seed_paper: Dict[str, Any]) -> List[Dict[str, Any]]:
        """计算相似度分数"""
        scored_papers = []

        # 提取种子文献特征
        seed_keywords = set(self._extract_keywords(seed_paper))
        seed_mesh = set(self._extract_mesh_terms(seed_paper))
        seed_journal = seed_paper.get('journal', '').lower()
        seed_authors = set(str(author).lower() for author in seed_paper.get('authors', []))
        seed_year = self._extract_year(seed_paper.get('pub_year', ''))

        for paper in papers:
            try:
                # 提取候选文献特征
                paper_keywords = set(self._extract_keywords(paper))
                paper_mesh = set(self._extract_mesh_terms(paper))
                paper_journal = paper.get('journal', '').lower()
                paper_authors = set(str(author).lower() for author in paper.get('authors', []))
                paper_year = self._extract_year(paper.get('pub_year', ''))

                # 计算各项相似度
                keyword_sim = self._jaccard_similarity(seed_keywords, paper_keywords)
                mesh_sim = self._jaccard_similarity(seed_mesh, paper_mesh)
                journal_sim = 1.0 if seed_journal == paper_journal else 0.0
                author_sim = self._jaccard_similarity(seed_authors, paper_authors)

                # 时间相似度（年份越接近相似度越高）
                time_sim = 0.0
                if seed_year and paper_year:
                    year_diff = abs(seed_year - paper_year)
                    time_sim = max(0, 1 - year_diff / 10)  # 10年内的文献有时间相似度

                # 综合相似度计算
                similarity_score = (
                    keyword_sim * 0.4 +      # 关键词相似度权重40%
                    mesh_sim * 0.3 +         # MeSH词相似度权重30%
                    journal_sim * 0.15 +     # 期刊相似度权重15%
                    author_sim * 0.1 +       # 作者相似度权重10%
                    time_sim * 0.05          # 时间相似度权重5%
                )

                # 添加相似度信息到文献
                paper_copy = paper.copy()
                paper_copy['similarity_score'] = similarity_score
                paper_copy['similarity_details'] = {
                    'keyword_similarity': keyword_sim,
                    'mesh_similarity': mesh_sim,
                    'journal_similarity': journal_sim,
                    'author_similarity': author_sim,
                    'time_similarity': time_sim
                }

                scored_papers.append(paper_copy)

            except Exception as e:
                logger.warning(f"计算文献相似度失败: {e}")
                continue

        return scored_papers

    def _jaccard_similarity(self, set1: Set[str], set2: Set[str]) -> float:
        """计算Jaccard相似度"""
        if not set1 and not set2:
            return 0.0
        if not set1 or not set2:
            return 0.0

        intersection = len(set1.intersection(set2))
        union = len(set1.union(set2))

        return intersection / union if union > 0 else 0.0

    def _extract_year(self, year_str: str) -> int:
        """提取年份"""
        if not year_str:
            return None

        # 尝试提取4位数字年份
        year_match = re.search(r'\b(19|20)\d{2}\b', str(year_str))
        if year_match:
            return int(year_match.group())

        return None


# 全局服务实例
similar_papers_service = SimilarPapersService()
