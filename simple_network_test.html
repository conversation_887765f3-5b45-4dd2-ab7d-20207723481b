<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单网络测试</title>
    <script src="https://unpkg.com/vis-network/standalone/umd/vis-network.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        
        #network {
            width: 100%;
            height: 600px;
            border: 1px solid #ddd;
            background: #f9f9f9;
        }
        
        .info {
            margin-bottom: 20px;
            padding: 10px;
            background: #e7f3ff;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="info">
        <h2>简单网络图谱测试</h2>
        <p>这是一个简单的vis.js网络图谱测试页面</p>
    </div>
    
    <div id="network"></div>
    
    <script>
        console.log('开始初始化网络图谱');
        
        // 检查vis.js是否加载
        if (typeof vis === 'undefined') {
            console.error('vis.js未加载');
            document.getElementById('network').innerHTML = '<p style="text-align: center; padding: 50px;">vis.js库加载失败</p>';
        } else {
            console.log('vis.js已加载');
            
            // 创建测试数据
            const nodes = new vis.DataSet([
                {id: 1, label: '中心文献', color: '#ff6b6b', size: 30},
                {id: 2, label: '引用文献1', color: '#4ecdc4', size: 20},
                {id: 3, label: '引用文献2', color: '#4ecdc4', size: 20},
                {id: 4, label: '被引文献1', color: '#45b7d1', size: 25},
                {id: 5, label: '被引文献2', color: '#45b7d1', size: 25}
            ]);
            
            const edges = new vis.DataSet([
                {from: 2, to: 1},
                {from: 3, to: 1},
                {from: 1, to: 4},
                {from: 1, to: 5}
            ]);
            
            // 网络配置
            const options = {
                nodes: {
                    shape: 'dot',
                    font: {
                        size: 14,
                        color: '#333333'
                    },
                    borderWidth: 2
                },
                edges: {
                    arrows: {
                        to: {enabled: true, scaleFactor: 1}
                    },
                    color: '#848484',
                    width: 2
                },
                physics: {
                    enabled: true,
                    stabilization: {enabled: true, iterations: 100}
                }
            };
            
            // 创建网络
            const container = document.getElementById('network');
            const data = {nodes: nodes, edges: edges};
            const network = new vis.Network(container, data, options);
            
            console.log('网络图谱创建完成');
            
            // 添加事件监听
            network.on('click', function(params) {
                console.log('点击事件:', params);
            });
        }
    </script>
</body>
</html>
