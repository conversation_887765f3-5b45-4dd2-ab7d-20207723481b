<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NNScholar - 后台管理</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <style>
        :root {
            --primary-color: #8B5CF6;
            --secondary-color: #94A3B8;
            --background-color: #000000;
            --card-background: #111111;
            --text-color: #E2E8F0;
            --border-color: #222222;
            --hover-color: #1A1A1A;
            --card-shadow: 0 4px 6px rgba(0, 0, 0, 0.5);
            --transition-speed: 0.3s;
        }

        body {
            padding-top: 70px;
            background-color: var(--background-color);
            color: var(--text-color);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
        }

        .navbar {
            background: #000000;
            box-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .navbar-brand {
            font-weight: 600;
            font-size: 1.5rem;
            color: white !important;
        }

        .card {
            background-color: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            margin-bottom: 24px;
            box-shadow: var(--card-shadow);
            transition: transform var(--transition-speed);
        }

        .card:hover {
            transform: translateY(-2px);
        }

        .card-title {
            color: var(--primary-color);
            font-weight: 600;
            margin-bottom: 1.5rem;
            font-size: 1.25rem;
        }

        .stats-value {
            font-size: 2rem;
            font-weight: 600;
            color: var(--primary-color);
        }

        .stats-label {
            color: var(--secondary-color);
            font-size: 0.9rem;
        }

        .chart-container {
            height: 400px;
            width: 100%;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark fixed-top">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-chart-line me-2"></i>
                NNScholar 后台管理
            </a>
        </div>
    </nav>

    <div class="container">
        <div class="row">
            <!-- 总访问量 -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-body text-center">
                        <h5 class="card-title">
                            <i class="fas fa-users me-2"></i>
                            总访问量
                        </h5>
                        <div class="stats-value" id="total-visits">0</div>
                        <div class="stats-label">累计访问人次</div>
                    </div>
                </div>
            </div>

            <!-- 当前在线 -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-body text-center">
                        <h5 class="card-title">
                            <i class="fas fa-user-clock me-2"></i>
                            当前在线
                        </h5>
                        <div class="stats-value" id="concurrent-users">0</div>
                        <div class="stats-label">实时在线人数</div>
                    </div>
                </div>
            </div>

            <!-- 峰值在线 -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-body text-center">
                        <h5 class="card-title">
                            <i class="fas fa-chart-bar me-2"></i>
                            峰值在线
                        </h5>
                        <div class="stats-value" id="peak-users">0</div>
                        <div class="stats-label">历史最高在线</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 访问量趋势图 -->
        <div class="card mt-4">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="fas fa-chart-line me-2"></i>
                    24小时访问趋势
                </h5>
                <div id="visits-chart" class="chart-container"></div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 初始化图表
        const visitsChart = echarts.init(document.getElementById('visits-chart'));

        // 更新统计数据
        function updateStats() {
            fetch('/api/stats')
                .then(response => response.json())
                .then(data => {
                    // 更新数值
                    document.getElementById('total-visits').textContent = data.total_visits;
                    document.getElementById('concurrent-users').textContent = data.concurrent_users;
                    document.getElementById('peak-users').textContent = data.peak_concurrent_users;

                    // 更新图表
                    const hours = Array.from({length: 24}, (_, i) => {
                        const hour = new Date().getHours() - (23 - i);
                        return `${hour < 0 ? hour + 24 : hour}:00`;
                    });

                    const option = {
                        backgroundColor: getComputedStyle(document.documentElement).getPropertyValue('--card-background').trim(),
                        tooltip: {
                            trigger: 'axis',
                            axisPointer: {
                                type: 'shadow'
                            },
                            textStyle: {
                                color: getComputedStyle(document.documentElement).getPropertyValue('--text-color').trim()
                            }
                        },
                        grid: {
                            left: '3%',
                            right: '4%',
                            bottom: '3%',
                            containLabel: true
                        },
                        xAxis: {
                            type: 'category',
                            data: hours,
                            axisLabel: {
                                color: getComputedStyle(document.documentElement).getPropertyValue('--text-color').trim()
                            },
                            axisLine: {
                                lineStyle: {
                                    color: getComputedStyle(document.documentElement).getPropertyValue('--border-color').trim()
                                }
                            }
                        },
                        yAxis: {
                            type: 'value',
                            name: '访问量',
                            nameTextStyle: {
                                color: getComputedStyle(document.documentElement).getPropertyValue('--text-color').trim()
                            },
                            axisLabel: {
                                color: getComputedStyle(document.documentElement).getPropertyValue('--text-color').trim()
                            },
                            axisLine: {
                                lineStyle: {
                                    color: getComputedStyle(document.documentElement).getPropertyValue('--border-color').trim()
                                }
                            },
                            splitLine: {
                                lineStyle: {
                                    color: getComputedStyle(document.documentElement).getPropertyValue('--border-color').trim()
                                }
                            }
                        },
                        series: [{
                            name: '访问量',
                            type: 'line',
                            data: data.hourly_visits,
                            smooth: true,
                            symbol: 'circle',
                            symbolSize: 8,
                            lineStyle: {
                                width: 3,
                                color: getComputedStyle(document.documentElement).getPropertyValue('--primary-color').trim()
                            },
                            itemStyle: {
                                color: getComputedStyle(document.documentElement).getPropertyValue('--primary-color').trim()
                            },
                            areaStyle: {
                                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                                    offset: 0,
                                    color: getComputedStyle(document.documentElement).getPropertyValue('--primary-color').trim()
                                }, {
                                    offset: 1,
                                    color: 'rgba(139, 92, 246, 0.1)'
                                }])
                            }
                        }]
                    };

                    visitsChart.setOption(option);
                })
                .catch(error => console.error('获取统计数据失败:', error));
        }

        // 页面加载时更新数据
        updateStats();

        // 每30秒更新一次数据
        setInterval(updateStats, 30000);

        // 监听窗口大小变化，调整图表大小
        window.addEventListener('resize', () => visitsChart.resize());
    </script>
</body>
</html> 