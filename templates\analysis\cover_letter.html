<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cover Letter - NNScholar</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2rem;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .header p {
            opacity: 0.9;
            font-size: 1.1rem;
        }

        .content {
            padding: 40px;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-group label {
            display: block;
            font-weight: 600;
            color: #374151;
            margin-bottom: 8px;
            font-size: 1rem;
        }

        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e5e7eb;
            border-radius: 10px;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .form-group input:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            outline: none;
            border-color: #10b981;
            box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
        }

        .form-group textarea {
            resize: vertical;
            min-height: 120px;
        }

        .submit-btn {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            width: 100%;
            transition: all 0.3s ease;
            margin-top: 20px;
        }

        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(16, 185, 129, 0.3);
        }

        .submit-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .result-section {
            margin-top: 30px;
            padding: 20px;
            background: #f8fafc;
            border-radius: 10px;
            border-left: 4px solid #10b981;
            display: none;
        }

        .result-section h3 {
            color: #10b981;
            margin-bottom: 15px;
        }

        .result-content {
            line-height: 1.6;
            color: #374151;
        }

        .loading {
            text-align: center;
            padding: 20px;
            color: #6b7280;
        }

        .back-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.9);
            border: none;
            padding: 10px 15px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            background: white;
            transform: translateY(-1px);
        }

        .export-btn {
            background: linear-gradient(135deg, #059669, #047857);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .export-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(5, 150, 105, 0.3);
        }

        .export-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .input-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        .radio-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 12px;
            margin-top: 8px;
        }

        .radio-item {
            display: flex;
            align-items: center;
            padding: 12px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .radio-item:hover {
            border-color: #10b981;
            background: #f0fdf4;
        }

        .radio-item input[type="radio"] {
            width: auto;
            margin-right: 8px;
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 15px;
            }
            
            .content {
                padding: 20px;
            }
            
            .input-grid {
                grid-template-columns: 1fr;
            }
            
            .radio-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <button class="back-btn" onclick="window.close()">← 返回</button>
    
    <div class="container">
        <div class="header">
            <h1>✉️ Cover Letter</h1>
            <p>只需期刊名、标题、摘要 - 一键生成专业投稿信</p>
        </div>
        
        <div class="content">
            <form id="coverLetterForm">
                <div class="form-group">
                    <label for="targetJournal">📊 目标期刊 *</label>
                    <input type="text" id="targetJournal" required
                           placeholder="请输入目标投稿期刊名称，例如：Nature, Science, Cell">
                </div>

                <div class="form-group">
                    <label for="paperTitle">📝 论文标题 *</label>
                    <input type="text" id="paperTitle" required
                           placeholder="请输入您的论文标题">
                </div>

                <div class="form-group">
                    <label for="paperAbstract">📄 论文摘要 *</label>
                    <textarea id="paperAbstract" required
                              placeholder="请粘贴您的论文摘要，这将帮助生成更准确的投稿信"></textarea>
                </div>

                <button type="submit" class="submit-btn" id="submitBtn">
                    ✉️ 生成专业投稿信
                </button>
            </form>

            <div class="result-section" id="resultSection">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                    <h3>✉️ 投稿信内容</h3>
                    <button id="exportBtn" class="export-btn" style="display: none;" onclick="exportToWord()">
                        📄 导出Word文档
                    </button>
                </div>
                <div class="result-content" id="resultContent"></div>
            </div>
        </div>
    </div>

    <script>
        document.getElementById('coverLetterForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const submitBtn = document.getElementById('submitBtn');
            const resultSection = document.getElementById('resultSection');
            const resultContent = document.getElementById('resultContent');

            // 获取表单数据
            const formData = {
                targetJournal: document.getElementById('targetJournal').value,
                paperTitle: document.getElementById('paperTitle').value,
                paperAbstract: document.getElementById('paperAbstract').value
            };

            if (!formData.paperTitle.trim() || !formData.targetJournal.trim() || !formData.paperAbstract.trim()) {
                alert('请填写目标期刊、论文标题和论文摘要');
                return;
            }

            // 显示加载状态
            submitBtn.disabled = true;
            submitBtn.textContent = '🔄 生成中...';
            resultSection.style.display = 'block';
            resultContent.innerHTML = '<div class="loading">🔄 AI正在为您生成专业的投稿信，请稍候...</div>';

            try {
                // 使用您提供的专业提示词模板
                const prompt = `I want you to act as an academic journal editor. I will provide you with the title and abstract of my manuscript. You need to write a formatted cover letter for submitting the manuscript to the ${formData.targetJournal} journal. You should state that the manuscript has not been considered for publication in any other journal. Briefly introduce the merit of the manuscript and provide a short background to contextualize the importance of the results for a scientific audience.

The title and abstract are as follows:

[Manuscript Title]: ${formData.paperTitle}

[Manuscript Abstract]: ${formData.paperAbstract}

Please generate the cover letter now.

Additionally, consider including suggestions for potential reviewers and ensure to format the letter correctly, including:
- Your name
- Your affiliation
- Your contact information
- The date
- The editor's title (e.g., "The Editor" or "Editor-in-Chief")

Please follow this format structure:

[Your Name]
[Your Affiliation]
[Your Contact Information]
[Date]

The Editor
${formData.targetJournal}

Dear Editor,

We are pleased to submit our manuscript, "[Manuscript Title]", for consideration as a [Article Type, e.g., Research Article, Letter] for publication in *${formData.targetJournal}*.

This manuscript has not been considered for publication in any other journal and is not under consideration by any other journal. All authors have approved this submission.

Our manuscript presents [Briefly introduce the merit of the manuscript] regarding [Briefly summarize the core topic]. The importance of this work lies in [Provide background context that highlights the significance]. We believe that the findings presented in this work will be of significant interest to the broad scientific audience of *${formData.targetJournal}* because [Point out the importance of the results for a scientific audience].

Thank you for your consideration. We look forward to hearing from you soon.

Sincerely,
[Your Name]`;

                // 发送请求
                const response = await fetch('/api/deepseek_analysis', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        prompt: prompt,
                        expert_type: 'Cover Letter专家'
                    })
                });

                const data = await response.json();

                if (data.success) {
                    // 格式化显示结果
                    const formattedResult = data.analysis.replace(/\n/g, '<br>').replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
                    resultContent.innerHTML = formattedResult;

                    // 显示导出按钮并存储数据
                    const exportBtn = document.getElementById('exportBtn');
                    exportBtn.style.display = 'block';

                    // 存储原始内容用于导出
                    window.coverLetterData = {
                        content: data.analysis,
                        journal: formData.targetJournal,
                        title: formData.paperTitle
                    };
                } else {
                    resultContent.innerHTML = `<div style="color: #ef4444;">❌ 生成失败: ${data.error}</div>`;
                    document.getElementById('exportBtn').style.display = 'none';
                }
            } catch (error) {
                console.error('生成错误:', error);
                resultContent.innerHTML = `<div style="color: #ef4444;">❌ 生成过程中出现错误: ${error.message}</div>`;
            } finally {
                submitBtn.disabled = false;
                submitBtn.textContent = '✉️ 生成投稿信';
            }
        });

        // 导出Word文档函数
        async function exportToWord() {
            if (!window.coverLetterData) {
                alert('没有可导出的内容');
                return;
            }

            const exportBtn = document.getElementById('exportBtn');
            const originalText = exportBtn.textContent;

            try {
                exportBtn.disabled = true;
                exportBtn.textContent = '📄 导出中...';

                const response = await fetch('/api/export_cover_letter', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(window.coverLetterData)
                });

                if (response.ok) {
                    // 获取文件名
                    const contentDisposition = response.headers.get('Content-Disposition');
                    let filename = 'cover_letter.docx';
                    if (contentDisposition) {
                        const filenameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/);
                        if (filenameMatch && filenameMatch[1]) {
                            filename = filenameMatch[1].replace(/['"]/g, '');
                        }
                    }

                    // 下载文件
                    const blob = await response.blob();
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.style.display = 'none';
                    a.href = url;
                    a.download = filename;
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(url);
                    document.body.removeChild(a);

                    // 显示成功消息
                    const resultContent = document.getElementById('resultContent');
                    const successMsg = document.createElement('div');
                    successMsg.style.cssText = 'background: #d1fae5; border: 1px solid #10b981; color: #065f46; padding: 12px; border-radius: 8px; margin-top: 15px;';
                    successMsg.innerHTML = '✅ Word文档已成功下载！';
                    resultContent.appendChild(successMsg);

                    // 3秒后移除成功消息
                    setTimeout(() => {
                        if (successMsg.parentNode) {
                            successMsg.parentNode.removeChild(successMsg);
                        }
                    }, 3000);

                } else {
                    const errorData = await response.json();
                    alert(`导出失败: ${errorData.error || '未知错误'}`);
                }
            } catch (error) {
                console.error('导出错误:', error);
                alert(`导出过程中出现错误: ${error.message}`);
            } finally {
                exportBtn.disabled = false;
                exportBtn.textContent = originalText;
            }
        }
    </script>
</body>
</html>
