<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>深度统计分析 - NNScholar</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #8b5cf6, #7c3aed);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2rem;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .header p {
            opacity: 0.9;
            font-size: 1.1rem;
        }

        .content {
            padding: 40px;
        }

        .input-tabs {
            display: flex;
            margin-bottom: 30px;
            border-bottom: 2px solid #e5e7eb;
        }

        .tab-button {
            padding: 12px 24px;
            border: none;
            background: none;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            color: #6b7280;
            border-bottom: 2px solid transparent;
            transition: all 0.3s ease;
        }

        .tab-button.active {
            color: #8b5cf6;
            border-bottom-color: #8b5cf6;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-group label {
            display: block;
            font-weight: 600;
            color: #374151;
            margin-bottom: 8px;
            font-size: 1rem;
        }

        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e5e7eb;
            border-radius: 10px;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .form-group input:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            outline: none;
            border-color: #8b5cf6;
            box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
        }

        .form-group textarea {
            resize: vertical;
            min-height: 120px;
        }

        .input-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        .checkbox-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 12px;
            margin-top: 8px;
        }

        .checkbox-item {
            display: flex;
            align-items: center;
            padding: 12px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .checkbox-item:hover {
            border-color: #8b5cf6;
            background: #f3f4f6;
        }

        .checkbox-item input[type="checkbox"] {
            width: auto;
            margin-right: 8px;
        }

        .file-upload {
            border: 2px dashed #d1d5db;
            border-radius: 10px;
            padding: 30px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: #f9fafb;
        }

        .file-upload:hover {
            border-color: #8b5cf6;
            background: #f3f4f6;
        }

        .file-upload.dragover {
            border-color: #8b5cf6;
            background: #ede9fe;
        }

        .submit-btn {
            background: linear-gradient(135deg, #8b5cf6, #7c3aed);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            width: 100%;
            transition: all 0.3s ease;
            margin-top: 20px;
        }

        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(139, 92, 246, 0.3);
        }

        .submit-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .result-section {
            margin-top: 30px;
            padding: 20px;
            background: #f8fafc;
            border-radius: 10px;
            border-left: 4px solid #8b5cf6;
            display: none;
        }

        .result-section h3 {
            color: #8b5cf6;
            margin-bottom: 15px;
        }

        .result-content {
            line-height: 1.6;
            color: #374151;
        }

        .loading {
            text-align: center;
            padding: 20px;
            color: #6b7280;
        }

        .back-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.9);
            border: none;
            padding: 10px 15px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            background: white;
            transform: translateY(-1px);
        }

        .suggestion-box {
            background: #ede9fe;
            border: 1px solid #c4b5fd;
            border-radius: 8px;
            padding: 12px;
            margin-top: 8px;
            font-size: 14px;
            color: #5b21b6;
        }

        .method-card {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .method-card h4 {
            color: #8b5cf6;
            margin-bottom: 10px;
        }

        .method-card .confidence {
            background: #10b981;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            float: right;
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 15px;
            }
            
            .content {
                padding: 20px;
            }
            
            .input-grid {
                grid-template-columns: 1fr;
            }
            
            .checkbox-grid {
                grid-template-columns: 1fr;
            }

            .input-tabs {
                flex-direction: column;
            }

            .tab-button {
                text-align: left;
                border-bottom: 1px solid #e5e7eb;
                border-right: none;
            }
        }
    </style>
</head>
<body>
    <button class="back-btn" onclick="window.close()">← 返回</button>
    
    <div class="container">
        <div class="header">
            <h1>📊 深度统计分析</h1>
            <p>智能统计方法推荐与分析报告生成系统</p>
        </div>
        
        <div class="content">
            <!-- 输入方式选择标签 -->
            <div class="input-tabs">
                <button class="tab-button active" onclick="switchTab('text')">📝 文本输入</button>
                <button class="tab-button" onclick="switchTab('questionnaire')">📋 结构化问卷</button>
                <button class="tab-button" onclick="switchTab('file')">📁 文件上传</button>
            </div>

            <!-- 文本输入标签页 -->
            <div id="text-tab" class="tab-content active">
                <form id="textAnalysisForm">
                    <div class="form-group">
                        <label for="researchQuestion">🎯 研究问题 *</label>
                        <textarea id="researchQuestion" required 
                                  placeholder="请详细描述您的研究问题，支持Markdown格式。例如：我想研究不同教学方法对学生成绩的影响..."></textarea>
                        <div class="suggestion-box" id="questionSuggestion" style="display: none;"></div>
                    </div>

                    <div class="form-group">
                        <label for="dataDescription">📊 数据描述</label>
                        <textarea id="dataDescription" 
                                  placeholder="请描述您的数据特征，包括样本量、变量类型、数据分布等..."></textarea>
                    </div>

                    <div class="form-group">
                        <label for="researchHypothesis">💡 研究假设</label>
                        <textarea id="researchHypothesis" 
                                  placeholder="请描述您的研究假设，例如：H1: 实验组的成绩显著高于对照组..."></textarea>
                    </div>

                    <div class="form-group">
                        <label>🔍 分析需求</label>
                        <div class="checkbox-grid">
                            <div class="checkbox-item">
                                <input type="checkbox" id="descriptive" value="descriptive">
                                <label for="descriptive">描述性统计</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="inferential" value="inferential">
                                <label for="inferential">推断性统计</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="modeling" value="modeling">
                                <label for="modeling">模型构建</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="visualization" value="visualization">
                                <label for="visualization">数据可视化</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="code-generation" value="code-generation">
                                <label for="code-generation">代码生成</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="report-generation" value="report-generation">
                                <label for="report-generation">报告生成</label>
                            </div>
                        </div>
                    </div>

                    <button type="submit" class="submit-btn" id="textSubmitBtn">
                        🚀 开始分析
                    </button>
                </form>
            </div>

            <!-- 结构化问卷标签页 -->
            <div id="questionnaire-tab" class="tab-content">
                <form id="questionnaireForm">
                    <div class="form-group">
                        <label for="studyType">📋 研究类型 *</label>
                        <select id="studyType" required onchange="updateQuestions()">
                            <option value="">请选择研究类型</option>
                            <option value="experimental">实验研究</option>
                            <option value="observational">观察性研究</option>
                            <option value="survey">调查研究</option>
                            <option value="case-study">案例研究</option>
                            <option value="meta-analysis">荟萃分析</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="sampleSize">👥 样本量</label>
                        <div class="input-grid">
                            <input type="number" id="sampleSize" placeholder="样本数量" min="1">
                            <select id="samplingMethod">
                                <option value="">抽样方法</option>
                                <option value="random">随机抽样</option>
                                <option value="stratified">分层抽样</option>
                                <option value="cluster">整群抽样</option>
                                <option value="convenience">便利抽样</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="variables">📊 变量信息</label>
                        <div class="input-grid">
                            <select id="independentVarType">
                                <option value="">自变量类型</option>
                                <option value="categorical">分类变量</option>
                                <option value="continuous">连续变量</option>
                                <option value="ordinal">有序变量</option>
                            </select>
                            <select id="dependentVarType">
                                <option value="">因变量类型</option>
                                <option value="categorical">分类变量</option>
                                <option value="continuous">连续变量</option>
                                <option value="ordinal">有序变量</option>
                            </select>
                        </div>
                    </div>

                    <div id="dynamicQuestions"></div>

                    <button type="submit" class="submit-btn" id="questionnaireSubmitBtn">
                        🚀 生成分析方案
                    </button>
                </form>
            </div>

            <!-- 文件上传标签页 -->
            <div id="file-tab" class="tab-content">
                <form id="fileAnalysisForm">
                    <div class="form-group">
                        <label>📁 数据文件上传</label>
                        <div class="file-upload" id="fileUpload">
                            <div>
                                <p>📂 拖拽文件到此处或点击选择文件</p>
                                <p style="color: #6b7280; font-size: 14px; margin-top: 8px;">
                                    支持 CSV, Excel (.xlsx, .xls) 格式
                                </p>
                            </div>
                            <input type="file" id="dataFile" accept=".csv,.xlsx,.xls" style="display: none;">
                        </div>
                        <div id="fileInfo" style="margin-top: 10px; display: none;"></div>
                    </div>

                    <div class="form-group">
                        <label for="analysisGoal">🎯 分析目标</label>
                        <textarea id="analysisGoal" 
                                  placeholder="请描述您希望通过数据分析达到的目标..."></textarea>
                    </div>

                    <button type="submit" class="submit-btn" id="fileSubmitBtn" disabled>
                        📊 分析数据文件
                    </button>
                </form>
            </div>

            <!-- 结果展示区域 -->
            <div class="result-section" id="resultSection">
                <h3>📈 分析结果</h3>
                <div class="result-content" id="resultContent"></div>
            </div>
        </div>
    </div>

    <script>
        // 标签页切换
        function switchTab(tabName) {
            // 隐藏所有标签页内容
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });

            // 移除所有按钮的激活状态
            document.querySelectorAll('.tab-button').forEach(btn => {
                btn.classList.remove('active');
            });

            // 显示选中的标签页
            document.getElementById(tabName + '-tab').classList.add('active');
            event.target.classList.add('active');
        }

        // 智能提示功能
        function showSuggestion(inputId, suggestionId, suggestions) {
            const input = document.getElementById(inputId);
            const suggestionBox = document.getElementById(suggestionId);

            if (input.value.length > 10) {
                suggestionBox.innerHTML = `💡 建议：${suggestions[Math.floor(Math.random() * suggestions.length)]}`;
                suggestionBox.style.display = 'block';
            } else {
                suggestionBox.style.display = 'none';
            }
        }

        // 动态问题更新
        function updateQuestions() {
            const studyType = document.getElementById('studyType').value;
            const dynamicQuestions = document.getElementById('dynamicQuestions');

            let questionsHTML = '';

            if (studyType === 'experimental') {
                questionsHTML = `
                    <div class="form-group">
                        <label for="groupCount">👥 实验组数量</label>
                        <input type="number" id="groupCount" placeholder="实验组和对照组总数" min="2">
                    </div>
                    <div class="form-group">
                        <label for="randomization">🎲 随机化方法</label>
                        <select id="randomization">
                            <option value="">请选择</option>
                            <option value="simple">简单随机化</option>
                            <option value="block">区组随机化</option>
                            <option value="stratified">分层随机化</option>
                        </select>
                    </div>
                `;
            } else if (studyType === 'survey') {
                questionsHTML = `
                    <div class="form-group">
                        <label for="surveyType">📋 调查类型</label>
                        <select id="surveyType">
                            <option value="">请选择</option>
                            <option value="cross-sectional">横断面调查</option>
                            <option value="longitudinal">纵向调查</option>
                            <option value="cohort">队列调查</option>
                        </select>
                    </div>
                `;
            }

            dynamicQuestions.innerHTML = questionsHTML;
        }

        // 文件上传处理
        function setupFileUpload() {
            const fileUpload = document.getElementById('fileUpload');
            const fileInput = document.getElementById('dataFile');
            const fileInfo = document.getElementById('fileInfo');
            const submitBtn = document.getElementById('fileSubmitBtn');

            fileUpload.addEventListener('click', () => fileInput.click());

            fileUpload.addEventListener('dragover', (e) => {
                e.preventDefault();
                fileUpload.classList.add('dragover');
            });

            fileUpload.addEventListener('dragleave', () => {
                fileUpload.classList.remove('dragover');
            });

            fileUpload.addEventListener('drop', (e) => {
                e.preventDefault();
                fileUpload.classList.remove('dragover');
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    handleFileSelect(files[0]);
                }
            });

            fileInput.addEventListener('change', (e) => {
                if (e.target.files.length > 0) {
                    handleFileSelect(e.target.files[0]);
                }
            });

            function handleFileSelect(file) {
                const allowedTypes = ['.csv', '.xlsx', '.xls'];
                const fileExtension = '.' + file.name.split('.').pop().toLowerCase();

                if (allowedTypes.includes(fileExtension)) {
                    fileInfo.innerHTML = `
                        <div style="color: #10b981;">
                            ✅ 文件已选择: ${file.name} (${(file.size / 1024 / 1024).toFixed(2)} MB)
                        </div>
                    `;
                    fileInfo.style.display = 'block';
                    submitBtn.disabled = false;
                } else {
                    fileInfo.innerHTML = `
                        <div style="color: #ef4444;">
                            ❌ 不支持的文件格式，请选择 CSV 或 Excel 文件
                        </div>
                    `;
                    fileInfo.style.display = 'block';
                    submitBtn.disabled = true;
                }
            }
        }

        // 表单提交处理
        async function handleFormSubmit(formData, analysisType) {
            const resultSection = document.getElementById('resultSection');
            const resultContent = document.getElementById('resultContent');

            resultSection.style.display = 'block';
            resultContent.innerHTML = '<div class="loading">🔄 AI正在进行深度统计分析，请稍候...</div>';

            try {
                // 构建专业提示词
                let prompt = '';

                if (analysisType === 'text') {
                    prompt = `作为高级统计分析专家，请为以下研究提供全面的统计分析方案：

研究问题：${formData.researchQuestion}
数据描述：${formData.dataDescription || '未提供'}
研究假设：${formData.researchHypothesis || '未提供'}
分析需求：${formData.analysisNeeds.join(', ') || '全面分析'}

请按照以下结构提供详细分析：

1. **研究设计评估**
   - 研究类型识别
   - 变量识别（自变量、因变量、控制变量）
   - 研究设计优化建议

2. **统计方法推荐**
   - 推荐3-5种最适合的统计方法
   - 每种方法的适用条件和优缺点
   - 方法选择的理由和置信度评分

3. **数据预处理建议**
   - 数据清洗步骤
   - 异常值处理
   - 缺失值处理策略

4. **分析步骤详解**
   - 描述性统计分析
   - 推断性统计分析
   - 效应量计算
   - 结果解读指导

5. **可视化建议**
   - 推荐的图表类型
   - 可视化最佳实践

6. **代码示例**
   - R语言代码示例
   - Python代码示例
   - 详细注释说明

7. **结果解读模板**
   - 结果报告模板
   - 统计显著性解读
   - 实际意义评估

8. **注意事项和局限性**
   - 统计假设检验
   - 潜在偏倚识别
   - 结果推广性评估

请确保建议专业、实用、易于理解。`;

                } else if (analysisType === 'questionnaire') {
                    prompt = `作为统计方法专家，请根据以下结构化信息设计统计分析方案：

研究类型：${formData.studyType}
样本量：${formData.sampleSize || '未指定'}
抽样方法：${formData.samplingMethod || '未指定'}
自变量类型：${formData.independentVarType || '未指定'}
因变量类型：${formData.dependentVarType || '未指定'}

请提供：
1. 最适合的统计方法推荐（按优先级排序）
2. 样本量充分性评估
3. 统计功效分析建议
4. 具体的分析步骤
5. R和Python代码实现
6. 结果解读指南

请确保推荐的方法符合统计学原理和最佳实践。`;

                } else if (analysisType === 'file') {
                    prompt = `作为数据分析专家，请为上传的数据文件提供分析建议：

分析目标：${formData.analysisGoal}
文件信息：${formData.fileName} (${formData.fileSize})

请提供：
1. 数据探索性分析建议
2. 适合的统计方法推荐
3. 数据预处理步骤
4. 可视化方案
5. 完整的分析流程
6. 代码示例和结果解读

注意：由于无法直接访问数据内容，请提供通用的数据分析框架和方法。`;
                }

                // 发送请求到DeepSeek API
                const response = await fetch('/api/deepseek_analysis', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        prompt: prompt,
                        expert_type: '深度统计分析专家'
                    })
                });

                const data = await response.json();

                if (data.success) {
                    // 格式化显示结果
                    const formattedResult = data.analysis
                        .replace(/\n/g, '<br>')
                        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                        .replace(/```(.*?)```/gs, '<pre style="background: #f3f4f6; padding: 15px; border-radius: 8px; overflow-x: auto;"><code>$1</code></pre>');

                    resultContent.innerHTML = formattedResult;
                } else {
                    resultContent.innerHTML = `<div style="color: #ef4444;">❌ 分析失败: ${data.error}</div>`;
                }
            } catch (error) {
                console.error('分析错误:', error);
                resultContent.innerHTML = `<div style="color: #ef4444;">❌ 分析过程中出现错误: ${error.message}</div>`;
            }
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            setupFileUpload();

            // 智能提示
            const suggestions = [
                '考虑添加更多的控制变量来提高研究的内部效度',
                '建议明确定义操作性变量以便于测量',
                '考虑使用多种统计方法来验证结果的稳健性',
                '注意检查数据的正态性和方差齐性假设'
            ];

            document.getElementById('researchQuestion').addEventListener('input', () => {
                showSuggestion('researchQuestion', 'questionSuggestion', suggestions);
            });

            // 文本输入表单提交
            document.getElementById('textAnalysisForm').addEventListener('submit', async function(e) {
                e.preventDefault();

                const formData = {
                    researchQuestion: document.getElementById('researchQuestion').value,
                    dataDescription: document.getElementById('dataDescription').value,
                    researchHypothesis: document.getElementById('researchHypothesis').value,
                    analysisNeeds: Array.from(document.querySelectorAll('#text-tab input[type="checkbox"]:checked')).map(cb => cb.value)
                };

                if (!formData.researchQuestion.trim()) {
                    alert('请填写研究问题');
                    return;
                }

                const submitBtn = document.getElementById('textSubmitBtn');
                submitBtn.disabled = true;
                submitBtn.textContent = '🔄 分析中...';

                await handleFormSubmit(formData, 'text');

                submitBtn.disabled = false;
                submitBtn.textContent = '🚀 开始分析';
            });

            // 问卷表单提交
            document.getElementById('questionnaireForm').addEventListener('submit', async function(e) {
                e.preventDefault();

                const formData = {
                    studyType: document.getElementById('studyType').value,
                    sampleSize: document.getElementById('sampleSize').value,
                    samplingMethod: document.getElementById('samplingMethod').value,
                    independentVarType: document.getElementById('independentVarType').value,
                    dependentVarType: document.getElementById('dependentVarType').value
                };

                if (!formData.studyType) {
                    alert('请选择研究类型');
                    return;
                }

                const submitBtn = document.getElementById('questionnaireSubmitBtn');
                submitBtn.disabled = true;
                submitBtn.textContent = '🔄 生成中...';

                await handleFormSubmit(formData, 'questionnaire');

                submitBtn.disabled = false;
                submitBtn.textContent = '🚀 生成分析方案';
            });

            // 文件分析表单提交
            document.getElementById('fileAnalysisForm').addEventListener('submit', async function(e) {
                e.preventDefault();

                const fileInput = document.getElementById('dataFile');
                const file = fileInput.files[0];

                if (!file) {
                    alert('请选择数据文件');
                    return;
                }

                const formData = {
                    fileName: file.name,
                    fileSize: (file.size / 1024 / 1024).toFixed(2) + ' MB',
                    analysisGoal: document.getElementById('analysisGoal').value
                };

                const submitBtn = document.getElementById('fileSubmitBtn');
                submitBtn.disabled = true;
                submitBtn.textContent = '🔄 分析中...';

                await handleFormSubmit(formData, 'file');

                submitBtn.disabled = false;
                submitBtn.textContent = '📊 分析数据文件';
            });
        });
    </script>
</body>
</html>
