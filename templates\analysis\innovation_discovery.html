<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>创新点挖掘 - NNScholar</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
            min-height: 100vh;
            padding: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .container {
            max-width: 600px;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            text-align: center;
        }

        .header {
            background: linear-gradient(135deg, #dc2626, #b91c1c);
            color: white;
            padding: 40px 30px;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .header p {
            opacity: 0.9;
            font-size: 1.2rem;
        }

        .content {
            padding: 40px 30px;
        }

        .status-icon {
            font-size: 4rem;
            margin-bottom: 20px;
        }

        .status-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #374151;
            margin-bottom: 15px;
        }

        .status-description {
            color: #6b7280;
            line-height: 1.6;
            margin-bottom: 30px;
        }

        .features-list {
            text-align: left;
            background: #f8fafc;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
        }

        .features-list h3 {
            color: #dc2626;
            margin-bottom: 15px;
            text-align: center;
        }

        .features-list ul {
            list-style: none;
            padding: 0;
        }

        .features-list li {
            padding: 8px 0;
            color: #374151;
            position: relative;
            padding-left: 25px;
        }

        .features-list li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #10b981;
            font-weight: bold;
        }

        .back-btn {
            background: linear-gradient(135deg, #dc2626, #b91c1c);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: #e5e7eb;
            border-radius: 3px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #dc2626, #b91c1c);
            width: 75%;
            border-radius: 3px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 15px;
            }
            
            .header {
                padding: 30px 20px;
            }
            
            .content {
                padding: 30px 20px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>💡 创新点挖掘</h1>
            <p>发现研究空白与创新方向</p>
        </div>
        
        <div class="content">
            <div class="status-icon">🚧</div>
            <div class="status-title">功能开发中</div>
            <div class="status-description">
                我们正在努力开发这个功能，为您提供最专业的服务。
            </div>
            
            <div class="progress-bar">
                <div class="progress-fill"></div>
            </div>
            
            <div class="features-list">
                <h3>即将推出的功能</h3>
                <ul>
                    <li>研究空白识别</li>
                    <li>创新机会发现</li>
                    <li>技术路线分析</li>
                    <li>竞争优势评估</li>
                    <li>发展潜力预测</li>
                    <li>实施建议</li>
                </ul>
            </div>
            
            <button class="back-btn" onclick="window.close()">
                ← 返回深度分析
            </button>
        </div>
    </div>
</body>
</html>