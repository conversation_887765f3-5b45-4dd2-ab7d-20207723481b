<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>论文润色 - NNScholar</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #f59e0b, #d97706);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2rem;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .header p {
            opacity: 0.9;
            font-size: 1.1rem;
        }

        .content {
            padding: 40px;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-group label {
            display: block;
            font-weight: 600;
            color: #374151;
            margin-bottom: 8px;
            font-size: 1rem;
        }

        .form-group textarea {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e5e7eb;
            border-radius: 10px;
            font-size: 14px;
            transition: all 0.3s ease;
            resize: vertical;
            min-height: 200px;
            font-family: inherit;
        }

        .form-group textarea:focus {
            outline: none;
            border-color: #f59e0b;
            box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.1);
        }

        .polish-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .option-card {
            border: 2px solid #e5e7eb;
            border-radius: 10px;
            padding: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }

        .option-card:hover {
            border-color: #f59e0b;
            background: #fef3c7;
        }

        .option-card.selected {
            border-color: #f59e0b;
            background: #fef3c7;
        }

        .option-card input[type="checkbox"] {
            display: none;
        }

        .option-title {
            font-weight: 600;
            color: #374151;
            margin-bottom: 5px;
        }

        .option-desc {
            color: #6b7280;
            font-size: 0.9rem;
        }

        .submit-btn {
            background: linear-gradient(135deg, #f59e0b, #d97706);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            margin-top: 20px;
        }

        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(245, 158, 11, 0.3);
        }

        .submit-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .result-section {
            margin-top: 30px;
            padding: 25px;
            background: #f8fafc;
            border-radius: 15px;
            display: none;
        }

        .result-section.show {
            display: block;
        }

        .result-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .result-header h3 {
            color: #374151;
            font-size: 1.2rem;
        }

        .copy-btn {
            background: #10b981;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
        }

        .copy-btn:hover {
            background: #059669;
        }

        .result-content {
            background: white;
            padding: 20px;
            border-radius: 10px;
            border: 1px solid #e5e7eb;
            white-space: pre-wrap;
            line-height: 1.6;
            font-size: 14px;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #6b7280;
        }

        .loading-spinner {
            display: inline-block;
            width: 40px;
            height: 40px;
            border: 4px solid #e5e7eb;
            border-radius: 50%;
            border-top-color: #f59e0b;
            animation: spin 1s ease-in-out infinite;
            margin-bottom: 15px;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .back-btn {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(255,255,255,0.2);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            background: rgba(255,255,255,0.3);
            transform: translateX(-5px);
        }

        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .feature-card {
            text-align: center;
            padding: 20px;
            background: #f8fafc;
            border-radius: 10px;
            border: 1px solid #e5e7eb;
        }

        .feature-icon {
            font-size: 2rem;
            margin-bottom: 10px;
        }

        .feature-title {
            font-weight: 600;
            color: #374151;
            margin-bottom: 5px;
        }

        .feature-desc {
            color: #6b7280;
            font-size: 0.9rem;
        }

        @media (max-width: 768px) {
            .polish-options {
                grid-template-columns: 1fr;
            }
            
            .container {
                margin: 10px;
                border-radius: 15px;
            }
            
            .content {
                padding: 20px;
            }
            
            .features {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <button class="back-btn" onclick="window.history.back()">← 返回</button>
    
    <div class="container">
        <div class="header">
            <h1>✨ 论文润色</h1>
            <p>提升论文语言表达和学术规范</p>
        </div>
        
        <div class="content">
            <form id="polishForm">
                <div class="form-group">
                    <label>选择润色重点</label>
                    <div class="polish-options">
                        <div class="option-card" onclick="toggleOption(this)">
                            <input type="checkbox" name="polishType" value="grammar">
                            <div class="option-title">语法优化</div>
                            <div class="option-desc">修正语法错误，提升表达准确性</div>
                        </div>
                        <div class="option-card" onclick="toggleOption(this)">
                            <input type="checkbox" name="polishType" value="style">
                            <div class="option-title">学术风格</div>
                            <div class="option-desc">优化学术写作风格和表达</div>
                        </div>
                        <div class="option-card" onclick="toggleOption(this)">
                            <input type="checkbox" name="polishType" value="clarity">
                            <div class="option-title">逻辑清晰</div>
                            <div class="option-desc">增强逻辑性和可读性</div>
                        </div>
                        <div class="option-card" onclick="toggleOption(this)">
                            <input type="checkbox" name="polishType" value="terminology">
                            <div class="option-title">术语规范</div>
                            <div class="option-desc">统一专业术语使用</div>
                        </div>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="inputText">输入要润色的论文内容</label>
                    <textarea id="inputText" placeholder="请输入需要润色的学术论文内容..." required></textarea>
                </div>
                
                <button type="submit" class="submit-btn" id="submitBtn">
                    ✨ 开始润色
                </button>
            </form>

            <!-- 加载状态 -->
            <div id="loadingSection" class="loading" style="display: none;">
                <div class="loading-spinner"></div>
                <p>正在润色中，请稍候...</p>
            </div>

            <!-- 结果显示 -->
            <div id="resultSection" class="result-section">
                <div class="result-header">
                    <h3>📝 润色结果</h3>
                    <button id="copyBtn" class="copy-btn">复制结果</button>
                </div>
                <div id="resultContent" class="result-content"></div>
            </div>

            <!-- 功能特色 -->
            <div class="features">
                <div class="feature-card">
                    <div class="feature-icon">📚</div>
                    <div class="feature-title">学术专业</div>
                    <div class="feature-desc">专业学术写作规范</div>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🎯</div>
                    <div class="feature-title">精准优化</div>
                    <div class="feature-desc">针对性语言改进</div>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🔍</div>
                    <div class="feature-title">细致检查</div>
                    <div class="feature-desc">全面语法和风格检查</div>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">⚡</div>
                    <div class="feature-title">快速高效</div>
                    <div class="feature-desc">AI驱动的智能润色</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function toggleOption(card) {
            const checkbox = card.querySelector('input[type="checkbox"]');
            checkbox.checked = !checkbox.checked;
            card.classList.toggle('selected', checkbox.checked);
        }

        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('polishForm');
            const inputText = document.getElementById('inputText');
            const submitBtn = document.getElementById('submitBtn');
            const loadingSection = document.getElementById('loadingSection');
            const resultSection = document.getElementById('resultSection');
            const resultContent = document.getElementById('resultContent');
            const copyBtn = document.getElementById('copyBtn');

            // 表单提交
            form.addEventListener('submit', async function(e) {
                e.preventDefault();

                const text = inputText.value.trim();
                if (!text) {
                    alert('请输入要润色的文本');
                    return;
                }

                // 获取选中的润色选项
                const selectedOptions = Array.from(document.querySelectorAll('input[name="polishType"]:checked'))
                    .map(input => {
                        const card = input.closest('.option-card');
                        return card.querySelector('.option-title').textContent;
                    });

                if (selectedOptions.length === 0) {
                    alert('请至少选择一个润色重点');
                    return;
                }

                const prompt = `请对以下学术论文内容进行润色，重点关注：${selectedOptions.join('、')}。

润色要求：
1. 保持学术论文的专业性和严谨性
2. 优化语言表达，提升可读性
3. 确保逻辑清晰，结构合理
4. 统一专业术语的使用
5. 修正语法错误和表达不当之处
6. 保持原文的核心观点和内容不变

需要润色的内容：
${text}`;

                try {
                    submitBtn.disabled = true;
                    loadingSection.style.display = 'block';
                    resultSection.classList.remove('show');

                    const response = await fetch('/api/deepseek_analysis', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            prompt: prompt,
                            expert_type: '学术论文润色专家'
                        })
                    });

                    const data = await response.json();

                    if (data.success) {
                        resultContent.textContent = data.analysis;
                        resultSection.classList.add('show');
                    } else {
                        alert('润色失败：' + data.error);
                    }
                } catch (error) {
                    console.error('润色错误:', error);
                    alert('润色过程中出现错误，请稍后重试');
                } finally {
                    submitBtn.disabled = false;
                    loadingSection.style.display = 'none';
                }
            });

            // 复制功能
            copyBtn.addEventListener('click', function() {
                const text = resultContent.textContent;
                navigator.clipboard.writeText(text).then(function() {
                    const originalText = copyBtn.textContent;
                    copyBtn.textContent = '已复制';
                    setTimeout(function() {
                        copyBtn.textContent = originalText;
                    }, 2000);
                }).catch(function(err) {
                    console.error('复制失败:', err);
                    alert('复制失败，请手动复制');
                });
            });
        });
    </script>
</body>
</html>
