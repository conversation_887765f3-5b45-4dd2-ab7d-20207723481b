<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>论文翻译 - NNScholar</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #06b6d4, #0891b2);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2rem;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .header p {
            opacity: 0.9;
            font-size: 1.1rem;
        }

        .content {
            padding: 40px;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-group label {
            display: block;
            font-weight: 600;
            color: #374151;
            margin-bottom: 8px;
            font-size: 1rem;
        }

        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e5e7eb;
            border-radius: 10px;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #06b6d4;
            box-shadow: 0 0 0 3px rgba(6, 182, 212, 0.1);
        }

        .form-group textarea {
            resize: vertical;
            min-height: 200px;
            font-family: inherit;
        }

        .language-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .submit-btn {
            background: linear-gradient(135deg, #06b6d4, #0891b2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            margin-top: 20px;
        }

        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(6, 182, 212, 0.3);
        }

        .submit-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .result-section {
            margin-top: 30px;
            padding: 25px;
            background: #f8fafc;
            border-radius: 15px;
            display: none;
        }

        .result-section.show {
            display: block;
        }

        .result-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .result-header h3 {
            color: #374151;
            font-size: 1.2rem;
        }

        .copy-btn {
            background: #10b981;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
        }

        .copy-btn:hover {
            background: #059669;
        }

        .result-content {
            background: white;
            padding: 20px;
            border-radius: 10px;
            border: 1px solid #e5e7eb;
            white-space: pre-wrap;
            line-height: 1.6;
            font-size: 14px;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #6b7280;
        }

        .loading-spinner {
            display: inline-block;
            width: 40px;
            height: 40px;
            border: 4px solid #e5e7eb;
            border-radius: 50%;
            border-top-color: #06b6d4;
            animation: spin 1s ease-in-out infinite;
            margin-bottom: 15px;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .back-btn {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(255,255,255,0.2);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            background: rgba(255,255,255,0.3);
            transform: translateX(-5px);
        }

        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .feature-card {
            text-align: center;
            padding: 20px;
            background: #f8fafc;
            border-radius: 10px;
            border: 1px solid #e5e7eb;
        }

        .feature-icon {
            font-size: 2rem;
            margin-bottom: 10px;
        }

        .feature-title {
            font-weight: 600;
            color: #374151;
            margin-bottom: 5px;
        }

        .feature-desc {
            color: #6b7280;
            font-size: 0.9rem;
        }

        @media (max-width: 768px) {
            .language-row {
                grid-template-columns: 1fr;
            }
            
            .container {
                margin: 10px;
                border-radius: 15px;
            }
            
            .content {
                padding: 20px;
            }
            
            .features {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <button class="back-btn" onclick="window.history.back()">← 返回</button>
    
    <div class="container">
        <div class="header">
            <h1>🌐 论文翻译</h1>
            <p>专业的学术论文中英文翻译服务</p>
        </div>
        
        <div class="content">
            <form id="translationForm">
                <div class="language-row">
                    <div class="form-group">
                        <label for="sourceLanguage">源语言</label>
                        <select id="sourceLanguage" required>
                            <option value="zh">中文</option>
                            <option value="en">English</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="targetLanguage">目标语言</label>
                        <select id="targetLanguage" required>
                            <option value="en">English</option>
                            <option value="zh">中文</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="inputText">输入要翻译的文本</label>
                    <textarea id="inputText" placeholder="请输入需要翻译的学术论文内容..." required></textarea>
                </div>
                
                <button type="submit" class="submit-btn" id="submitBtn">
                    🚀 开始翻译
                </button>
            </form>
            
            <!-- 加载状态 -->
            <div id="loadingSection" class="loading" style="display: none;">
                <div class="loading-spinner"></div>
                <p>正在翻译中，请稍候...</p>
            </div>
            
            <!-- 结果显示 -->
            <div id="resultSection" class="result-section">
                <div class="result-header">
                    <h3>📄 翻译结果</h3>
                    <button id="copyBtn" class="copy-btn">复制结果</button>
                </div>
                <div id="resultContent" class="result-content"></div>
            </div>
            
            <!-- 功能特色 -->
            <div class="features">
                <div class="feature-card">
                    <div class="feature-icon">🎯</div>
                    <div class="feature-title">学术专业</div>
                    <div class="feature-desc">专门针对学术论文优化</div>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">⚡</div>
                    <div class="feature-title">快速高效</div>
                    <div class="feature-desc">AI驱动的翻译引擎</div>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🔄</div>
                    <div class="feature-title">双向翻译</div>
                    <div class="feature-desc">支持中英文双向翻译</div>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">📝</div>
                    <div class="feature-title">格式保持</div>
                    <div class="feature-desc">保持原文段落结构</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('translationForm');
            const sourceLanguage = document.getElementById('sourceLanguage');
            const targetLanguage = document.getElementById('targetLanguage');
            const inputText = document.getElementById('inputText');
            const submitBtn = document.getElementById('submitBtn');
            const loadingSection = document.getElementById('loadingSection');
            const resultSection = document.getElementById('resultSection');
            const resultContent = document.getElementById('resultContent');
            const copyBtn = document.getElementById('copyBtn');

            // 语言切换逻辑
            sourceLanguage.addEventListener('change', function() {
                targetLanguage.value = this.value === 'zh' ? 'en' : 'zh';
            });

            targetLanguage.addEventListener('change', function() {
                sourceLanguage.value = this.value === 'zh' ? 'en' : 'zh';
            });

            // 表单提交
            form.addEventListener('submit', async function(e) {
                e.preventDefault();

                const text = inputText.value.trim();
                if (!text) {
                    alert('请输入要翻译的文本');
                    return;
                }

                const sourceLang = sourceLanguage.value;
                const targetLang = targetLanguage.value;

                const prompt = `请将以下${sourceLang === 'zh' ? '中文' : '英文'}学术论文内容翻译成${targetLang === 'zh' ? '中文' : '英文'}，要求：
1. 保持学术论文的专业性和准确性
2. 确保专业术语翻译准确
3. 保持原文的段落结构和格式
4. 语言表达自然流畅
5. 符合目标语言的学术写作规范

需要翻译的内容：
${text}`;

                try {
                    submitBtn.disabled = true;
                    loadingSection.style.display = 'block';
                    resultSection.classList.remove('show');

                    const response = await fetch('/api/deepseek_analysis', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            prompt: prompt,
                            expert_type: '学术翻译专家'
                        })
                    });

                    const data = await response.json();

                    if (data.success) {
                        resultContent.textContent = data.analysis;
                        resultSection.classList.add('show');
                    } else {
                        alert('翻译失败：' + data.error);
                    }
                } catch (error) {
                    console.error('翻译错误:', error);
                    alert('翻译过程中出现错误，请稍后重试');
                } finally {
                    submitBtn.disabled = false;
                    loadingSection.style.display = 'none';
                }
            });

            // 复制功能
            copyBtn.addEventListener('click', function() {
                const text = resultContent.textContent;
                navigator.clipboard.writeText(text).then(function() {
                    const originalText = copyBtn.textContent;
                    copyBtn.textContent = '已复制';
                    setTimeout(function() {
                        copyBtn.textContent = originalText;
                    }, 2000);
                }).catch(function(err) {
                    console.error('复制失败:', err);
                    alert('复制失败，请手动复制');
                });
            });
        });
    </script>
</body>
</html>
