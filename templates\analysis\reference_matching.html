<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>参考文献匹配 - NNScholar</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .card-shadow {
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
        .reference-card {
            border-left: 4px solid #667eea;
            transition: all 0.3s ease;
        }
        .reference-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }
        .verified-badge {
            background: linear-gradient(45deg, #10b981, #059669);
        }
        .unverified-badge {
            background: linear-gradient(45deg, #ef4444, #dc2626);
        }
        .loading-spinner {
            border: 3px solid #f3f4f6;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .citation-link {
            color: #2563eb;
            font-weight: 600;
            cursor: pointer;
            padding: 2px 4px;
            border-radius: 4px;
            transition: all 0.2s ease;
            background-color: #dbeafe;
        }
        .citation-link:hover {
            background-color: #3b82f6;
            color: white;
            transform: scale(1.1);
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 头部 -->
    <div class="gradient-bg text-white py-8">
        <div class="container mx-auto px-4">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold mb-2">📚 参考文献匹配</h1>
                    <p class="text-blue-100">为您的学术内容匹配权威参考文献，确保文献真实可靠</p>
                </div>
                <div class="text-right">
                    <div class="text-sm text-blue-100">学术文献验证与检索专家</div>
                    <div class="text-xs text-blue-200 mt-1">基于多个数据库验证文献真实性</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 主要内容 -->
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-4xl mx-auto">
            <!-- 输入区域 -->
            <div class="bg-white rounded-lg card-shadow p-6 mb-8">
                <h2 class="text-xl font-semibold mb-4 text-gray-800">📝 输入需要匹配参考文献的内容</h2>
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            学术内容 <span class="text-red-500">*</span>
                        </label>
                        <textarea 
                            id="textContent" 
                            rows="8" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            placeholder="请输入需要匹配参考文献的段落或句子，例如：&#10;&#10;人工智能在医疗诊断中的应用前景广阔。深度学习技术能够有效提高诊断准确率。机器学习算法在影像识别方面表现出色。&#10;&#10;系统将为每个重要观点匹配权威的参考文献..."
                        ></textarea>
                    </div>
                    <div class="flex justify-between items-center">
                        <div class="text-sm text-gray-500">
                            💡 提示：系统将为每个重要观点匹配5年内的高影响因子文献
                        </div>
                        <button 
                            id="matchBtn" 
                            onclick="startReferenceMatching()"
                            class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md font-medium transition-colors duration-200"
                        >
                            🔍 开始匹配
                        </button>
                    </div>
                </div>
            </div>

            <!-- 进度显示 -->
            <div id="progressSection" class="bg-white rounded-lg card-shadow p-6 mb-8 hidden">
                <div class="flex items-center space-x-3">
                    <div class="loading-spinner"></div>
                    <div>
                        <div class="font-medium text-gray-800">正在匹配参考文献...</div>
                        <div id="progressMessage" class="text-sm text-gray-600">正在分析文本内容...</div>
                    </div>
                </div>
                <div class="mt-4">
                    <div class="bg-gray-200 rounded-full h-2">
                        <div id="progressBar" class="bg-blue-600 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                    </div>
                    <div class="text-xs text-gray-500 mt-1">
                        <span id="progressPercent">0%</span> 完成
                    </div>
                </div>
            </div>

            <!-- 结果显示 -->
            <div id="resultsSection" class="hidden">
                <!-- 统计信息 -->
                <div id="statsSection" class="bg-white rounded-lg card-shadow p-6 mb-6">
                    <h3 class="text-lg font-semibold mb-4 text-gray-800">📊 匹配统计</h3>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div class="text-center p-4 bg-blue-50 rounded-lg">
                            <div class="text-2xl font-bold text-blue-600" id="totalCount">0</div>
                            <div class="text-sm text-gray-600">总文献数</div>
                        </div>
                        <div class="text-center p-4 bg-green-50 rounded-lg">
                            <div class="text-2xl font-bold text-green-600" id="validCount">0</div>
                            <div class="text-sm text-gray-600">已验证文献</div>
                        </div>
                        <div class="text-center p-4 bg-red-50 rounded-lg">
                            <div class="text-2xl font-bold text-red-600" id="invalidCount">0</div>
                            <div class="text-sm text-gray-600">未验证文献</div>
                        </div>
                    </div>
                </div>

                <!-- AI生成的带引用文本 -->
                <div id="generatedTextSection" class="bg-white rounded-lg card-shadow p-6 mb-6 hidden">
                    <h3 class="text-lg font-semibold mb-4 text-gray-800">📝 带引用的学术文本</h3>
                    <div class="bg-gray-50 rounded-lg p-4 border-l-4 border-blue-500">
                        <div id="generatedText" class="text-gray-800 leading-relaxed">
                            <!-- AI生成的带序号引用的文本将显示在这里 -->
                        </div>
                    </div>
                    <div class="mt-3 text-sm text-gray-600">
                        💡 <strong>说明：</strong>上述文本中的序号[1][2][3]等对应下方的参考文献列表，点击序号可快速定位到对应文献。
                    </div>

                    <!-- 导出按钮 -->
                    <div class="mt-4 flex justify-end">
                        <div class="flex flex-wrap gap-3 items-center">
                            <div class="text-sm text-gray-600 flex items-center">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                NBIB格式可导入EndNote、Zotero等文献管理软件
                            </div>
                            <button onclick="exportToNBIB()" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                                导出NBIB文件
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 参考文献列表 -->
                <div class="bg-white rounded-lg card-shadow p-6">
                    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 gap-3">
                        <h3 class="text-lg font-semibold text-gray-800">📚 匹配的参考文献</h3>
                        <div class="flex flex-col sm:flex-row items-start sm:items-center gap-3 w-full sm:w-auto">
                            <div class="text-sm text-gray-600 flex items-center">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                NBIB格式可导入EndNote、Zotero
                            </div>
                            <button id="exportNBIBBtn" onclick="exportToNBIB()" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center whitespace-nowrap">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                                导出NBIB文件
                            </button>
                        </div>
                    </div>
                    <div id="referencesList" class="space-y-6">
                        <!-- 动态生成的参考文献卡片 -->
                    </div>
                </div>
            </div>

            <!-- 错误显示 -->
            <div id="errorSection" class="bg-red-50 border border-red-200 rounded-lg p-6 hidden">
                <div class="flex items-center space-x-3">
                    <div class="text-red-500 text-xl">❌</div>
                    <div>
                        <div class="font-medium text-red-800">匹配失败</div>
                        <div id="errorMessage" class="text-sm text-red-600 mt-1"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentTaskId = null;

        async function startReferenceMatching() {
            const textContent = document.getElementById('textContent').value.trim();
            
            if (!textContent) {
                alert('请输入需要匹配参考文献的内容');
                return;
            }

            // 隐藏之前的结果
            document.getElementById('resultsSection').classList.add('hidden');
            document.getElementById('errorSection').classList.add('hidden');
            
            // 显示进度
            document.getElementById('progressSection').classList.remove('hidden');
            document.getElementById('matchBtn').disabled = true;
            document.getElementById('matchBtn').textContent = '匹配中...';

            try {
                // 启动匹配任务
                const response = await fetch('/api/reference_matching', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        text_content: textContent
                    })
                });

                const result = await response.json();
                
                if (!result.success) {
                    throw new Error(result.error || '启动匹配任务失败');
                }

                currentTaskId = result.task_id;
                
                // 开始轮询任务状态
                pollTaskStatus();

            } catch (error) {
                console.error('匹配失败:', error);
                showError(error.message);
                resetUI();
            }
        }

        async function pollTaskStatus() {
            if (!currentTaskId) return;

            try {
                const response = await fetch(`/api/async/task/${currentTaskId}/status`);
                const statusData = await response.json();

                // 从API响应中获取任务信息
                const taskInfo = statusData.task || {};

                // 更新进度
                updateProgress(taskInfo.progress || 0, taskInfo.message || '处理中...');

                if (taskInfo.status === 'completed') {
                    // 直接从taskInfo中获取结果
                    console.log('任务完成，状态数据:', taskInfo);

                    if (taskInfo.result) {
                        showResults(taskInfo.result);
                    } else {
                        // 如果taskInfo中没有结果，再尝试获取
                        try {
                            const resultResponse = await fetch(`/api/async/task/${currentTaskId}/result`);
                            const resultData = await resultResponse.json();

                            if (resultData.success && resultData.result) {
                                showResults(resultData.result);
                            } else {
                                throw new Error('无法获取任务结果');
                            }
                        } catch (resultError) {
                            console.error('获取结果失败:', resultError);
                            showError('任务完成但无法获取结果');
                        }
                    }
                    resetUI();
                } else if (taskInfo.status === 'failed') {
                    throw new Error(taskInfo.error || '任务执行失败');
                } else {
                    // 继续轮询
                    setTimeout(pollTaskStatus, 2000);
                }

            } catch (error) {
                console.error('获取任务状态失败:', error);
                showError(error.message);
                resetUI();
            }
        }

        function updateProgress(percent, message) {
            document.getElementById('progressBar').style.width = percent + '%';
            document.getElementById('progressPercent').textContent = percent + '%';
            document.getElementById('progressMessage').textContent = message;
        }

        function showResults(data) {
            // 保存当前的参考文献数据供导出使用
            currentReferences = data.references || [];
            console.log('showResults called, references count:', currentReferences.length);

            document.getElementById('progressSection').classList.add('hidden');
            document.getElementById('resultsSection').classList.remove('hidden');

            // 确保导出按钮可见
            const exportBtn = document.getElementById('exportNBIBBtn');
            if (exportBtn) {
                exportBtn.style.display = 'flex';
                console.log('Export button made visible');
            } else {
                console.error('Export button not found!');
            }

            // 更新统计信息
            document.getElementById('totalCount').textContent = data.total_count || 0;
            document.getElementById('validCount').textContent = data.valid_count || 0;
            document.getElementById('invalidCount').textContent = data.invalid_count || 0;

            // 显示AI生成的带引用文本
            if (data.ai_response) {
                showGeneratedText(data.ai_response, data.references);
            }

            // 显示参考文献列表
            const referencesList = document.getElementById('referencesList');
            referencesList.innerHTML = '';

            if (data.references && data.references.length > 0) {
                data.references.forEach((ref, index) => {
                    const refCard = createReferenceCard(ref, index + 1);
                    referencesList.appendChild(refCard);
                });

                // 添加简洁的PMID列表
                addSimpleReferenceList(data.references);
            } else {
                referencesList.innerHTML = '<div class="text-center text-gray-500 py-8">未找到匹配的参考文献</div>';
            }
        }

        function showGeneratedText(aiResponse, references) {
            const generatedTextSection = document.getElementById('generatedTextSection');
            const generatedTextDiv = document.getElementById('generatedText');

            if (!aiResponse) {
                generatedTextSection.classList.add('hidden');
                return;
            }

            // 提取AI响应中的主要文本部分（去掉参考文献列表部分）
            let mainText = aiResponse;

            // 查找"参考文献："之前的内容
            const refSectionIndex = aiResponse.indexOf('参考文献：');
            if (refSectionIndex !== -1) {
                mainText = aiResponse.substring(0, refSectionIndex).trim();
            }

            // 处理文本，为引用序号添加点击功能
            let processedText = mainText;

            // 查找所有的引用序号 [1], [2], [3] 等
            processedText = processedText.replace(/\[(\d+)\]/g, (match, number) => {
                const refIndex = parseInt(number) - 1;
                if (references && references[refIndex]) {
                    return `<span class="citation-link" onclick="scrollToReference(${number})" title="点击查看文献详情">${match}</span>`;
                }
                return match;
            });

            generatedTextDiv.innerHTML = processedText;
            generatedTextSection.classList.remove('hidden');
        }

        function scrollToReference(number) {
            // 滚动到对应的参考文献
            const referenceCards = document.querySelectorAll('.reference-card');
            if (referenceCards[number - 1]) {
                referenceCards[number - 1].scrollIntoView({
                    behavior: 'smooth',
                    block: 'center'
                });

                // 高亮显示对应的文献卡片
                referenceCards[number - 1].style.backgroundColor = '#fef3c7';
                setTimeout(() => {
                    referenceCards[number - 1].style.backgroundColor = '';
                }, 2000);
            }
        }

        function createReferenceCard(ref, index) {
            const card = document.createElement('div');
            card.className = 'reference-card bg-gray-50 rounded-lg p-4';
            
            const verified = ref.verified;
            const badgeClass = verified ? 'verified-badge' : 'unverified-badge';
            const badgeText = verified ? '✅ 已验证' : '❌ 未验证';
            
            card.innerHTML = `
                <div class="flex justify-between items-start mb-3">
                    <div class="font-medium text-gray-800">${index}. ${ref.title || ref.sentence || '原句子'}</div>
                    <span class="${badgeClass} text-white text-xs px-2 py-1 rounded-full">${badgeText}</span>
                </div>
                <div class="space-y-2 text-sm">
                    <div><strong>作者:</strong> ${Array.isArray(ref.authors) ? ref.authors.join(', ') : (ref.authors || '未提供')}</div>
                    <div><strong>期刊:</strong> ${ref.journal || '未提供'}</div>
                    <div><strong>年份:</strong> ${ref.year || '未提供'}</div>
                    ${ref.pmid ? `<div><strong>PMID:</strong> <a href="https://pubmed.ncbi.nlm.nih.gov/${ref.pmid}/" target="_blank" class="text-blue-600 hover:underline">${ref.pmid}</a></div>` : ''}
                    ${ref.doi ? `<div><strong>DOI:</strong> <a href="https://doi.org/${ref.doi}" target="_blank" class="text-blue-600 hover:underline">${ref.doi}</a></div>` : ''}
                    ${ref.citation_count ? `<div><strong>引用次数:</strong> ${ref.citation_count}</div>` : ''}
                    ${ref.abstract ? `<div><strong>摘要:</strong> ${ref.abstract}</div>` : ''}
                    ${ref.selection_reason ? `<div><strong>选择理由:</strong> ${ref.selection_reason}</div>` : ''}
                    ${ref.relevance ? `<div><strong>相关性:</strong> ${ref.relevance}</div>` : ''}
                    ${ref.error ? `<div class="text-red-600"><strong>错误:</strong> ${ref.error}</div>` : ''}
                </div>
                <div class="mt-3 p-2 bg-green-50 border border-green-200 rounded text-xs text-green-800">
                    ✅ <strong>标题引用：</strong>基于文献标题的引用更安全可靠，建议根据标题进一步查证完整文献信息。
                </div>
            `;

            return card;
        }

        function showFormattedText(aiResponse) {
            const resultsContainer = document.getElementById('results-container');

            const formattedTextHtml = `
                <div class="mt-6 bg-yellow-50 border border-yellow-200 rounded-lg p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <svg class="w-5 h-5 text-yellow-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                        </svg>
                        AI生成的带引用文本
                    </h3>
                    <div class="bg-white border border-gray-200 rounded p-4">
                        <div class="text-sm text-gray-700 whitespace-pre-wrap leading-relaxed">${aiResponse}</div>
                    </div>
                    <div class="mt-4 p-3 bg-blue-50 border border-blue-200 rounded">
                        <div class="flex">
                            <svg class="w-5 h-5 text-blue-400 mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <div class="text-sm text-blue-700">
                                <strong>📚 学术引用格式说明：</strong>
                                <p class="mt-1">系统使用标准的学术引用格式：</p>
                                <ul class="mt-2 list-disc list-inside space-y-1">
                                    <li>正文中使用序号引用：[1]、[2]、[3]等</li>
                                    <li>文末列出完整文献标题</li>
                                    <li>符合学术写作规范，便于查证</li>
                                    <li>可根据标题在学术数据库中搜索完整信息</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="mt-3 flex space-x-2">
                        <button onclick="copyToClipboard('${aiResponse.replace(/'/g, "\\'")}', this)"
                                class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            📋 复制文本
                        </button>
                        <button onclick="window.open('https://pubmed.ncbi.nlm.nih.gov/', '_blank')"
                                class="inline-flex items-center px-3 py-2 border border-blue-300 shadow-sm text-sm leading-4 font-medium rounded-md text-blue-700 bg-blue-50 hover:bg-blue-100">
                            🔍 验证PMID
                        </button>
                    </div>
                    <div class="mt-2 text-xs text-gray-500">
                        💡 提示：文本中使用[1]、[2]等序号标记引用位置，文末列出完整文献标题
                    </div>
                </div>
            `;

            const referencesList = document.getElementById('referencesList');
            referencesList.insertAdjacentHTML('afterend', formattedTextHtml);
        }

        function addSimpleReferenceList(references) {
            // 提取所有PMID（包括验证失败的）
            const allPmids = references
                .filter(ref => ref.pmid)
                .map((ref, index) => `${index + 1}.${ref.pmid}`)
                .join('\n');

            // 提取验证成功的PMID
            const verifiedPmids = references
                .filter(ref => ref.pmid && ref.verified === true)
                .map((ref, index) => `${index + 1}.${ref.pmid}`)
                .join('\n');

            if (allPmids) {
                const simpleListHtml = `
                    <div class="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                            <svg class="w-5 h-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                            PMID列表（便于验证）
                        </h3>

                        <div class="space-y-4">
                            <div>
                                <h4 class="text-sm font-medium text-gray-700 mb-2">所有PMID：</h4>
                                <div class="bg-white border border-gray-200 rounded p-4">
                                    <pre class="text-sm text-gray-700 whitespace-pre-wrap font-mono">${allPmids}</pre>
                                </div>
                                <div class="mt-2 flex space-x-2">
                                    <button onclick="copyToClipboard('${allPmids.replace(/'/g, "\\'")}', this)"
                                            class="inline-flex items-center px-3 py-1 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50">
                                        📋 复制全部
                                    </button>
                                </div>
                            </div>

                            ${verifiedPmids ? `
                            <div>
                                <h4 class="text-sm font-medium text-green-700 mb-2">已验证PMID：</h4>
                                <div class="bg-green-50 border border-green-200 rounded p-4">
                                    <pre class="text-sm text-green-700 whitespace-pre-wrap font-mono">${verifiedPmids}</pre>
                                </div>
                                <div class="mt-2 flex space-x-2">
                                    <button onclick="copyToClipboard('${verifiedPmids.replace(/'/g, "\\'")}', this)"
                                            class="inline-flex items-center px-3 py-1 border border-green-300 shadow-sm text-xs font-medium rounded text-green-700 bg-green-50 hover:bg-green-100">
                                        ✅ 复制已验证
                                    </button>
                                </div>
                            </div>
                            ` : ''}
                        </div>

                        <div class="mt-4 flex space-x-2">
                            <a href="https://pubmed.ncbi.nlm.nih.gov/" target="_blank"
                               class="inline-flex items-center px-3 py-2 border border-blue-300 shadow-sm text-sm leading-4 font-medium rounded-md text-blue-700 bg-blue-50 hover:bg-blue-100">
                                🔍 在PubMed中验证
                            </a>
                        </div>

                        <div class="mt-3 text-xs text-gray-500">
                            💡 提示：复制PMID列表，在PubMed中搜索验证文献真实性
                        </div>
                    </div>
                `;

                const referencesList = document.getElementById('referencesList');
                referencesList.insertAdjacentHTML('afterend', simpleListHtml);
            }
        }

        function copyToClipboard(text, button) {
            navigator.clipboard.writeText(text).then(function() {
                const originalText = button.innerHTML;
                button.innerHTML = `
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    已复制
                `;
                button.classList.add('text-green-600', 'border-green-300', 'bg-green-50');

                setTimeout(function() {
                    button.innerHTML = originalText;
                    button.classList.remove('text-green-600', 'border-green-300', 'bg-green-50');
                }, 2000);
            }).catch(function(err) {
                console.error('复制失败:', err);
                alert('复制失败，请手动复制');
            });
        }

        // 全局变量存储当前的参考文献数据
        let currentReferences = [];

        function exportToNBIB() {
            console.log('exportToNBIB called, currentReferences:', currentReferences);
            if (!currentReferences || currentReferences.length === 0) {
                alert('没有可导出的参考文献');
                return;
            }

            // 检查是否有验证成功的文献
            const verifiedRefs = currentReferences.filter(ref => ref.verified);
            if (verifiedRefs.length === 0) {
                alert('没有验证成功的文献可导出');
                return;
            }

            // 显示加载状态
            const button = event.target;
            const originalText = button.innerHTML;
            button.innerHTML = `
                <svg class="w-4 h-4 mr-2 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                导出中...
            `;
            button.disabled = true;

            // 发送导出请求
            fetch('/api/export/nbib', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    references: currentReferences
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 创建下载链接
                    const blob = new Blob([data.content], { type: 'text/plain;charset=utf-8' });
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = data.filename;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    window.URL.revokeObjectURL(url);

                    // 显示成功消息
                    alert(data.message);
                } else {
                    alert('导出失败: ' + data.error);
                }
            })
            .catch(error => {
                console.error('导出失败:', error);
                alert('导出失败，请稍后重试');
            })
            .finally(() => {
                // 恢复按钮状态
                button.innerHTML = originalText;
                button.disabled = false;
            });
        }

        function showError(message) {
            document.getElementById('progressSection').classList.add('hidden');
            document.getElementById('errorSection').classList.remove('hidden');
            document.getElementById('errorMessage').textContent = message;
        }

        function resetUI() {
            document.getElementById('matchBtn').disabled = false;
            document.getElementById('matchBtn').textContent = '🔍 开始匹配';
            currentTaskId = null;
        }
    </script>
</body>
</html>
