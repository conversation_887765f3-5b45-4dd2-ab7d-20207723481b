<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>深度分析工具箱 - NNScholar</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #64748b;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --background-color: #f8fafc;
            --card-background: #ffffff;
            --text-primary: #1e293b;
            --text-secondary: #64748b;
            --border-color: #e2e8f0;
        }

        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 1.5rem;
        }

        .header {
            text-align: center;
            margin-bottom: 2rem;
            color: white;
        }

        .header h1 {
            font-size: 2.2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .toolbox-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .tool-card {
            background: var(--card-background);
            border-radius: 12px;
            padding: 1.2rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            border: 1px solid var(--border-color);
            cursor: pointer;
            position: relative;
            overflow: hidden;
            height: fit-content;
        }

        .tool-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--primary-color), var(--success-color));
        }

        .tool-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }

        .tool-icon {
            font-size: 2.2rem;
            color: var(--primary-color);
            margin-bottom: 0.8rem;
        }

        .tool-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.4rem;
        }

        .tool-description {
            color: var(--text-secondary);
            line-height: 1.4;
            margin-bottom: 1rem;
            font-size: 0.9rem;
        }

        .tool-features {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .tool-features li {
            padding: 0.2rem 0;
            color: var(--text-secondary);
            font-size: 0.8rem;
        }

        .tool-features li::before {
            content: '✓';
            color: var(--success-color);
            font-weight: bold;
            margin-right: 0.4rem;
        }

        .back-btn {
            position: fixed;
            top: 2rem;
            left: 2rem;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 50px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            z-index: 1000;
        }

        .back-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateX(-5px);
        }

        .category-section {
            margin-bottom: 1.5rem;
        }

        .category-title {
            color: white;
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
            text-align: center;
            position: relative;
        }

        .category-title::after {
            content: '';
            position: absolute;
            bottom: -0.3rem;
            left: 50%;
            transform: translateX(-50%);
            width: 50px;
            height: 2px;
            background: linear-gradient(90deg, var(--warning-color), var(--success-color));
            border-radius: 2px;
        }

        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .toolbox-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }
            
            .tool-card {
                padding: 1.5rem;
            }
            
            .back-btn {
                top: 1rem;
                left: 1rem;
                padding: 0.5rem 1rem;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <button class="back-btn" onclick="window.history.back()">
        <i class="fas fa-arrow-left me-2"></i>返回
    </button>

    <div class="container">
        <div class="header">
            <h1><i class="fas fa-microscope me-3"></i>深度分析工具箱</h1>
            <p>学术研究全流程AI智能分析工具集</p>
        </div>

        <!-- 学术工具箱 -->
        <div class="category-section">
            <div class="toolbox-grid">
                <div class="tool-card" onclick="openTool('/analysis/journal-selection')">
                    <div class="tool-icon">
                        <i class="fas fa-journal-whills"></i>
                    </div>
                    <h3 class="tool-title">AI投稿选刊</h3>
                    <p class="tool-description">基于论文内容智能推荐最适合的期刊，提高投稿成功率</p>
                    <ul class="tool-features">
                        <li>智能期刊匹配</li>
                        <li>影响因子分析</li>
                        <li>投稿建议</li>
                    </ul>
                </div>

                <div class="tool-card" onclick="openTool('/analysis/paper-translation')">
                    <div class="tool-icon">
                        <i class="fas fa-language"></i>
                    </div>
                    <h3 class="tool-title">论文翻译</h3>
                    <p class="tool-description">专业学术论文翻译，保持学术表达的准确性和专业性</p>
                    <ul class="tool-features">
                        <li>学术术语准确翻译</li>
                        <li>语法结构优化</li>
                        <li>格式保持</li>
                    </ul>
                </div>

                <div class="tool-card" onclick="openTool('/analysis/paper-polish')">
                    <div class="tool-icon">
                        <i class="fas fa-edit"></i>
                    </div>
                    <h3 class="tool-title">论文润色</h3>
                    <p class="tool-description">提升论文语言表达质量，增强学术写作的专业性</p>
                    <ul class="tool-features">
                        <li>语言表达优化</li>
                        <li>逻辑结构调整</li>
                        <li>学术规范检查</li>
                    </ul>
                </div>

                <div class="tool-card" onclick="openTool('/analysis/cover-letter')">
                    <div class="tool-icon">
                        <i class="fas fa-envelope"></i>
                    </div>
                    <h3 class="tool-title">Cover Letter生成</h3>
                    <p class="tool-description">生成专业的投稿信，提高期刊编辑的关注度</p>
                    <ul class="tool-features">
                        <li>个性化投稿信</li>
                        <li>突出研究亮点</li>
                        <li>专业格式规范</li>
                    </ul>
                </div>

                <div class="tool-card" onclick="openTool('/analysis/statistical-analysis')">
                    <div class="tool-icon">
                        <i class="fas fa-chart-bar"></i>
                    </div>
                    <h3 class="tool-title">统计分析专家</h3>
                    <p class="tool-description">专业统计分析指导，帮助您选择合适的统计方法</p>
                    <ul class="tool-features">
                        <li>统计方法选择</li>
                        <li>数据分析指导</li>
                        <li>结果解释建议</li>
                    </ul>
                </div>

                <div class="tool-card" onclick="openTool('/analysis/visualization-expert')">
                    <div class="tool-icon">
                        <i class="fas fa-chart-pie"></i>
                    </div>
                    <h3 class="tool-title">绘图分析专家</h3>
                    <p class="tool-description">为您的数据推荐最佳的可视化方案和图表类型</p>
                    <ul class="tool-features">
                        <li>图表类型推荐</li>
                        <li>可视化设计</li>
                        <li>美化建议</li>
                    </ul>
                </div>

                <div class="tool-card" onclick="openTool('/analysis/reference-matching')">
                    <div class="tool-icon">
                        <i class="fas fa-link"></i>
                    </div>
                    <h3 class="tool-title">引文真实性验证</h3>
                    <p class="tool-description">检测学术内容中的虚假文献，确保引用的真实性</p>
                    <ul class="tool-features">
                        <li>文献匹配检查</li>
                        <li>引用规范建议</li>
                    </ul>
                </div>
            </div>
        </div>


    </div>

    <script>
        function openTool(url) {
            window.open(url, '_blank');
        }

        // 添加页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.tool-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    card.style.transition = 'all 0.5s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
    </script>
</body>
</html>
