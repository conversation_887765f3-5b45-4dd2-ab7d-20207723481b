<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>绘图建议专家 - NNScholar</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #8b5cf6, #7c3aed);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2rem;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .header p {
            opacity: 0.9;
            font-size: 1.1rem;
        }

        .content {
            padding: 40px;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-group label {
            display: block;
            font-weight: 600;
            color: #374151;
            margin-bottom: 8px;
            font-size: 1rem;
        }

        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e5e7eb;
            border-radius: 10px;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .form-group input:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            outline: none;
            border-color: #8b5cf6;
            box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
        }

        .form-group textarea {
            resize: vertical;
            min-height: 100px;
        }

        .checkbox-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 12px;
            margin-top: 8px;
        }

        .checkbox-item {
            display: flex;
            align-items: center;
            padding: 12px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .checkbox-item:hover {
            border-color: #8b5cf6;
            background: #faf5ff;
        }

        .checkbox-item input[type="checkbox"] {
            width: auto;
            margin-right: 8px;
        }

        .submit-btn {
            background: linear-gradient(135deg, #8b5cf6, #7c3aed);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            width: 100%;
            transition: all 0.3s ease;
            margin-top: 20px;
        }

        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(139, 92, 246, 0.3);
        }

        .submit-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .result-section {
            margin-top: 30px;
            padding: 20px;
            background: #f8fafc;
            border-radius: 10px;
            border-left: 4px solid #8b5cf6;
            display: none;
        }

        .result-section h3 {
            color: #8b5cf6;
            margin-bottom: 15px;
        }

        .result-content {
            line-height: 1.6;
            color: #374151;
        }

        .loading {
            text-align: center;
            padding: 20px;
            color: #6b7280;
        }

        .back-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.9);
            border: none;
            padding: 10px 15px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            background: white;
            transform: translateY(-1px);
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 15px;
            }
            
            .content {
                padding: 20px;
            }
            
            .checkbox-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <button class="back-btn" onclick="window.close()">← 返回</button>
    
    <div class="container">
        <div class="header">
            <h1>📊 绘图建议专家</h1>
            <p>为您的数据选择最合适的图表和可视化方法</p>
        </div>
        
        <div class="content">
            <form id="visualizationExpertForm">
                <div class="form-group">
                    <label for="visualizationPurpose">🎯 可视化目的 *</label>
                    <textarea id="visualizationPurpose" required 
                              placeholder="请描述您想要通过图表展示什么，例如：比较不同治疗组的疗效差异、展示变量间的相关关系等"></textarea>
                </div>

                <div class="form-group">
                    <label>📊 数据特征</label>
                    <div class="checkbox-grid">
                        <div class="checkbox-item">
                            <input type="checkbox" id="numerical" value="numerical">
                            <label for="numerical">数值型数据</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="categorical" value="categorical">
                            <label for="categorical">分类数据</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="timeBased" value="time-based">
                            <label for="timeBased">时间序列</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="geographical" value="geographical">
                            <label for="geographical">地理数据</label>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="variableCount">🔢 变量数量</label>
                    <select id="variableCount">
                        <option value="">请选择变量数量</option>
                        <option value="1">单变量</option>
                        <option value="2">双变量</option>
                        <option value="3">三变量</option>
                        <option value="multiple">多变量</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="targetAudience">👥 目标受众</label>
                    <select id="targetAudience">
                        <option value="">请选择目标受众</option>
                        <option value="academic">学术期刊</option>
                        <option value="conference">学术会议</option>
                        <option value="clinical">临床医生</option>
                        <option value="public">公众科普</option>
                        <option value="policy">政策制定者</option>
                        <option value="other">其他</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="dataDescription">📝 数据描述</label>
                    <textarea id="dataDescription" 
                              placeholder="请简要描述您的数据，包括样本量、主要变量、数据分布等（可选）"></textarea>
                </div>

                <button type="submit" class="submit-btn" id="submitBtn">
                    🎨 获取绘图建议
                </button>
            </form>

            <div class="result-section" id="resultSection">
                <h3>🎨 绘图建议</h3>
                <div class="result-content" id="resultContent"></div>
            </div>
        </div>
    </div>

    <script>
        document.getElementById('visualizationExpertForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const submitBtn = document.getElementById('submitBtn');
            const resultSection = document.getElementById('resultSection');
            const resultContent = document.getElementById('resultContent');
            
            // 获取表单数据
            const formData = {
                visualizationPurpose: document.getElementById('visualizationPurpose').value,
                dataCharacteristics: Array.from(document.querySelectorAll('input[type="checkbox"]:checked')).map(cb => cb.value),
                variableCount: document.getElementById('variableCount').value,
                targetAudience: document.getElementById('targetAudience').value,
                dataDescription: document.getElementById('dataDescription').value
            };
            
            if (!formData.visualizationPurpose.trim()) {
                alert('请填写可视化目的');
                return;
            }
            
            // 显示加载状态
            submitBtn.disabled = true;
            submitBtn.textContent = '🔄 分析中...';
            resultSection.style.display = 'block';
            resultContent.innerHTML = '<div class="loading">🔄 绘图建议专家正在为您分析，请稍候...</div>';
            
            try {
                // 构建专业提示词
                const prompt = `作为数据可视化专家，请为以下需求提供最合适的图表和可视化建议：

可视化目的：${formData.visualizationPurpose}
数据特征：${formData.dataCharacteristics.join(', ') || '未指定'}
变量数量：${formData.variableCount || '未指定'}
目标受众：${formData.targetAudience || '未指定'}
数据描述：${formData.dataDescription || '无'}

请提供以下内容：
1. 推荐的图表类型及其理由
2. 图表设计要点（颜色、布局、标注等）
3. 数据预处理建议
4. 统计图形的最佳实践
5. 不同软件的实现方法（R、Python、SPSS等）
6. 图表美化和专业化建议
7. 常见错误和避免方法
8. 针对目标受众的展示技巧

请确保建议实用且符合数据可视化的最佳实践。`;
                
                // 发送请求
                const response = await fetch('/api/deepseek_analysis', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        prompt: prompt,
                        expert_type: '绘图建议专家'
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    // 格式化显示结果
                    const formattedResult = data.analysis.replace(/\n/g, '<br>').replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
                    resultContent.innerHTML = formattedResult;
                } else {
                    resultContent.innerHTML = `<div style="color: #ef4444;">❌ 分析失败: ${data.error}</div>`;
                }
            } catch (error) {
                console.error('分析错误:', error);
                resultContent.innerHTML = `<div style="color: #ef4444;">❌ 分析过程中出现错误: ${error.message}</div>`;
            } finally {
                submitBtn.disabled = false;
                submitBtn.textContent = '🎨 获取绘图建议';
            }
        });
    </script>
</body>
</html>
