<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NNScholar - 医学文献智能检索助手</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.socket.io/4.7.2/socket.io.min.js"></script>
    <style>
        :root {
            --primary-color: #3b82f6;
            --secondary-color: #6b7280;
            --background-color: #f8fafc;
            --sidebar-bg: #1e293b;
            --sidebar-hover: #334155;
            --text-color: #1f2937;
            --text-muted: #6b7280;
            --border-color: #e5e7eb;
            --chat-background: #ffffff;
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--background-color);
            color: var(--text-color);
            overflow: hidden;
        }

        /* 应用容器 */
        .app-container {
            display: flex;
            height: 100vh;
        }

        /* 左侧边栏 */
        .sidebar {
            width: 280px;
            background: var(--sidebar-bg);
            color: white;
            display: flex;
            flex-direction: column;
            transition: width 0.3s ease;
        }

        .sidebar.collapsed {
            width: 60px;
        }

        .sidebar-header {
            padding: 1rem;
            border-bottom: 1px solid #334155;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo-section {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .logo {
            width: 32px;
            height: 32px;
            background: var(--primary-color);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 1.2rem;
        }

        .app-name {
            font-weight: 600;
            font-size: 1.1rem;
            transition: opacity 0.3s ease;
        }

        .sidebar.collapsed .app-name {
            opacity: 0;
            width: 0;
            overflow: hidden;
        }

        .new-chat-btn {
            background: var(--primary-color);
            color: white;
            border: none;
            width: 32px;
            height: 32px;
            border-radius: 6px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
        }

        .new-chat-btn:hover {
            background: #2563eb;
            transform: scale(1.05);
        }

        .sidebar-content {
            flex: 1;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            padding: 1rem;
        }

        .history-search {
            margin-bottom: 1rem;
        }

        .history-search input {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #334155;
            border-radius: 6px;
            background: #334155;
            color: white;
            font-size: 0.875rem;
        }

        .history-search input::placeholder {
            color: #94a3b8;
        }

        .history-search input:focus {
            outline: none;
            border-color: var(--primary-color);
        }

        .history-list {
            flex: 1;
            overflow-y: auto;
        }

        .history-item {
            padding: 0.75rem;
            border-radius: 6px;
            margin-bottom: 0.5rem;
            cursor: pointer;
            transition: all 0.2s ease;
            border: 1px solid transparent;
        }

        .history-item:hover {
            background: var(--sidebar-hover);
        }

        .history-item.active {
            background: var(--primary-color);
        }

        .history-item-title {
            font-weight: 500;
            font-size: 0.875rem;
            margin-bottom: 0.25rem;
            line-height: 1.4;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .history-item-time {
            font-size: 0.75rem;
            color: #94a3b8;
        }

        .sidebar-footer {
            padding: 1rem;
            border-top: 1px solid #334155;
        }

        .sidebar-btn {
            width: 100%;
            background: none;
            border: none;
            color: #94a3b8;
            padding: 0.75rem;
            border-radius: 6px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            margin-bottom: 0.5rem;
            transition: all 0.2s ease;
            font-size: 0.875rem;
        }

        .sidebar-btn:hover {
            background: var(--sidebar-hover);
            color: white;
        }

        .sidebar.collapsed .sidebar-btn .text {
            display: none;
        }

        /* 主内容区域 */
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            position: relative;
        }

        /* 初始欢迎界面 */
        .welcome-screen {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100vh;
            padding: 2rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
        }

        .welcome-screen.hidden {
            display: none;
        }

        .welcome-title {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 1rem;
            background: linear-gradient(45deg, #fff, #e0e7ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .welcome-subtitle {
            font-size: 1.2rem;
            margin-bottom: 3rem;
            opacity: 0.9;
            max-width: 600px;
            line-height: 1.6;
        }

        .welcome-search {
            width: 100%;
            max-width: 600px;
            position: relative;
        }

        .welcome-input {
            width: 100%;
            padding: 1.5rem 4rem 1.5rem 1.5rem;
            border: none;
            border-radius: 1rem;
            font-size: 1.1rem;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .welcome-input:focus {
            outline: none;
            background: white;
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
            transform: translateY(-2px);
        }

        .welcome-send-btn {
            position: absolute;
            right: 0.75rem;
            top: 50%;
            transform: translateY(-50%);
            background: var(--primary-color);
            color: white;
            border: none;
            width: 48px;
            height: 48px;
            border-radius: 0.75rem;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            transition: all 0.2s ease;
        }

        .welcome-send-btn:hover {
            background: #2563eb;
            transform: translateY(-50%) scale(1.05);
        }

        .welcome-send-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: translateY(-50%);
        }

        /* 聊天界面 */
        .chat-container {
            display: none;
            flex-direction: column;
            height: 100vh;
            background: var(--chat-background);
        }

        .chat-container.active {
            display: flex;
        }

        .chat-header {
            padding: 1rem 1.5rem;
            border-bottom: 1px solid var(--border-color);
            background: var(--chat-background);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .chat-title {
            font-weight: 600;
            font-size: 1.1rem;
            color: var(--text-color);
        }

        .chat-actions {
            display: flex;
            gap: 0.5rem;
        }

        .action-btn {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            cursor: pointer;
            font-size: 0.875rem;
            transition: all 0.2s ease;
        }

        .action-btn:hover {
            background: #2563eb;
            transform: translateY(-1px);
        }

        /* 深度分析版块 */
        .analysis-section {
            background: var(--background-color);
            border-bottom: 1px solid var(--border-color);
            padding: 1rem;
            max-height: 400px;
            overflow-y: auto;
        }

        .analysis-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 0.75rem;
            padding-right: 0.5rem;
        }

        .analysis-card {
            background: white;
            border: 1px solid var(--border-color);
            border-radius: 0.75rem;
            padding: 0.75rem;
            cursor: pointer;
            transition: all 0.2s ease;
            text-align: center;
            min-height: 100px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .analysis-card:hover {
            border-color: var(--primary-color);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        /* 聊天消息区域 */
        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 1rem;
            background: var(--background-color);
        }

        .message {
            display: flex;
            margin-bottom: 1rem;
            align-items: flex-start;
            gap: 0.75rem;
        }

        .message.user {
            flex-direction: row-reverse;
        }

        .message-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 0.875rem;
            flex-shrink: 0;
        }

        .message.user .message-avatar {
            background: var(--primary-color);
            color: white;
        }

        .message.assistant .message-avatar {
            background: #10b981;
            color: white;
        }

        .message-content {
            max-width: 70%;
            padding: 1rem;
            border-radius: 1rem;
            line-height: 1.6;
        }

        .message.user .message-content {
            background: var(--primary-color);
            color: white;
            border-bottom-right-radius: 0.25rem;
        }

        .message.assistant .message-content {
            background: white;
            border: 1px solid var(--border-color);
            border-bottom-left-radius: 0.25rem;
        }

        /* 进度消息样式 */
        .progress-message {
            display: flex;
            align-items: center;
            gap: 10px;
            color: #6b7280;
            font-style: italic;
        }

        .progress-spinner {
            width: 16px;
            height: 16px;
            border: 2px solid #e5e7eb;
            border-top: 2px solid #3b82f6;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 综述结果样式 */
        .review-result {
            max-width: 100%;
        }

        .review-meta {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin: 15px 0;
            padding: 10px;
            background: #f8fafc;
            border-radius: 8px;
            font-size: 14px;
            color: #6b7280;
        }

        .review-content {
            margin: 20px 0;
            line-height: 1.8;
        }

        .review-content h1, .review-content h2, .review-content h3 {
            margin: 20px 0 10px 0;
            color: #1f2937;
        }

        .review-content h1 {
            font-size: 24px;
            border-bottom: 2px solid #e5e7eb;
            padding-bottom: 10px;
        }

        .review-content h2 {
            font-size: 20px;
            color: #374151;
        }

        .review-content h3 {
            font-size: 18px;
            color: #4b5563;
        }

        .review-content p {
            margin: 10px 0;
            text-align: justify;
        }

        .review-content ul, .review-content ol {
            margin: 10px 0 10px 20px;
        }

        .review-actions {
            margin-top: 20px;
            padding-top: 15px;
            border-top: 1px solid #e5e7eb;
        }

        .export-btn {
            padding: 8px 16px;
            background: #3b82f6;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s ease;
        }

        .export-btn:hover {
            background: #2563eb;
        }

        /* 输入区域 */
        .input-container {
            padding: 1rem;
            border-top: 1px solid var(--border-color);
            background: white;
        }

        .input-wrapper {
            display: flex;
            gap: 0.75rem;
            max-width: 800px;
            margin: 0 auto;
        }

        .input-wrapper input {
            flex: 1;
            padding: 1rem;
            border: 1px solid var(--border-color);
            border-radius: 1rem;
            font-size: 1rem;
            transition: all 0.2s ease;
        }

        .input-wrapper input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .input-wrapper button {
            background: var(--primary-color);
            color: white;
            border: none;
            width: 48px;
            height: 48px;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            transition: all 0.2s ease;
        }

        .input-wrapper button:hover {
            background: #2563eb;
            transform: scale(1.05);
        }

        .input-wrapper button:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }

        /* 设置按钮样式 */
        .settings-btn {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 50px;
            height: 50px;
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            transition: all 0.3s ease;
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
        }

        .settings-btn:hover {
            background: #2563eb;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
        }

        /* 设置面板样式 */
        .settings-panel {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 2000;
            display: none;
            align-items: center;
            justify-content: center;
        }

        .settings-content {
            background: white;
            border-radius: 1rem;
            padding: 2rem;
            max-width: 500px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        .settings-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid var(--border-color);
        }

        .settings-header h3 {
            margin: 0;
            color: var(--text-color);
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: var(--text-muted);
            padding: 0.25rem;
            border-radius: 0.25rem;
            transition: all 0.2s ease;
        }

        .close-btn:hover {
            background: var(--background-color);
            color: var(--text-color);
        }

        .settings-group {
            margin-bottom: 1.5rem;
        }

        .settings-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: var(--text-color);
        }

        .settings-group input,
        .settings-group select {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid var(--border-color);
            border-radius: 0.5rem;
            font-size: 0.875rem;
            transition: border-color 0.2s ease;
        }

        .settings-group input:focus,
        .settings-group select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .settings-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }

        .checkbox-group {
            display: flex;
            flex-wrap: wrap;
            gap: 0.75rem;
            margin-top: 0.5rem;
        }

        .checkbox-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .checkbox-item input[type="checkbox"] {
            width: auto;
            margin: 0;
        }

        .settings-actions {
            display: flex;
            gap: 1rem;
            justify-content: flex-end;
            margin-top: 2rem;
            padding-top: 1rem;
            border-top: 1px solid var(--border-color);
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 0.5rem;
            cursor: pointer;
            font-size: 0.875rem;
            font-weight: 600;
            transition: all 0.2s ease;
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background: #2563eb;
        }

        .btn-secondary {
            background: var(--background-color);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .btn-secondary:hover {
            background: #f3f4f6;
        }

        /* 检索结果样式 */
        .search-results {
            max-width: 100%;
            margin: 1rem 0;
        }

        .results-header {
            background: #f8fafc;
            padding: 1rem;
            border-radius: 0.5rem;
            margin-bottom: 1rem;
            border-left: 4px solid var(--primary-color);
        }

        .results-header h3 {
            margin: 0 0 0.5rem 0;
            color: var(--primary-color);
        }

        .results-header p {
            margin: 0.25rem 0;
            color: var(--text-muted);
            font-size: 0.875rem;
        }

        .papers-list {
            max-height: 600px;
            overflow-y: auto;
            background: white;
        }

        /* Bootstrap卡片样式覆盖 */
        .search-results .card {
            border: 1px solid var(--border-color);
            border-radius: 0.5rem;
            box-shadow: var(--shadow-sm);
            transition: box-shadow 0.2s ease;
        }

        .search-results .card:hover {
            box-shadow: var(--shadow-md);
        }

        .search-results .card-body {
            padding: 1.25rem;
        }

        .search-results .card-title {
            margin-bottom: 1rem;
            font-size: 1.1rem;
            font-weight: 600;
            line-height: 1.4;
        }

        .search-results .card-title a {
            color: var(--text-color);
            text-decoration: none;
        }

        .search-results .card-title a:hover {
            color: var(--primary-color);
        }

        .search-results .badge {
            font-size: 0.75rem;
            padding: 0.375rem 0.75rem;
        }

        .search-results .text-muted {
            color: var(--text-muted) !important;
            font-size: 0.875rem;
        }

        .search-results .card-text {
            font-size: 0.875rem;
            line-height: 1.6;
            margin-bottom: 0;
        }

        .paper-item {
            padding: 1rem;
            border-bottom: 1px solid var(--border-color);
            transition: background-color 0.2s ease;
        }

        .paper-item:hover {
            background: #f8fafc;
        }

        .paper-item:last-child {
            border-bottom: none;
        }

        .paper-header {
            display: flex;
            align-items: flex-start;
            gap: 0.75rem;
            margin-bottom: 0.5rem;
        }

        .paper-number {
            background: var(--primary-color);
            color: white;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.75rem;
            font-weight: bold;
            flex-shrink: 0;
        }

        .paper-title {
            margin: 0;
            font-size: 1rem;
            font-weight: 600;
            color: var(--text-color);
            line-height: 1.4;
        }

        .paper-meta {
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
            margin-bottom: 0.5rem;
            font-size: 0.875rem;
            color: var(--text-muted);
        }

        .paper-abstract {
            font-size: 0.875rem;
            color: var(--text-color);
            line-height: 1.5;
            margin-bottom: 0.5rem;
            background: #f8fafc;
            padding: 0.75rem;
            border-radius: 0.25rem;
        }

        .paper-metrics {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .metric {
            background: #e0e7ff;
            color: #3730a3;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .results-actions {
            display: flex;
            gap: 0.75rem;
            margin-top: 1rem;
            flex-wrap: wrap;
        }

        .results-actions .action-btn {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            cursor: pointer;
            font-size: 0.875rem;
            transition: all 0.2s ease;
        }

        .results-actions .action-btn:hover {
            background: #2563eb;
            transform: translateY(-1px);
        }

        /* 智能问题推荐样式 */
        .smart-questions {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-radius: 1rem;
            padding: 1.5rem;
            border: 1px solid var(--border-color);
        }

        .smart-questions h6 {
            color: #374151;
            font-weight: 600;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .question-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 0.75rem;
        }

        .question-card {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 0.75rem;
            padding: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .question-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            border-color: var(--primary-color);
        }

        .question-card i {
            font-size: 1.2rem;
            width: 24px;
            text-align: center;
        }

        .question-card span {
            font-weight: 500;
            color: #374151;
            font-size: 0.9rem;
        }

        .question-card:hover span {
            color: var(--primary-color);
        }

        /* 进度条样式 */
        .progress-info {
            padding: 1rem;
            background: #f8fafc;
            border-radius: 0.5rem;
            border-left: 4px solid var(--primary-color);
        }

        .progress-message {
            margin-bottom: 0.75rem;
            font-weight: 500;
            color: var(--text-color);
        }

        .progress-bar-container {
            background: #e5e7eb;
            border-radius: 0.5rem;
            height: 8px;
            overflow: hidden;
            margin-bottom: 0.5rem;
        }

        .progress-bar {
            background: linear-gradient(90deg, var(--primary-color), #2563eb);
            height: 100%;
            border-radius: 0.5rem;
            transition: width 0.3s ease;
            position: relative;
        }

        .progress-bar::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .progress-percentage {
            text-align: center;
            font-size: 0.875rem;
            font-weight: 600;
            color: var(--primary-color);
        }

        /* Toast样式 */
        .toast {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                left: -280px;
                z-index: 1000;
                transition: left 0.3s ease;
            }

            .sidebar.open {
                left: 0;
            }

            .main-content {
                width: 100%;
            }

            .welcome-title {
                font-size: 2rem;
            }

            .welcome-subtitle {
                font-size: 1rem;
            }
        }

        /* 新布局样式 */
        .global-summary {
            margin-bottom: 20px;
        }

        .summary-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .summary-card h4 {
            margin-bottom: 15px;
            font-weight: 600;
        }

        .summary-text {
            font-size: 1.1em;
            margin-bottom: 10px;
        }

        .smart-recommendations-section {
            margin: 20px 0;
        }

        .recommendations-title {
            color: #495057;
            margin-bottom: 20px;
            text-align: center;
        }

        .recommendation-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .rec-card {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 12px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .rec-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.1);
            border-color: #007bff;
        }

        .rec-icon {
            font-size: 2em;
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f8f9fa;
            border-radius: 50%;
        }

        .rec-content h6 {
            margin: 0 0 5px 0;
            color: #495057;
            font-weight: 600;
        }

        .rec-content p {
            margin: 0;
            color: #6c757d;
            font-size: 0.9em;
        }

        .full-literature-section {
            margin-top: 30px;
            border-top: 2px solid #e9ecef;
            padding-top: 20px;
        }

        .literature-header {
            cursor: pointer;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
            transition: all 0.3s ease;
        }

        .literature-header:hover {
            background: #e9ecef;
        }

        .literature-header h5 {
            margin: 0;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .toggle-icon {
            transition: transform 0.3s ease;
        }

        .literature-list-container {
            margin-top: 15px;
        }

        /* 推荐文献卡片样式 */
        .recommended-paper-card {
            border: 2px solid #e9ecef;
            border-radius: 15px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .recommended-paper-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            border-color: #007bff;
        }

        .paper-header {
            display: flex;
            align-items: flex-start;
            gap: 15px;
            margin-bottom: 15px;
        }

        .paper-rank {
            background: linear-gradient(135deg, #ffd700, #ffed4e);
            color: #333;
            font-weight: bold;
            font-size: 1.2em;
            padding: 8px 12px;
            border-radius: 50%;
            min-width: 40px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(255, 215, 0, 0.3);
        }

        .title-bilingual .title-english {
            font-size: 1.1em;
            color: #495057;
            margin-bottom: 5px;
        }

        .title-bilingual .title-chinese {
            font-size: 1em;
            color: #6c757d;
            font-style: italic;
        }

        .paper-meta {
            background: #f8f9fa;
            padding: 12px;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }

        .meta-row {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-bottom: 8px;
        }

        .meta-row:last-child {
            margin-bottom: 0;
        }

        .meta-item {
            color: #495057;
            font-size: 0.9em;
        }

        .meta-item strong {
            color: #333;
        }

        .paper-badges {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        .paper-summary {
            background: #f0f8ff;
            padding: 15px;
            border-radius: 10px;
            border-left: 4px solid #17a2b8;
        }

        .paper-summary h6 {
            color: #17a2b8;
            margin-bottom: 10px;
            font-weight: 600;
        }

        .summary-text {
            color: #495057;
            line-height: 1.6;
            margin: 0;
        }

        .recommendation-reason {
            background: #fff3cd;
            padding: 15px;
            border-radius: 10px;
            border-left: 4px solid #ffc107;
        }

        .recommendation-reason h6 {
            color: #856404;
            margin-bottom: 10px;
            font-weight: 600;
        }

        .reason-content {
            color: #495057;
            line-height: 1.6;
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- 左侧边栏 -->
        <div class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <div class="logo-section">
                    <a href="/" style="text-decoration: none; color: inherit; display: flex; align-items: center; gap: 0.75rem;">
                        <div class="logo">N</div>
                        <span class="app-name">NNScholar</span>
                    </a>
                </div>
                <button class="new-chat-btn" onclick="startNewSession()" title="新会话">
                    ➕
                </button>
            </div>
            
            <div class="sidebar-content">
                <div class="history-search">
                    <input type="text" id="historySearch" placeholder="搜索历史会话..." onkeyup="searchHistory()">
                </div>
                <div class="history-list" id="historyList">
                    <!-- 历史会话将在这里动态加载 -->
                </div>
            </div>
            
            <div class="sidebar-footer">
                <button class="sidebar-btn" onclick="clearAllHistory()" title="清空历史">
                    <span class="icon">🗑️</span>
                    <span class="text">清空历史</span>
                </button>
                <button class="sidebar-btn" onclick="toggleSidebar()" title="收起侧边栏">
                    <span class="icon">◀</span>
                    <span class="text">收起</span>
                </button>
            </div>
        </div>

        <!-- 主内容区域 -->
        <div class="main-content">
            <!-- 欢迎界面 -->
            <div class="welcome-screen" id="welcomeScreen">
                <h1 class="welcome-title">我是 NNScholar，很高兴见到你！</h1>
                <p class="welcome-subtitle">
                    我可以帮你进行医学文献检索、研究现状分析、论文写作指导等学术研究工作。
                    请输入你的研究问题或关键词开始对话～
                </p>
                <div class="welcome-search">
                    <input type="text" class="welcome-input" id="welcomeInput" placeholder="输入研究问题或关键词..." onkeypress="handleWelcomeEnter(event)">
                    <button class="welcome-send-btn" onclick="startFirstSearch()" id="welcomeSendBtn">
                        🚀
                    </button>
                </div>
            </div>

            <!-- 聊天界面 -->
            <div class="chat-container" id="chatContainer">
                <div class="chat-header">
                    <div class="chat-title">NNScholar 医学文献智能检索助手</div>
                    <div class="chat-actions">
                        <button class="action-btn" onclick="toggleAnalysisSection()">
                            🔬 深度分析
                        </button>
                    </div>
                </div>
                
                <!-- 深度分析版块 -->
                <div class="analysis-section" id="analysisSection" style="display: none;">
                    <div style="text-align: center; margin-bottom: 0.5rem; color: var(--text-muted); font-size: 0.75rem;">
                        🚧 学术研究全流程工具箱 (功能开发中，点击查看详情) 🚧
                    </div>
                    <div class="analysis-grid">
                        <!-- 分析功能卡片将在这里动态加载 -->
                    </div>
                </div>

                <!-- 聊天消息区域 -->
                <div class="chat-messages" id="chatMessages">
                    <!-- 聊天消息将在这里显示 -->
                </div>

                <!-- 输入区域 -->
                <div class="input-container">
                    <div class="input-wrapper">
                        <input type="text" id="messageInput" placeholder="输入您的问题..." onkeypress="handleEnter(event)">
                        <button id="sendButton" onclick="sendMessage()">
                            <span>🚀</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 设置按钮 -->
    <button class="settings-btn" onclick="openSettings()" title="检索设置">
        ⚙️
    </button>

    <!-- 设置面板 -->
    <div class="settings-panel" id="settingsPanel">
        <div class="settings-content">
            <div class="settings-header">
                <h3>🔧 检索设置</h3>
                <button class="close-btn" onclick="closeSettings()">×</button>
            </div>

            <form id="settingsForm">
                <!-- 影响因子设置 -->
                <div class="settings-group">
                    <label>📊 影响因子范围</label>
                    <div class="settings-row">
                        <div>
                            <input type="number" id="impactFactorMin" placeholder="最小值" step="0.1" min="0">
                        </div>
                        <div>
                            <input type="number" id="impactFactorMax" placeholder="最大值" step="0.1" min="0">
                        </div>
                    </div>
                </div>

                <!-- JCR分区设置 -->
                <div class="settings-group">
                    <label>🏆 JCR分区</label>
                    <div class="checkbox-group">
                        <div class="checkbox-item">
                            <input type="checkbox" id="jcrQ1" value="Q1" checked>
                            <label for="jcrQ1">Q1</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="jcrQ2" value="Q2" checked>
                            <label for="jcrQ2">Q2</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="jcrQ3" value="Q3" checked>
                            <label for="jcrQ3">Q3</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="jcrQ4" value="Q4" checked>
                            <label for="jcrQ4">Q4</label>
                        </div>
                    </div>
                </div>

                <!-- 中科院分区设置 -->
                <div class="settings-group">
                    <label>🔬 中科院分区</label>
                    <div class="checkbox-group">
                        <div class="checkbox-item">
                            <input type="checkbox" id="cas1" value="1" checked>
                            <label for="cas1">1区</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="cas2" value="2" checked>
                            <label for="cas2">2区</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="cas3" value="3" checked>
                            <label for="cas3">3区</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="cas4" value="4" checked>
                            <label for="cas4">4区</label>
                        </div>
                    </div>
                </div>

                <!-- 文献类型设置 -->
                <div class="settings-group">
                    <label>📄 文献类型</label>
                    <div class="checkbox-group">
                        <div class="checkbox-item">
                            <input type="checkbox" id="typeArticle" value="Journal Article" checked>
                            <label for="typeArticle">期刊文章</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="typeReview" value="Review" checked>
                            <label for="typeReview">综述</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="typeMeta" value="Meta-Analysis" checked>
                            <label for="typeMeta">Meta分析</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="typeCase" value="Case Reports">
                            <label for="typeCase">病例报告</label>
                        </div>
                    </div>
                </div>

                <!-- 发表年份设置 -->
                <div class="settings-group">
                    <label>📅 发表年份</label>
                    <div class="settings-row">
                        <div>
                            <input type="number" id="yearStart" placeholder="开始年份" min="1900" max="2025" value="2020">
                        </div>
                        <div>
                            <input type="number" id="yearEnd" placeholder="结束年份" min="1900" max="2025" value="2025">
                        </div>
                    </div>
                </div>

                <!-- 文献数量限制 -->
                <div class="settings-group">
                    <label>📈 最大文献数量</label>
                    <select id="papersLimit">
                        <option value="100">100篇</option>
                        <option value="200">200篇</option>
                        <option value="500" selected>500篇</option>
                        <option value="1000">1000篇</option>
                        <option value="2000">2000篇</option>
                    </select>
                </div>

                <!-- 语言设置 -->
                <div class="settings-group">
                    <label>🌐 文献语言</label>
                    <div class="checkbox-group">
                        <div class="checkbox-item">
                            <input type="checkbox" id="langEn" value="eng" checked>
                            <label for="langEn">英文</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="langCh" value="chi">
                            <label for="langCh">中文</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="langOther" value="other">
                            <label for="langOther">其他语言</label>
                        </div>
                    </div>
                </div>
            </form>

            <div class="settings-actions">
                <button type="button" class="btn btn-secondary" onclick="resetSettings()">重置默认</button>
                <button type="button" class="btn btn-warning" onclick="clearChatHistory()" title="清除所有聊天记录以释放存储空间">清理记录</button>
                <button type="button" class="btn btn-primary" onclick="saveSettings()">保存设置</button>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentSessionId = null;
        let isFirstMessage = true;
        let backendSessionId = 'session_' + Math.random().toString(36).substr(2, 9); // 后端会话ID
        let socket = null; // Socket.IO连接
        let currentPapers = []; // 存储当前检索到的文献数据

        // 历史会话管理
        class ChatHistory {
            constructor() {
                this.sessions = this.loadSessions();
                this.currentSessionId = null;
                this.currentMessages = [];
            }

            loadSessions() {
                const saved = localStorage.getItem('nnscholar_chat_history');
                return saved ? JSON.parse(saved) : [];
            }

            saveSessions() {
                try {
                    // 尝试保存会话
                    localStorage.setItem('nnscholar_chat_history', JSON.stringify(this.sessions));
                } catch (error) {
                    if (error.name === 'QuotaExceededError') {
                        console.warn('localStorage配额超出，开始清理旧数据...');
                        this.cleanupOldSessions();
                        try {
                            // 清理后再次尝试保存
                            localStorage.setItem('nnscholar_chat_history', JSON.stringify(this.sessions));
                        } catch (secondError) {
                            console.error('清理后仍无法保存，禁用聊天记录功能');
                            // 如果还是失败，就不保存聊天记录
                        }
                    } else {
                        console.error('保存聊天记录失败:', error);
                    }
                }
            }

            cleanupOldSessions() {
                // 只保留最近的10个会话
                const maxSessions = 10;
                if (this.sessions.length > maxSessions) {
                    // 按时间排序，保留最新的会话
                    this.sessions.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
                    this.sessions = this.sessions.slice(0, maxSessions);
                }

                // 限制每个会话的消息数量
                const maxMessagesPerSession = 50;
                this.sessions.forEach(session => {
                    if (session.messages && session.messages.length > maxMessagesPerSession) {
                        // 保留最新的消息
                        session.messages = session.messages.slice(-maxMessagesPerSession);
                    }
                });

                console.log(`清理完成，当前会话数: ${this.sessions.length}`);
            }

            createSession(firstMessage) {
                const sessionId = Date.now().toString();
                const session = {
                    id: sessionId,
                    title: this.generateTitle(firstMessage),
                    messages: [firstMessage],
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString()
                };

                this.sessions.unshift(session);
                this.currentSessionId = sessionId;
                this.currentMessages = [firstMessage];
                this.saveSessions();
                this.updateHistoryPanel();

                return sessionId;
            }

            addMessage(message) {
                if (!this.currentSessionId) {
                    this.createSession(message);
                    return;
                }

                this.currentMessages.push(message);

                const sessionIndex = this.sessions.findIndex(s => s.id === this.currentSessionId);
                if (sessionIndex !== -1) {
                    this.sessions[sessionIndex].messages = [...this.currentMessages];
                    this.sessions[sessionIndex].updatedAt = new Date().toISOString();
                    this.saveSessions();
                    this.updateHistoryPanel();
                }
            }

            generateTitle(firstMessage) {
                if (firstMessage.type === 'user') {
                    const text = firstMessage.content.substring(0, 30);
                    return text.length < firstMessage.content.length ? text + '...' : text;
                }
                return '新对话';
            }

            loadSession(sessionId) {
                const session = this.sessions.find(s => s.id === sessionId);
                if (session) {
                    this.currentSessionId = sessionId;
                    this.currentMessages = [...session.messages];
                    this.restoreMessages(session.messages);
                    return true;
                }
                return false;
            }

            restoreMessages(messages) {
                const messagesContainer = document.getElementById('chatMessages');
                messagesContainer.innerHTML = '';

                messages.forEach(message => {
                    addMessage(message.content, message.type, false);
                });

                // 确保聊天界面是显示的
                document.getElementById('welcomeScreen').classList.add('hidden');
                document.getElementById('chatContainer').classList.add('active');
            }

            deleteSession(sessionId) {
                this.sessions = this.sessions.filter(s => s.id !== sessionId);
                if (this.currentSessionId === sessionId) {
                    this.currentSessionId = null;
                    this.currentMessages = [];
                }
                this.saveSessions();
                this.updateHistoryPanel();
            }

            clearAll() {
                this.sessions = [];
                this.currentSessionId = null;
                this.currentMessages = [];
                this.saveSessions();
                this.updateHistoryPanel();
            }

            searchSessions(query) {
                if (!query.trim()) {
                    return this.sessions;
                }

                return this.sessions.filter(session =>
                    session.title.toLowerCase().includes(query.toLowerCase()) ||
                    session.messages.some(msg =>
                        msg.content.toLowerCase().includes(query.toLowerCase())
                    )
                );
            }

            updateHistoryPanel() {
                const historyList = document.getElementById('historyList');

                if (this.sessions.length === 0) {
                    historyList.innerHTML = `
                        <div style="text-align: center; padding: 2rem 1rem; color: #94a3b8;">
                            <p style="margin-bottom: 0.5rem;">暂无历史会话</p>
                            <small style="font-size: 0.75rem;">开始一次对话后，会话记录将显示在这里</small>
                        </div>
                    `;
                    return;
                }

                const sessionsToShow = this.searchSessions(document.getElementById('historySearch')?.value || '');

                historyList.innerHTML = sessionsToShow.map(session => `
                    <div class="history-item ${session.id === this.currentSessionId ? 'active' : ''}" onclick="loadHistorySession('${session.id}')">
                        <div class="history-item-title">${session.title}</div>
                        <div class="history-item-time">${this.formatDate(session.updatedAt)}</div>
                    </div>
                `).join('');
            }

            formatDate(dateString) {
                const date = new Date(dateString);
                const now = new Date();
                const diffMs = now - date;
                const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

                if (diffDays === 0) {
                    return '今天 ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
                } else if (diffDays === 1) {
                    return '昨天';
                } else if (diffDays < 7) {
                    return `${diffDays}天前`;
                } else {
                    return date.toLocaleDateString('zh-CN');
                }
            }
        }

        // 初始化历史会话管理器
        const chatHistory = new ChatHistory();

        // 基础功能
        function handleWelcomeEnter(event) {
            if (event.key === 'Enter') {
                startFirstSearch();
            }
        }

        function handleEnter(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }

        async function startFirstSearch() {
            const input = document.getElementById('welcomeInput');
            const query = input.value.trim();

            if (!query) {
                return;
            }

            // 隐藏欢迎界面，显示聊天界面
            document.getElementById('welcomeScreen').classList.add('hidden');
            document.getElementById('chatContainer').classList.add('active');

            // 添加用户消息
            addMessage(query, 'user');

            // 清空输入框
            input.value = '';

            // 调用后端API进行文献检索
            await performLiteratureSearch(query);
        }

        async function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();

            if (!message) {
                return;
            }

            // 添加用户消息
            addMessage(message, 'user');

            // 清空输入框
            input.value = '';

            // 调用后端API处理消息
            await performLiteratureSearch(message);
        }

        function addMessage(content, type, saveToHistory = true) {
            const messagesContainer = document.getElementById('chatMessages');

            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;

            const avatar = document.createElement('div');
            avatar.className = 'message-avatar';
            avatar.textContent = type === 'user' ? 'U' : 'N';

            const messageContent = document.createElement('div');
            messageContent.className = 'message-content';

            // 安全检查：确保content是字符串
            if (typeof content !== 'string') {
                console.warn('addMessage received non-string content:', content);
                content = content ? String(content) : '内容为空';
            }

            // 检查内容是否包含HTML标签，如果包含则使用innerHTML，否则使用textContent
            if (content.includes('<') && content.includes('>')) {
                messageContent.innerHTML = content;
            } else {
                messageContent.textContent = content;
            }

            messageDiv.appendChild(avatar);
            messageDiv.appendChild(messageContent);

            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;

            // 保存到历史记录
            if (saveToHistory) {
                const message = {
                    type,
                    content,
                    timestamp: new Date().toISOString()
                };
                chatHistory.addMessage(message);
            }
        }

        function startNewSession() {
            // 重置会话状态
            chatHistory.currentSessionId = null;
            chatHistory.currentMessages = [];

            // 生成新的后端会话ID
            backendSessionId = 'session_' + Math.random().toString(36).substr(2, 9);

            // 重新初始化Socket连接
            if (socket) {
                socket.disconnect();
            }
            initializeSocket();

            // 清空聊天消息
            document.getElementById('chatMessages').innerHTML = '';

            // 显示欢迎界面，隐藏聊天界面
            document.getElementById('welcomeScreen').classList.remove('hidden');
            document.getElementById('chatContainer').classList.remove('active');

            // 清空输入框
            document.getElementById('welcomeInput').value = '';
            document.getElementById('messageInput').value = '';

            // 更新历史面板
            chatHistory.updateHistoryPanel();
        }

        function loadHistorySession(sessionId) {
            chatHistory.loadSession(sessionId);
        }

        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.toggle('collapsed');
        }

        function searchHistory() {
            chatHistory.updateHistoryPanel();
        }

        function clearAllHistory() {
            if (confirm('确定要清空所有历史会话吗？此操作不可恢复。')) {
                chatHistory.clearAll();
                // 如果当前在聊天界面，回到欢迎界面
                startNewSession();
            }
        }

        function toggleAnalysisSection() {
            const section = document.getElementById('analysisSection');
            if (section.style.display === 'none') {
                section.style.display = 'block';
                loadAnalysisCards();
            } else {
                section.style.display = 'none';
            }
        }

        function loadAnalysisCards() {
            const grid = document.querySelector('.analysis-grid');
            grid.innerHTML = `
                <div class="analysis-card" onclick="openAnalysisFeature('journal-selection')">
                    <div style="font-size: 1.5rem; margin-bottom: 0.25rem;">📊</div>
                    <div style="font-weight: 600; margin-bottom: 0.25rem; font-size: 0.875rem;">AI投稿选刊</div>
                    <div style="font-size: 0.7rem; color: #6b7280;">基于研究内容智能推荐合适的期刊</div>
                </div>
                <div class="analysis-card" onclick="openAnalysisFeature('reference-matching')">
                    <div style="font-size: 1.5rem; margin-bottom: 0.25rem;">📚</div>
                    <div style="font-weight: 600; margin-bottom: 0.25rem; font-size: 0.875rem;">参考文献匹配</div>
                    <div style="font-size: 0.7rem; color: #6b7280;">为内容匹配权威文献</div>
                </div>
                <div class="analysis-card" onclick="openAnalysisFeature('statistical-analysis')">
                    <div style="font-size: 1.5rem; margin-bottom: 0.25rem;">📈</div>
                    <div style="font-weight: 600; margin-bottom: 0.25rem; font-size: 0.875rem;">统计分析专家</div>
                    <div style="font-size: 0.7rem; color: #6b7280;">设计科学合理的统计分析方法</div>
                </div>
                <div class="analysis-card" onclick="openAnalysisFeature('visualization-expert')">
                    <div style="font-size: 1.5rem; margin-bottom: 0.25rem;">📊</div>
                    <div style="font-weight: 600; margin-bottom: 0.25rem; font-size: 0.875rem;">绘图建议专家</div>
                    <div style="font-size: 0.7rem; color: #6b7280;">选择合适的图表和可视化方法</div>
                </div>
            `;
        }

        // 文献检索核心函数
        async function performLiteratureSearch(query) {
            try {
                // 显示加载消息
                addMessage('🔍 正在为您生成检索策略...', 'assistant');

                // 第一步：生成检索策略
                const strategyResponse = await fetch('/api/generate_strategy', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'sid': backendSessionId
                    },
                    body: JSON.stringify({
                        query: query,
                        mode: 'single'
                    })
                });

                if (!strategyResponse.ok) {
                    throw new Error(`生成策略失败: ${strategyResponse.status}`);
                }

                const strategyData = await strategyResponse.json();
                console.log('生成的检索策略:', strategyData);

                if (!strategyData.success) {
                    throw new Error(strategyData.error || '生成检索策略失败');
                }

                // 显示策略生成完成
                addMessage('✅ 检索策略生成完成，正在执行检索...', 'assistant');

                // 转换设置格式为后端期望的格式
                const filters = {
                    impact_factor_min: currentSettings.impactFactorMin ? parseFloat(currentSettings.impactFactorMin) : null,
                    impact_factor_max: currentSettings.impactFactorMax ? parseFloat(currentSettings.impactFactorMax) : null,
                    jcr_quartiles: currentSettings.jcrQuartiles || ['Q1', 'Q2', 'Q3', 'Q4'],
                    cas_zones: currentSettings.casZones || ['1', '2', '3', '4'],
                    publication_types: currentSettings.publicationTypes || ['Journal Article', 'Review', 'Meta-Analysis'],
                    year_start: currentSettings.yearStart || 2010,
                    year_end: currentSettings.yearEnd || 2025,
                    papers_limit: currentSettings.papersLimit || 500,  // 修正参数名
                    languages: currentSettings.languages || ['eng']
                };

                // 第二步：执行检索策略
                const searchResponse = await fetch('/api/search', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'sid': backendSessionId
                    },
                    body: JSON.stringify({
                        query: strategyData.search_strategy,
                        mode: 'strategy',  // 使用strategy模式
                        filters: filters
                    })
                });

                if (!searchResponse.ok) {
                    throw new Error(`HTTP error! status: ${searchResponse.status}`);
                }

                const searchData = await searchResponse.json();
                console.log('收到搜索响应数据:', searchData);

                if (searchData.success) {
                    // 显示检索结果
                    displaySearchResults(searchData);
                } else {
                    addMessage(`❌ 检索失败: ${searchData.error || '未知错误'}`, 'assistant');
                }

            } catch (error) {
                console.error('检索错误:', error);
                addMessage(`❌ 检索过程中出现错误: ${error.message}`, 'assistant');
            }
        }

        // 显示检索结果 - 修改为先显示完整文献列表
        function displaySearchResults(data) {
            const papers = data.data || data.papers || [];  // 兼容不同的数据结构
            const total_count = data.total_count || 0;
            const filtered_count = data.filtered_count || papers.length;
            const query = data.search_strategy || '检索查询';

            // 存储当前检索到的文献数据到全局变量
            currentPapers = papers;
            console.log('存储当前文献数据:', currentPapers.length, '篇');

            if (!papers || papers.length === 0) {
                addMessage('📚 未找到相关文献，请尝试调整搜索关键词或筛选条件。', 'assistant');
                return;
            }

            // 1. 首先显示完整文献列表（默认展开）
            displayFullLiteratureListFirst(papers, query, filtered_count);

            // 2. 延迟显示推荐内容
            setTimeout(() => {
                let coreRecommendationHtml = `
                    <div class="core-recommendation-section">
                        <div class="recommendation-header">
                            <h3><i class="fas fa-star text-warning me-2"></i>NNScholar科研助手为您推荐</h3>
                            <p class="text-muted">基于学术价值和相关度为您精选最重要的研究内容</p>
                        </div>
                    </div>
                `;
                addMessage(coreRecommendationHtml, 'assistant');

                // 立即执行代表性文章推荐
                handleSmartQuestionWithCallback('representative', query, backendSessionId, () => {
                    // 代表性文章推荐完成后不再显示其他推荐选项
                    console.log('代表性文章推荐完成');
                });
            }, 1000);
        }

        // 首次显示完整文献列表（默认展开）
        function displayFullLiteratureListFirst(papers, query, filtered_count) {
            let fullListHtml = `
                <div class="full-literature-section">
                    <div class="literature-header" onclick="toggleFullLiteratureListFirst()">
                        <h5>
                            <i class="fas fa-list text-secondary me-2"></i>
                            查看全部 ${filtered_count} 篇文献
                            <i class="fas fa-chevron-up toggle-icon" id="literatureToggleIconFirst"></i>
                        </h5>
                        <p class="text-muted mb-0">点击展开完整文献列表</p>
                    </div>
                    <div class="literature-list-container" id="fullLiteratureListFirst" style="display: block;">
                        <div class="papers-list">
            `;

            papers.forEach((paper, index) => {
                const journalInfo = paper.journal_info || {};
                fullListHtml += `
                    <div class="card mb-3">
                        <div class="card-body">
                            <h5 class="card-title">
                                <a href="${paper.url || '#'}" target="_blank" class="text-decoration-none">
                                    ${paper.title || '无标题'}
                                </a>
                            </h5>
                            <div class="d-flex flex-wrap gap-3 mb-2">
                                <span class="text-muted">
                                    <i class="fas fa-users me-1"></i>
                                    ${paper.authors ? (Array.isArray(paper.authors) ? paper.authors.join(', ') : paper.authors) : 'N/A'}
                                </span>
                                <span class="text-muted">
                                    <i class="fas fa-book me-1"></i>
                                    ${journalInfo.title || paper.journal || 'N/A'}
                                </span>
                                <span class="text-muted">
                                    <i class="fas fa-calendar me-1"></i>
                                    ${paper.pub_year || paper.year || 'N/A'}
                                </span>
                            </div>
                            <div class="d-flex flex-wrap gap-3 mb-3">
                                <span class="badge bg-primary">
                                    IF: ${journalInfo.impact_factor || paper.impact_factor || 'N/A'}
                                </span>
                                <span class="badge bg-info">
                                    JCR: ${journalInfo.jcr_quartile || paper.jcr_quartile || 'N/A'}
                                </span>
                                <span class="badge bg-success">
                                    CAS: ${journalInfo.cas_quartile || paper.cas_quartile || 'N/A'}
                                </span>
                                <span class="badge bg-warning text-dark">
                                    相关度: ${paper.relevance_score ? Math.round(paper.relevance_score * 100) : 'N/A'}%
                                </span>
                            </div>
                            <p class="card-text text-muted">
                                ${paper.abstract || 'No abstract available'}
                            </p>
                        </div>
                    </div>
                `;
            });

            fullListHtml += `
                        </div>
                        <div class="literature-actions mt-3" id="literatureActionsFirst">
                            <button class="action-btn" data-action="preciseSearch" data-session="${backendSessionId}">
                                🔍 进一步筛查文献
                            </button>
                            <button class="action-btn" data-action="exportExcel" data-session="${backendSessionId}">
                                📊 导出表格
                            </button>
                            <button class="action-btn" data-action="exportWord" data-session="${backendSessionId}">
                                📄 导出文档
                            </button>
                        </div>
                    </div>
                </div>
            `;

            addMessage(fullListHtml, 'assistant');
        }

        // 显示完整文献列表（重用原有的文献显示逻辑）
        function displayFullLiteratureList(papers, query, filtered_count) {
            let fullListHtml = `
                <div class="full-literature-section">
                    <div class="literature-header" onclick="toggleFullLiteratureList()">
                        <h5>
                            <i class="fas fa-list text-secondary me-2"></i>
                            查看全部 ${filtered_count} 篇文献
                            <i class="fas fa-chevron-down toggle-icon" id="literatureToggleIcon"></i>
                        </h5>
                        <p class="text-muted mb-0">点击展开完整文献列表</p>
                    </div>
                    <div class="literature-list-container" id="fullLiteratureList" style="display: none;">
                        <div class="papers-list">
            `;

            papers.forEach((paper, index) => {
                const journalInfo = paper.journal_info || {};
                fullListHtml += `
                    <div class="card mb-3">
                        <div class="card-body">
                            <h5 class="card-title">
                                <a href="${paper.url || '#'}" target="_blank" class="text-decoration-none">
                                    ${paper.title || '无标题'}
                                </a>
                            </h5>
                            <div class="d-flex flex-wrap gap-3 mb-2">
                                <span class="text-muted">
                                    <i class="fas fa-users me-1"></i>
                                    ${paper.authors ? (Array.isArray(paper.authors) ? paper.authors.join(', ') : paper.authors) : 'N/A'}
                                </span>
                                <span class="text-muted">
                                    <i class="fas fa-book me-1"></i>
                                    ${journalInfo.title || paper.journal || 'N/A'}
                                </span>
                                <span class="text-muted">
                                    <i class="fas fa-calendar me-1"></i>
                                    ${paper.pub_year || paper.year || 'N/A'}
                                </span>
                            </div>
                            <div class="d-flex flex-wrap gap-3 mb-3">
                                <span class="badge bg-primary">
                                    IF: ${journalInfo.impact_factor || paper.impact_factor || 'N/A'}
                                </span>
                                <span class="badge bg-info">
                                    JCR: ${journalInfo.jcr_quartile || paper.jcr_quartile || 'N/A'}
                                </span>
                                <span class="badge bg-success">
                                    CAS: ${journalInfo.cas_quartile || paper.cas_quartile || 'N/A'}
                                </span>
                                <span class="badge bg-warning text-dark">
                                    相关度: ${paper.relevance_score ? Math.round(paper.relevance_score * 100) : 'N/A'}%
                                </span>
                            </div>
                            <p class="card-text text-muted">
                                ${paper.abstract || 'No abstract available'}
                            </p>
                        </div>
                    </div>
                `;
            });

            fullListHtml += `
                        </div>
                        <div class="literature-actions mt-3" style="display: none;" id="literatureActions">
                            <button class="action-btn" data-action="preciseSearch" data-session="${backendSessionId}">
                                🔍 进一步筛查文献
                            </button>
                            <button class="action-btn" data-action="exportExcel" data-session="${backendSessionId}">
                                📊 导出表格
                            </button>
                            <button class="action-btn" data-action="exportWord" data-session="${backendSessionId}">
                                📄 导出文档
                            </button>
                        </div>
                    </div>
                </div>
            `;

            addMessage(fullListHtml, 'assistant');
        }

        // 切换首次显示的完整文献列表
        function toggleFullLiteratureListFirst() {
            const listContainer = document.getElementById('fullLiteratureListFirst');
            const actionsContainer = document.getElementById('literatureActionsFirst');
            const toggleIcon = document.getElementById('literatureToggleIconFirst');

            if (listContainer && listContainer.style.display === 'none') {
                listContainer.style.display = 'block';
                if (actionsContainer) actionsContainer.style.display = 'block';
                if (toggleIcon) toggleIcon.className = 'fas fa-chevron-up toggle-icon';
            } else if (listContainer) {
                listContainer.style.display = 'none';
                if (actionsContainer) actionsContainer.style.display = 'none';
                if (toggleIcon) toggleIcon.className = 'fas fa-chevron-down toggle-icon';
            }
        }

        // 切换完整文献列表显示
        function toggleFullLiteratureList() {
            const listContainer = document.getElementById('fullLiteratureList');
            const actionsContainer = document.getElementById('literatureActions');
            const toggleIcon = document.getElementById('literatureToggleIcon');

            if (listContainer && listContainer.style.display === 'none') {
                listContainer.style.display = 'block';
                if (actionsContainer) actionsContainer.style.display = 'block';
                if (toggleIcon) toggleIcon.className = 'fas fa-chevron-up toggle-icon';
            } else if (listContainer) {
                listContainer.style.display = 'none';
                if (actionsContainer) actionsContainer.style.display = 'none';
                if (toggleIcon) toggleIcon.className = 'fas fa-chevron-down toggle-icon';
            }
        }

        // 分析研究现状
        async function analyzeResearchStatus(query, sessionId) {
            try {
                addMessage('📊 正在分析研究现状，请稍候...', 'assistant');

                // 使用传入的会话ID，如果没有则使用当前的
                const useSessionId = sessionId || backendSessionId;
                console.log(`分析研究现状 - 使用会话ID: ${useSessionId}`);

                const response = await fetch('/api/analyze_research_status', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'sid': useSessionId
                    },
                    body: JSON.stringify({ query: query, papers: currentPapers })
                });

                const data = await response.json();

                if (data.success) {
                    addMessage(data.analysis, 'assistant');
                } else {
                    addMessage(`❌ 分析失败: ${data.error}`, 'assistant');
                }
            } catch (error) {
                addMessage(`❌ 分析过程中出现错误: ${error.message}`, 'assistant');
            }
        }

        // 精确查找文献功能
        async function preciseSearch(sessionId) {
            try {
                // 弹出输入框让用户输入进一步要求
                const userInput = prompt('请输入您的精确查找要求（例如：和降糖药物相关研究）：');

                if (!userInput || userInput.trim() === '') {
                    addMessage('❌ 请输入有效的查找要求', 'assistant');
                    return;
                }

                addMessage(`🔍 正在从已检索文献中精确查找"${userInput}"相关的文献，请稍候...`, 'assistant');

                // 使用传入的会话ID，如果没有则使用当前的
                const useSessionId = sessionId || backendSessionId;
                console.log(`精确查找文献 - 使用会话ID: ${useSessionId}, 查找要求: ${userInput}`);

                const response = await fetch('/api/precise_literature_search', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'sid': useSessionId
                    },
                    body: JSON.stringify({
                        search_requirement: userInput,
                        max_papers: 100,  // 限制最多100篇文献
                        papers: currentPapers
                    })
                });

                const data = await response.json();

                if (data.success) {
                    // 特殊处理精确查找结果
                    if (data.structured_papers && data.structured_papers.length > 0) {
                        displayPreciseSearchResults(data.structured_papers, userInput, data.analysis);
                    } else {
                        addMessage(data.analysis || '未找到符合要求的文献', 'assistant');
                    }
                } else {
                    addMessage(`❌ 精确查找失败: ${data.error}`, 'assistant');
                }
            } catch (error) {
                addMessage(`❌ 精确查找过程中出现错误: ${error.message}`, 'assistant');
            }
        }

        // 显示精确查找结果
        function displayPreciseSearchResults(papers, searchRequirement, originalText) {
            if (!papers || papers.length === 0) {
                addMessage(originalText || '未找到符合要求的文献', 'assistant');
                return;
            }

            let resultHtml = `
                <div class="search-results">
                    <div class="precise-search-section">
                        <h4><i class="fas fa-search text-primary me-2"></i>精确查找结果</h4>
                        <p class="text-muted mb-4">基于"${searchRequirement}"筛选出 <strong>${papers.length}</strong> 篇高度相关文献</p>
                    </div>
                    <div class="papers-list">
            `;

            papers.forEach((paper, index) => {
                const journalInfo = paper.journal_info || {};

                // 处理标题显示（中英文对照）
                let titleDisplay = paper.title || '无标题';
                if (paper.chinese_title && paper.chinese_title !== paper.title) {
                    titleDisplay = `
                        <div class="title-bilingual">
                            <div class="title-english">${paper.title}</div>
                            <div class="title-chinese">${paper.chinese_title}</div>
                        </div>
                    `;
                }

                resultHtml += `
                    <div class="card mb-4 precise-search-paper-card">
                        <div class="card-body">
                            <div class="paper-header">
                                <span class="paper-rank">#${index + 1}</span>
                                <h5 class="card-title">
                                    <a href="${paper.url || '#'}" target="_blank" class="text-decoration-none">
                                        ${titleDisplay}
                                    </a>
                                </h5>
                            </div>

                            <div class="paper-meta mb-3">
                                <div class="meta-row">
                                    <span class="meta-item">
                                        <i class="fas fa-users me-1"></i>
                                        <strong>作者：</strong>${paper.authors ? (Array.isArray(paper.authors) ? paper.authors.join(', ') : paper.authors) : 'N/A'}
                                    </span>
                                </div>
                                <div class="meta-row">
                                    <span class="meta-item">
                                        <i class="fas fa-book me-1"></i>
                                        <strong>期刊：</strong>${journalInfo.title || paper.journal || 'N/A'}
                                    </span>
                                    <span class="meta-item">
                                        <i class="fas fa-calendar me-1"></i>
                                        <strong>年份：</strong>${paper.pub_year || paper.year || 'N/A'}
                                    </span>
                                </div>
                            </div>

                            <div class="paper-badges mb-3">
                                <span class="badge bg-primary">
                                    IF: ${journalInfo.impact_factor || paper.impact_factor || 'N/A'}
                                </span>
                                <span class="badge bg-info">
                                    JCR: ${journalInfo.jcr_quartile || paper.jcr_quartile || 'N/A'}
                                </span>
                                <span class="badge bg-success">
                                    CAS: ${journalInfo.cas_quartile || paper.cas_quartile || 'N/A'}
                                </span>
                                <span class="badge bg-warning text-dark">
                                    相关度: ${paper.relevance_score ? Math.round(paper.relevance_score * 100) : 'N/A'}%
                                </span>
                                ${paper.sufficiency_score ? `
                                    <span class="badge bg-danger">
                                        符合充分性: ${paper.sufficiency_score}%
                                    </span>
                                ` : ''}
                            </div>

                            <div class="paper-summary mb-3">
                                <h6><i class="fas fa-file-text me-1"></i>中文摘要总结</h6>
                                <p class="summary-text">${paper.abstract || '暂无摘要总结'}</p>
                            </div>

                            ${paper.selection_reason ? `
                                <div class="selection-reason">
                                    <h6><i class="fas fa-check-circle text-success me-1"></i>符合筛选的理由</h6>
                                    <div class="reason-content">
                                        ${paper.selection_reason}
                                    </div>
                                </div>
                            ` : ''}
                        </div>
                    </div>
                `;
            });

            resultHtml += `
                    </div>
                    <div class="alert alert-info mt-3">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>筛选说明：</strong>以上文献基于与"${searchRequirement}"的相关度从已检索的全部文献中精确筛选而出。
                    </div>
                </div>
            `;

            addMessage(resultHtml, 'assistant');
        }

        // 生成综述选题建议
        async function generateReviewTopicSuggestions(query, sessionId) {
            try {
                addMessage('📝 正在生成综述选题建议，请稍候...', 'assistant');

                // 使用传入的会话ID，如果没有则使用当前的
                const useSessionId = sessionId || backendSessionId;
                console.log(`综述选题建议 - 使用会话ID: ${useSessionId}`);

                const response = await fetch('/api/review_topic_suggestion', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'sid': useSessionId
                    },
                    body: JSON.stringify({ query: query, papers: currentPapers })
                });

                const data = await response.json();

                if (data.success) {
                    addMessage(data.suggestions, 'assistant');
                } else {
                    addMessage(`❌ 生成失败: ${data.error}`, 'assistant');
                }
            } catch (error) {
                addMessage(`❌ 生成过程中出现错误: ${error.message}`, 'assistant');
            }
        }

        // 生成论著选题建议
        async function generateResearchTopicSuggestions(query, sessionId) {
            try {
                addMessage('🔬 正在生成论著选题建议，请稍候...', 'assistant');

                // 使用传入的会话ID，如果没有则使用当前的
                const useSessionId = sessionId || backendSessionId;
                console.log(`论著选题建议 - 使用会话ID: ${useSessionId}`);

                const response = await fetch('/api/research_topic_suggestion', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'sid': useSessionId
                    },
                    body: JSON.stringify({ query: query, papers: currentPapers })
                });

                const data = await response.json();

                if (data.success) {
                    addMessage(data.suggestions, 'assistant');
                } else {
                    addMessage(`❌ 生成失败: ${data.error}`, 'assistant');
                }
            } catch (error) {
                addMessage(`❌ 生成过程中出现错误: ${error.message}`, 'assistant');
            }
        }

        // 生成完整综述
        async function generateCompleteReview(query, sessionId) {
            try {
                // 显示语言选择对话框
                const language = await showLanguageSelectionDialog();
                if (!language) return; // 用户取消了选择

                // 使用传入的会话ID，如果没有则使用当前的
                const useSessionId = sessionId || backendSessionId;
                console.log(`生成完整综述 - 使用会话ID: ${useSessionId}, 语言: ${language}`);

                // 添加进度消息容器
                const progressMessageId = addProgressMessage('📚 正在准备生成综述...', 'assistant');

                // 使用WebSocket进行实时进度更新
                if (socket && socket.connected) {
                    // 设置进度监听器
                    socket.off('review_started');
                    socket.off('review_progress');
                    socket.off('review_completed');
                    socket.off('review_error');

                    socket.on('review_started', (data) => {
                        updateProgressMessage(progressMessageId, `🚀 开始生成综述 (${data.paper_count}篇文献, ${data.language === 'english' ? '英文' : '中文'})`);
                    });

                    socket.on('review_progress', (data) => {
                        // 如果消息包含换行符，说明是大纲等结构化内容，需要特殊处理
                        if (data.message.includes('\n')) {
                            const formattedMessage = data.message.replace(/\n/g, '<br>').replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
                            updateProgressMessage(progressMessageId, `⏳ ${formattedMessage} (${data.progress}%)`);
                        } else {
                            updateProgressMessage(progressMessageId, `⏳ ${data.message} (${data.progress}%)`);
                        }
                    });

                    socket.on('review_completed', (data) => {
                        removeProgressMessage(progressMessageId);

                        // 显示完成的综述
                        const reviewHtml = `
                            <div class="review-result">
                                <h2>${data.title}</h2>
                                <div class="review-meta">
                                    <span>📊 基于 ${data.paper_count} 篇文献</span>
                                    <span>📝 ${data.word_count} 字</span>
                                    <span>📚 ${data.reference_count} 个参考文献</span>
                                    <span>⏰ ${new Date(data.generated_at).toLocaleString()}</span>
                                </div>
                                <div class="review-content">
                                    ${data.content}
                                </div>
                                <div class="review-actions">
                                    <button onclick="exportReviewAsWord('${data.title}', \`${data.content.replace(/`/g, '\\`')}\`)" class="export-btn">
                                        📄 导出Word文档
                                    </button>
                                </div>
                            </div>
                        `;
                        addMessage(reviewHtml, 'assistant');
                    });

                    socket.on('review_error', (data) => {
                        removeProgressMessage(progressMessageId);
                        addMessage(`❌ 综述生成失败: ${data.error}`, 'assistant');
                    });

                    // 发送生成请求
                    socket.emit('generate_full_review_with_progress', {
                        query: query,
                        papers: currentPapers,
                        language: language,
                        session_id: useSessionId
                    });

                } else {
                    // 回退到HTTP API
                    const response = await fetch('/api/generate_full_review', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'sid': useSessionId
                        },
                        body: JSON.stringify({
                            query: query,
                            papers: currentPapers,
                            language: language
                        })
                    });

                    const data = await response.json();
                    removeProgressMessage(progressMessageId);

                    if (data.success) {
                        addMessage(data.review, 'assistant');
                    } else {
                        addMessage(`❌ 生成失败: ${data.error}`, 'assistant');
                    }
                }
            } catch (error) {
                addMessage(`❌ 生成过程中出现错误: ${error.message}`, 'assistant');
            }
        }

        // 显示语言选择对话框
        function showLanguageSelectionDialog() {
            return new Promise((resolve) => {
                const modal = document.createElement('div');
                modal.className = 'language-selection-modal';
                modal.innerHTML = `
                    <div class="modal-overlay">
                        <div class="modal-content">
                            <h3>选择综述语言</h3>
                            <p>请选择您希望生成的综述语言：</p>
                            <div class="language-options">
                                <button class="language-btn" data-lang="chinese">
                                    🇨🇳 中文综述
                                </button>
                                <button class="language-btn" data-lang="english">
                                    🇺🇸 English Review
                                </button>
                            </div>
                            <div class="modal-actions">
                                <button class="cancel-btn">取消</button>
                            </div>
                        </div>
                    </div>
                `;

                document.body.appendChild(modal);

                // 添加样式
                const style = document.createElement('style');
                style.textContent = `
                    .language-selection-modal {
                        position: fixed;
                        top: 0;
                        left: 0;
                        width: 100%;
                        height: 100%;
                        z-index: 10000;
                    }
                    .modal-overlay {
                        width: 100%;
                        height: 100%;
                        background: rgba(0, 0, 0, 0.5);
                        display: flex;
                        align-items: center;
                        justify-content: center;
                    }
                    .modal-content {
                        background: white;
                        padding: 30px;
                        border-radius: 12px;
                        max-width: 400px;
                        width: 90%;
                        text-align: center;
                        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
                    }
                    .language-options {
                        margin: 20px 0;
                        display: flex;
                        flex-direction: column;
                        gap: 12px;
                    }
                    .language-btn {
                        padding: 15px 20px;
                        border: 2px solid #e0e0e0;
                        border-radius: 8px;
                        background: white;
                        cursor: pointer;
                        font-size: 16px;
                        transition: all 0.3s ease;
                    }
                    .language-btn:hover {
                        border-color: #6366f1;
                        background: #f8faff;
                        transform: translateY(-2px);
                    }
                    .modal-actions {
                        margin-top: 20px;
                    }
                    .cancel-btn {
                        padding: 8px 16px;
                        border: 1px solid #ccc;
                        border-radius: 6px;
                        background: white;
                        cursor: pointer;
                    }
                `;
                document.head.appendChild(style);

                // 事件处理
                modal.addEventListener('click', (e) => {
                    if (e.target.classList.contains('language-btn')) {
                        const language = e.target.dataset.lang;
                        document.body.removeChild(modal);
                        document.head.removeChild(style);
                        resolve(language);
                    } else if (e.target.classList.contains('cancel-btn') || e.target.classList.contains('modal-overlay')) {
                        document.body.removeChild(modal);
                        document.head.removeChild(style);
                        resolve(null);
                    }
                });
            });
        }

        // 添加进度消息
        function addProgressMessage(message, sender) {
            const messageId = 'progress_' + Date.now();
            const messageElement = document.createElement('div');
            messageElement.className = `message ${sender}-message`;
            messageElement.id = messageId;
            messageElement.innerHTML = `
                <div class="message-content">
                    <div class="progress-message">
                        ${message}
                        <div class="progress-spinner"></div>
                    </div>
                </div>
            `;

            const messagesContainer = document.getElementById('messages');
            messagesContainer.appendChild(messageElement);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;

            return messageId;
        }

        // 更新进度消息
        function updateProgressMessage(messageId, newMessage) {
            const messageElement = document.getElementById(messageId);
            if (messageElement) {
                const progressDiv = messageElement.querySelector('.progress-message');
                if (progressDiv) {
                    progressDiv.innerHTML = `
                        ${newMessage}
                        <div class="progress-spinner"></div>
                    `;
                }
            }
        }

        // 移除进度消息
        function removeProgressMessage(messageId) {
            const messageElement = document.getElementById(messageId);
            if (messageElement) {
                messageElement.remove();
            }
        }

        // 导出综述为Word文档
        function exportReviewAsWord(title, content) {
            try {
                // 创建Word文档内容
                const wordContent = `
                    <html>
                    <head>
                        <meta charset="utf-8">
                        <title>${title}</title>
                        <style>
                            body { font-family: 'Times New Roman', serif; line-height: 1.6; margin: 40px; }
                            h1 { color: #333; border-bottom: 2px solid #333; padding-bottom: 10px; }
                            h2 { color: #555; margin-top: 30px; }
                            h3 { color: #777; }
                            p { margin: 10px 0; text-align: justify; }
                            ul, ol { margin: 10px 0 10px 20px; }
                            .reference { font-size: 14px; margin: 5px 0; }
                        </style>
                    </head>
                    <body>
                        ${content}
                    </body>
                    </html>
                `;

                // 创建Blob并下载
                const blob = new Blob([wordContent], { type: 'application/msword' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `${title}.doc`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);

                addMessage('📄 Word文档已开始下载', 'assistant');
            } catch (error) {
                addMessage(`❌ 导出失败: ${error.message}`, 'assistant');
            }
        }

        // 导出结果
        async function exportResults(format, sessionId) {
            try {
                // 检查是否有搜索结果
                if (!chatHistory.currentMessages || chatHistory.currentMessages.length === 0) {
                    addMessage('❌ 请先进行文献检索，然后再导出结果', 'assistant');
                    return;
                }

                // 使用传入的会话ID，如果没有则使用当前的
                const useSessionId = sessionId || backendSessionId;
                console.log(`开始导出${format}，使用会话ID: ${useSessionId}`);

                addMessage(`📄 正在准备${format.toUpperCase()}导出，请稍候...`, 'assistant');

                const response = await fetch(`/api/export/${format}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'sid': useSessionId
                    },
                    body: JSON.stringify({
                        query: chatHistory.currentMessages[0]?.content || '',
                        format: format
                    })
                });

                console.log(`导出${format}响应状态:`, response.status);
                console.log(`导出${format}响应头:`, Object.fromEntries(response.headers.entries()));

                if (response.ok) {
                    const blob = await response.blob();
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `literature_results.${format === 'excel' ? 'xlsx' : 'docx'}`;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    window.URL.revokeObjectURL(url);

                    addMessage(`✅ ${format.toUpperCase()}文件已下载完成！`, 'assistant');
                } else {
                    // 尝试获取详细错误信息
                    try {
                        const errorData = await response.json();
                        addMessage(`❌ 导出失败: ${errorData.error || '未知错误'}`, 'assistant');
                    } catch (e) {
                        addMessage(`❌ 导出失败 (HTTP ${response.status}): ${response.statusText}`, 'assistant');
                    }
                }
            } catch (error) {
                addMessage(`❌ 导出过程中出现错误: ${error.message}`, 'assistant');
            }
        }

        // 显示推荐文献的专用函数
        function displayRecommendedPapers(papers, originalText) {
            if (!papers || papers.length === 0) {
                // 如果没有结构化数据，显示原始文本
                addMessage(originalText, 'assistant');
                return;
            }

            let resultHtml = `
                <div class="search-results">
                    <div class="representative-papers-section">
                        <h4><i class="fas fa-medal text-warning me-2"></i>代表性文章推荐</h4>
                        <p class="text-muted mb-4">为您精选 <strong>${papers.length}</strong> 篇最具学术价值的代表性文献</p>
                    </div>
                    <div class="papers-list">
            `;

            papers.forEach((paper, index) => {
                const journalInfo = paper.journal_info || {};

                // 处理标题显示（中英文对照）
                let titleDisplay = paper.title || '无标题';
                if (paper.chinese_title && paper.chinese_title !== paper.title) {
                    titleDisplay = `
                        <div class="title-bilingual">
                            <div class="title-english">${paper.title}</div>
                            <div class="title-chinese">${paper.chinese_title}</div>
                        </div>
                    `;
                }

                resultHtml += `
                    <div class="card mb-4 recommended-paper-card">
                        <div class="card-body">
                            <div class="paper-header">
                                <span class="paper-rank">#${index + 1}</span>
                                <h5 class="card-title">
                                    <a href="${paper.url || '#'}" target="_blank" class="text-decoration-none">
                                        ${titleDisplay}
                                    </a>
                                </h5>
                            </div>

                            <div class="paper-meta mb-3">
                                <div class="meta-row">
                                    <span class="meta-item">
                                        <i class="fas fa-users me-1"></i>
                                        <strong>作者：</strong>${paper.authors ? (Array.isArray(paper.authors) ? paper.authors.join(', ') : paper.authors) : 'N/A'}
                                    </span>
                                </div>
                                <div class="meta-row">
                                    <span class="meta-item">
                                        <i class="fas fa-book me-1"></i>
                                        <strong>期刊：</strong>${journalInfo.title || paper.journal || 'N/A'}
                                    </span>
                                    <span class="meta-item">
                                        <i class="fas fa-calendar me-1"></i>
                                        <strong>年份：</strong>${paper.pub_year || paper.year || 'N/A'}
                                    </span>
                                </div>
                            </div>

                            <div class="paper-badges mb-3">
                                <span class="badge bg-primary">
                                    IF: ${journalInfo.impact_factor || paper.impact_factor || 'N/A'}
                                </span>
                                <span class="badge bg-info">
                                    JCR: ${journalInfo.jcr_quartile || paper.jcr_quartile || 'N/A'}
                                </span>
                                <span class="badge bg-success">
                                    CAS: ${journalInfo.cas_quartile || paper.cas_quartile || 'N/A'}
                                </span>
                                <span class="badge bg-warning text-dark">
                                    相关度: ${paper.relevance_score ? Math.round(paper.relevance_score * 100) : 'N/A'}%
                                </span>
                            </div>

                            <div class="paper-summary mb-3">
                                <h6><i class="fas fa-file-text me-1"></i>中文摘要总结</h6>
                                <p class="summary-text">${paper.abstract || '暂无摘要总结'}</p>
                            </div>

                            ${paper.recommendation_reason ? `
                                <div class="recommendation-reason">
                                    <h6><i class="fas fa-star text-warning me-1"></i>推荐理由</h6>
                                    <div class="reason-content">
                                        ${paper.recommendation_reason}
                                    </div>
                                </div>
                            ` : ''}
                        </div>
                    </div>
                `;
            });

            resultHtml += `
                    </div>
                    <div class="alert alert-info mt-3">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>筛选说明：</strong>以上文献基于影响因子(30%)、创新性(40%)和学术质量(30%)综合评估筛选。
                    </div>
                </div>
            `;

            addMessage(resultHtml, 'assistant');
        }

        // 带回调的智能问题处理函数
        async function handleSmartQuestionWithCallback(questionType, query, sessionId, callback) {
            try {
                const useSessionId = sessionId || backendSessionId;
                console.log(`处理智能问题: ${questionType}, 查询: ${query}, 会话ID: ${useSessionId}`);

                let message = '';
                let apiEndpoint = '';
                let requestData = { query: query, papers: currentPapers };

                switch(questionType) {
                    case 'representative':
                        message = '📚 正在为您推荐代表性文章...';
                        apiEndpoint = '/api/recommend_representative_papers';
                        break;
                    default:
                        addMessage('❌ 未知的问题类型', 'assistant');
                        return;
                }

                addMessage(message, 'assistant');

                const response = await fetch(apiEndpoint, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'sid': useSessionId
                    },
                    body: JSON.stringify(requestData)
                });

                const data = await response.json();
                console.log(`智能问题${questionType}的API响应(带回调):`, data);

                if (data.success) {
                    // 特殊处理推荐代表性文章
                    if (questionType === 'representative' && data.structured_papers) {
                        displayRecommendedPapers(data.structured_papers, data.recommendations);
                    } else {
                        const result = data.analysis || data.suggestions || data.suggestion || data.recommendations || data.result;
                        console.log(`提取到的结果字段(带回调):`, {
                            analysis: data.analysis ? `${data.analysis.length}字符` : '无',
                            suggestions: data.suggestions ? `${data.suggestions.length}字符` : '无',
                            suggestion: data.suggestion ? `${data.suggestion.length}字符` : '无',
                            recommendations: data.recommendations ? `${data.recommendations.length}字符` : '无',
                            result: data.result ? `${data.result.length}字符` : '无',
                            finalResult: result ? `${result.length}字符` : '无'
                        });
                        
                        if (result) {
                            addMessage(result, 'assistant');
                        } else {
                            console.warn('所有结果字段都为空，API响应(带回调):', data);
                            addMessage(`⚠️ 分析完成，但AI未返回有效内容。API状态: ${data.success}`, 'assistant');
                        }
                    }

                    // 执行回调函数
                    if (callback && typeof callback === 'function') {
                        callback();
                    }
                } else {
                    console.error(`智能问题${questionType}失败(带回调):`, data);
                    addMessage(`❌ 分析失败: ${data.error}`, 'assistant');
                }
            } catch (error) {
                addMessage(`❌ 处理过程中出现错误: ${error.message}`, 'assistant');
            }
        }

        // 智能问题处理函数
        async function handleSmartQuestion(questionType, query, sessionId) {
            try {
                const useSessionId = sessionId || backendSessionId;
                console.log(`处理智能问题: ${questionType}, 查询: ${query}, 会话ID: ${useSessionId}`);

                let message = '';
                let apiEndpoint = '';
                let requestData = { query: query, papers: currentPapers };

                switch(questionType) {
                    case 'representative':
                        message = '📚 正在为您推荐代表性文章...';
                        apiEndpoint = '/api/recommend_representative_papers';
                        break;
                    case 'further_search':
                        message = '🔍 正在为您分析进一步检索建议...';
                        apiEndpoint = '/api/suggest_further_search';
                        break;
                    case 'review_topics':
                        message = '📝 正在为您生成综述选题建议...';
                        apiEndpoint = '/api/review_topic_suggestion';
                        break;
                    case 'research_directions':
                        message = '🚀 正在分析前沿研究方向...';
                        apiEndpoint = '/api/analyze_research_frontiers';
                        break;
                    case 'research_gaps':
                        message = '🔬 正在识别研究空白与机会...';
                        apiEndpoint = '/api/identify_research_gaps';
                        break;
                    default:
                        addMessage('❌ 未知的问题类型', 'assistant');
                        return;
                }

                addMessage(message, 'assistant');

                const response = await fetch(apiEndpoint, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'sid': useSessionId
                    },
                    body: JSON.stringify(requestData)
                });

                const data = await response.json();
                console.log(`智能问题${questionType}的API响应:`, data);

                if (data.success) {
                    // 特殊处理推荐代表性文章
                    if (questionType === 'representative' && data.structured_papers) {
                        displayRecommendedPapers(data.structured_papers, data.recommendations);
                    } else {
                        const result = data.analysis || data.suggestions || data.suggestion || data.recommendations || data.result;
                        console.log(`提取到的结果字段:`, {
                            analysis: data.analysis ? `${data.analysis.length}字符` : '无',
                            suggestions: data.suggestions ? `${data.suggestions.length}字符` : '无',
                            suggestion: data.suggestion ? `${data.suggestion.length}字符` : '无',
                            recommendations: data.recommendations ? `${data.recommendations.length}字符` : '无',
                            result: data.result ? `${data.result.length}字符` : '无',
                            finalResult: result ? `${result.length}字符` : '无'
                        });
                        
                        if (result) {
                            addMessage(result, 'assistant');
                        } else {
                            console.warn('所有结果字段都为空，API响应:', data);
                            addMessage(`⚠️ 分析完成，但AI未返回有效内容。API状态: ${data.success}`, 'assistant');
                        }
                    }
                } else {
                    console.error(`智能问题${questionType}失败:`, data);
                    addMessage(`❌ 分析失败: ${data.error}`, 'assistant');
                }
            } catch (error) {
                addMessage(`❌ 处理过程中出现错误: ${error.message}`, 'assistant');
            }
        }

        // 深度分析功能处理
        function openAnalysisFeature(featureType) {
            // 根据功能类型跳转到相应的独立页面
            switch(featureType) {
                case 'journal-selection':
                    window.open('/analysis/journal-selection', '_blank');
                    break;
                case 'reference-matching':
                    window.open('/analysis/reference-matching', '_blank');
                    break;
                case 'statistical-analysis':
                    window.open('/analysis/statistical-analysis', '_blank');
                    break;
                case 'visualization-expert':
                    window.open('/analysis/visualization-expert', '_blank');
                    break;
                default:
                    addMessage(`🚧 ${featureType} 功能正在开发中，敬请期待！`, 'assistant');
            }
        }

        // AI投稿选刊功能
        function showJournalSelectionInterface() {
            const interfaceHtml = `
                <div class="analysis-interface" style="background: white; border-radius: 12px; padding: 24px; margin: 16px 0; border: 1px solid #e5e7eb;">
                    <div style="display: flex; align-items: center; margin-bottom: 20px;">
                        <div style="background: linear-gradient(135deg, #3b82f6, #1d4ed8); width: 40px; height: 40px; border-radius: 10px; display: flex; align-items: center; justify-content: center; margin-right: 12px;">
                            <i class="fas fa-journal-whills" style="color: white; font-size: 18px;"></i>
                        </div>
                        <div>
                            <h3 style="margin: 0; color: #1f2937; font-size: 18px; font-weight: 600;">📊 AI投稿选刊</h3>
                            <p style="margin: 4px 0 0 0; color: #6b7280; font-size: 14px;">基于研究内容智能推荐合适的期刊</p>
                        </div>
                    </div>

                    <form id="journalSelectionForm" style="space-y: 16px;">
                        <div style="margin-bottom: 16px;">
                            <label style="display: block; font-weight: 500; color: #374151; margin-bottom: 6px;">
                                <i class="fas fa-heading" style="margin-right: 6px; color: #3b82f6;"></i>论文标题 *
                            </label>
                            <input type="text" id="paperTitle" required
                                   style="width: 100%; padding: 10px 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px;"
                                   placeholder="请输入您的论文标题">
                        </div>

                        <div style="margin-bottom: 16px;">
                            <label style="display: block; font-weight: 500; color: #374151; margin-bottom: 6px;">
                                <i class="fas fa-align-left" style="margin-right: 6px; color: #3b82f6;"></i>论文摘要 *
                            </label>
                            <textarea id="paperAbstract" required rows="4"
                                      style="width: 100%; padding: 10px 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px; resize: vertical;"
                                      placeholder="请输入您的论文摘要，包括研究背景、方法、结果和结论"></textarea>
                        </div>

                        <div style="margin-bottom: 16px;">
                            <label style="display: block; font-weight: 500; color: #374151; margin-bottom: 6px;">
                                <i class="fas fa-tags" style="margin-right: 6px; color: #3b82f6;"></i>关键词
                            </label>
                            <input type="text" id="paperKeywords"
                                   style="width: 100%; padding: 10px 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px;"
                                   placeholder="请输入关键词，用逗号分隔（可选）">
                        </div>

                        <div style="margin-bottom: 16px;">
                            <label style="display: block; font-weight: 500; color: #374151; margin-bottom: 6px;">
                                <i class="fas fa-flask" style="margin-right: 6px; color: #3b82f6;"></i>研究领域
                            </label>
                            <select id="researchField" style="width: 100%; padding: 10px 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px;">
                                <option value="">请选择研究领域（可选）</option>
                                <option value="medicine">医学</option>
                                <option value="biology">生物学</option>
                                <option value="chemistry">化学</option>
                                <option value="physics">物理学</option>
                                <option value="engineering">工程学</option>
                                <option value="computer-science">计算机科学</option>
                                <option value="materials">材料科学</option>
                                <option value="environmental">环境科学</option>
                                <option value="other">其他</option>
                            </select>
                        </div>

                        <div style="display: flex; gap: 12px; margin-top: 20px;">
                            <button type="submit" style="flex: 1; background: linear-gradient(135deg, #3b82f6, #1d4ed8); color: white; border: none; padding: 12px 24px; border-radius: 8px; font-weight: 500; cursor: pointer; transition: all 0.2s;">
                                <i class="fas fa-search" style="margin-right: 6px;"></i>开始分析
                            </button>
                            <button type="button" onclick="cancelAnalysis()" style="background: #f3f4f6; color: #6b7280; border: 1px solid #d1d5db; padding: 12px 24px; border-radius: 8px; font-weight: 500; cursor: pointer; transition: all 0.2s;">
                                取消
                            </button>
                        </div>
                    </form>
                </div>
            `;

            addMessage('🔍 请填写以下信息，我将为您推荐合适的期刊：', 'assistant');
            addMessage(interfaceHtml, 'assistant', true);

            // 绑定表单提交事件
            setTimeout(() => {
                const form = document.getElementById('journalSelectionForm');
                if (form) {
                    form.addEventListener('submit', handleJournalSelection);
                }
            }, 100);
        }

        // 处理期刊选择分析
        async function handleJournalSelection(event) {
            event.preventDefault();

            const title = document.getElementById('paperTitle').value.trim();
            const abstract = document.getElementById('paperAbstract').value.trim();
            const keywords = document.getElementById('paperKeywords').value.trim();
            const field = document.getElementById('researchField').value;

            if (!title || !abstract) {
                alert('请填写论文标题和摘要');
                return;
            }

            // 显示分析中状态
            const submitBtn = event.target.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin" style="margin-right: 6px;"></i>分析中...';
            submitBtn.disabled = true;

            try {
                addMessage('🔄 正在分析您的研究内容，为您推荐合适的期刊...', 'assistant');

                const response = await fetch('/api/journal_selection', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        title: title,
                        abstract: abstract,
                        keywords: keywords,
                        field: field
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const result = await response.json();

                if (result.success) {
                    displayJournalRecommendations(result.recommendations);
                } else {
                    addMessage(`❌ 分析失败: ${result.error}`, 'assistant');
                }

            } catch (error) {
                console.error('期刊选择分析错误:', error);
                addMessage(`❌ 分析过程中出现错误: ${error.message}`, 'assistant');
            } finally {
                // 恢复按钮状态
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            }
        }

        // 显示期刊推荐结果
        function displayJournalRecommendations(recommendations) {
            if (!recommendations || recommendations.length === 0) {
                addMessage('❌ 未找到合适的期刊推荐，请尝试调整您的研究内容描述。', 'assistant');
                return;
            }

            let resultHtml = `
                <div style="background: white; border-radius: 12px; padding: 24px; margin: 16px 0; border: 1px solid #e5e7eb;">
                    <div style="display: flex; align-items: center; margin-bottom: 20px;">
                        <div style="background: linear-gradient(135deg, #10b981, #059669); width: 40px; height: 40px; border-radius: 10px; display: flex; align-items: center; justify-content: center; margin-right: 12px;">
                            <i class="fas fa-check-circle" style="color: white; font-size: 18px;"></i>
                        </div>
                        <div>
                            <h3 style="margin: 0; color: #1f2937; font-size: 18px; font-weight: 600;">📊 期刊推荐结果</h3>
                            <p style="margin: 4px 0 0 0; color: #6b7280; font-size: 14px;">为您找到 ${recommendations.length} 个合适的期刊</p>
                        </div>
                    </div>
            `;

            recommendations.forEach((journal, index) => {
                const matchScore = journal.match_score || 85;
                const difficulty = journal.difficulty || '中等';
                const difficultyColor = difficulty === '容易' ? '#10b981' : difficulty === '困难' ? '#ef4444' : '#f59e0b';

                resultHtml += `
                    <div style="border: 1px solid #e5e7eb; border-radius: 8px; padding: 16px; margin-bottom: 16px; background: #fafafa;">
                        <div style="display: flex; justify-content: between; align-items: start; margin-bottom: 12px;">
                            <div style="flex: 1;">
                                <h4 style="margin: 0 0 4px 0; color: #1f2937; font-size: 16px; font-weight: 600;">
                                    ${index + 1}. ${journal.name}
                                </h4>
                                <p style="margin: 0; color: #6b7280; font-size: 13px;">${journal.publisher || '期刊出版社'}</p>
                            </div>
                            <div style="text-align: right;">
                                <div style="background: linear-gradient(135deg, #3b82f6, #1d4ed8); color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500;">
                                    匹配度: ${matchScore}%
                                </div>
                            </div>
                        </div>

                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: 12px; margin-bottom: 12px;">
                            <div style="text-align: center; padding: 8px; background: white; border-radius: 6px; border: 1px solid #e5e7eb;">
                                <div style="font-size: 18px; font-weight: 600; color: #1f2937;">${journal.impact_factor || 'N/A'}</div>
                                <div style="font-size: 12px; color: #6b7280;">影响因子</div>
                            </div>
                            <div style="text-align: center; padding: 8px; background: white; border-radius: 6px; border: 1px solid #e5e7eb;">
                                <div style="font-size: 18px; font-weight: 600; color: #1f2937;">${journal.jcr_quartile || 'N/A'}</div>
                                <div style="font-size: 12px; color: #6b7280;">JCR分区</div>
                            </div>
                            <div style="text-align: center; padding: 8px; background: white; border-radius: 6px; border: 1px solid #e5e7eb;">
                                <div style="font-size: 18px; font-weight: 600; color: #1f2937;">${journal.cas_zone || 'N/A'}</div>
                                <div style="font-size: 12px; color: #6b7280;">中科院分区</div>
                            </div>
                            <div style="text-align: center; padding: 8px; background: white; border-radius: 6px; border: 1px solid #e5e7eb;">
                                <div style="font-size: 14px; font-weight: 600; color: ${difficultyColor};">${difficulty}</div>
                                <div style="font-size: 12px; color: #6b7280;">投稿难度</div>
                            </div>
                        </div>

                        <div style="margin-bottom: 12px;">
                            <h5 style="margin: 0 0 6px 0; color: #374151; font-size: 14px; font-weight: 500;">推荐理由：</h5>
                            <p style="margin: 0; color: #6b7280; font-size: 13px; line-height: 1.5;">${journal.reason || '该期刊与您的研究内容高度匹配，具有良好的学术声誉和影响力。'}</p>
                        </div>

                        ${journal.submission_tips ? `
                        <div style="background: #eff6ff; border: 1px solid #bfdbfe; border-radius: 6px; padding: 12px;">
                            <h5 style="margin: 0 0 6px 0; color: #1e40af; font-size: 14px; font-weight: 500;">
                                <i class="fas fa-lightbulb" style="margin-right: 4px;"></i>投稿建议：
                            </h5>
                            <p style="margin: 0; color: #1e40af; font-size: 13px; line-height: 1.5;">${journal.submission_tips}</p>
                        </div>
                        ` : ''}
                    </div>
                `;
            });

            resultHtml += `
                    <div style="margin-top: 20px; padding: 16px; background: #f0f9ff; border: 1px solid #bae6fd; border-radius: 8px;">
                        <h5 style="margin: 0 0 8px 0; color: #0369a1; font-size: 14px; font-weight: 500;">
                            <i class="fas fa-info-circle" style="margin-right: 6px;"></i>温馨提示：
                        </h5>
                        <ul style="margin: 0; padding-left: 16px; color: #0369a1; font-size: 13px; line-height: 1.6;">
                            <li>建议根据期刊的影响因子和分区选择合适的投稿目标</li>
                            <li>投稿前请仔细阅读期刊的投稿指南和格式要求</li>
                            <li>可以考虑同时准备多个期刊的投稿版本</li>
                            <li>关注期刊的审稿周期和发表时间</li>
                        </ul>
                    </div>
                </div>
            `;

            addMessage('✅ 期刊推荐分析完成！', 'assistant');
            addMessage(resultHtml, 'assistant', true);
        }

        // 取消分析
        function cancelAnalysis() {
            addMessage('❌ 已取消期刊选择分析。', 'assistant');
        }

        // 论文翻译功能
        function showPaperTranslationInterface() {
            const interfaceHtml = `
                <div class="analysis-interface" style="background: white; border-radius: 12px; padding: 24px; margin: 16px 0; border: 1px solid #e5e7eb;">
                    <div style="display: flex; align-items: center; margin-bottom: 20px;">
                        <div style="background: linear-gradient(135deg, #10b981, #059669); width: 40px; height: 40px; border-radius: 10px; display: flex; align-items: center; justify-content: center; margin-right: 12px;">
                            <i class="fas fa-language" style="color: white; font-size: 18px;"></i>
                        </div>
                        <div>
                            <h3 style="margin: 0; color: #1f2937; font-size: 18px; font-weight: 600;">🌐 论文翻译</h3>
                            <p style="margin: 4px 0 0 0; color: #6b7280; font-size: 14px;">专业的学术论文中英文翻译服务</p>
                        </div>
                    </div>

                    <form id="paperTranslationForm" style="space-y: 16px;">
                        <div style="margin-bottom: 16px;">
                            <label style="display: block; font-weight: 500; color: #374151; margin-bottom: 6px;">
                                <i class="fas fa-exchange-alt" style="margin-right: 6px; color: #10b981;"></i>翻译方向 *
                            </label>
                            <select id="translationDirection" required
                                    style="width: 100%; padding: 10px 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px;">
                                <option value="">请选择翻译方向</option>
                                <option value="zh-to-en">中文 → 英文</option>
                                <option value="en-to-zh">英文 → 中文</option>
                            </select>
                        </div>

                        <div style="margin-bottom: 16px;">
                            <label style="display: block; font-weight: 500; color: #374151; margin-bottom: 6px;">
                                <i class="fas fa-file-alt" style="margin-right: 6px; color: #10b981;"></i>翻译类型 *
                            </label>
                            <select id="translationType" required
                                    style="width: 100%; padding: 10px 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px;">
                                <option value="">请选择翻译类型</option>
                                <option value="title">标题翻译</option>
                                <option value="abstract">摘要翻译</option>
                                <option value="paragraph">段落翻译</option>
                                <option value="full-text">全文翻译</option>
                            </select>
                        </div>

                        <div style="margin-bottom: 16px;">
                            <label style="display: block; font-weight: 500; color: #374151; margin-bottom: 6px;">
                                <i class="fas fa-edit" style="margin-right: 6px; color: #10b981;"></i>待翻译内容 *
                            </label>
                            <textarea id="translationContent" required rows="8"
                                      style="width: 100%; padding: 10px 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px; resize: vertical;"
                                      placeholder="请输入需要翻译的内容..."></textarea>
                            <div style="font-size: 12px; color: #6b7280; margin-top: 4px;">
                                <i class="fas fa-info-circle" style="margin-right: 4px;"></i>
                                支持学术论文的标题、摘要、正文等内容翻译
                            </div>
                        </div>

                        <div style="margin-bottom: 16px;">
                            <label style="display: block; font-weight: 500; color: #374151; margin-bottom: 6px;">
                                <i class="fas fa-cogs" style="margin-right: 6px; color: #10b981;"></i>翻译选项
                            </label>
                            <div style="display: flex; flex-wrap: wrap; gap: 12px;">
                                <label style="display: flex; align-items: center; font-size: 14px;">
                                    <input type="checkbox" id="preserveTerms" style="margin-right: 6px;">
                                    保持专业术语
                                </label>
                                <label style="display: flex; align-items: center; font-size: 14px;">
                                    <input type="checkbox" id="academicStyle" checked style="margin-right: 6px;">
                                    学术写作风格
                                </label>
                                <label style="display: flex; align-items: center; font-size: 14px;">
                                    <input type="checkbox" id="formatPreservation" style="margin-right: 6px;">
                                    保持格式
                                </label>
                            </div>
                        </div>

                        <div style="display: flex; gap: 12px; margin-top: 20px;">
                            <button type="submit" style="flex: 1; background: linear-gradient(135deg, #10b981, #059669); color: white; border: none; padding: 12px 24px; border-radius: 8px; font-weight: 500; cursor: pointer; transition: all 0.2s;">
                                <i class="fas fa-language" style="margin-right: 6px;"></i>开始翻译
                            </button>
                            <button type="button" onclick="cancelTranslation()" style="background: #f3f4f6; color: #6b7280; border: 1px solid #d1d5db; padding: 12px 24px; border-radius: 8px; font-weight: 500; cursor: pointer; transition: all 0.2s;">
                                取消
                            </button>
                        </div>
                    </form>
                </div>
            `;

            addMessage('🌐 请选择翻译选项并输入需要翻译的内容：', 'assistant');
            addMessage(interfaceHtml, 'assistant', true);

            // 绑定表单提交事件
            setTimeout(() => {
                const form = document.getElementById('paperTranslationForm');
                if (form) {
                    form.addEventListener('submit', handlePaperTranslation);
                }
            }, 100);
        }

        // 处理论文翻译
        async function handlePaperTranslation(event) {
            event.preventDefault();

            const direction = document.getElementById('translationDirection').value;
            const type = document.getElementById('translationType').value;
            const content = document.getElementById('translationContent').value.trim();
            const preserveTerms = document.getElementById('preserveTerms').checked;
            const academicStyle = document.getElementById('academicStyle').checked;
            const formatPreservation = document.getElementById('formatPreservation').checked;

            if (!direction || !type || !content) {
                alert('请填写所有必填项');
                return;
            }

            // 显示翻译中状态
            const submitBtn = event.target.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin" style="margin-right: 6px;"></i>翻译中...';
            submitBtn.disabled = true;

            try {
                addMessage('🔄 正在进行专业翻译，请稍候...', 'assistant');

                const response = await fetch('/api/paper_translation', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        direction: direction,
                        type: type,
                        content: content,
                        options: {
                            preserve_terms: preserveTerms,
                            academic_style: academicStyle,
                            format_preservation: formatPreservation
                        }
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const result = await response.json();

                if (result.success) {
                    displayTranslationResult(result.translation, direction, type);
                } else {
                    addMessage(`❌ 翻译失败: ${result.error}`, 'assistant');
                }

            } catch (error) {
                console.error('论文翻译错误:', error);
                addMessage(`❌ 翻译过程中出现错误: ${error.message}`, 'assistant');
            } finally {
                // 恢复按钮状态
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            }
        }

        // 显示翻译结果
        function displayTranslationResult(translation, direction, type) {
            const directionText = direction === 'zh-to-en' ? '中文 → 英文' : '英文 → 中文';
            const typeText = {
                'title': '标题',
                'abstract': '摘要',
                'paragraph': '段落',
                'full-text': '全文'
            }[type] || type;

            const resultHtml = `
                <div style="background: white; border-radius: 12px; padding: 24px; margin: 16px 0; border: 1px solid #e5e7eb;">
                    <div style="display: flex; align-items: center; margin-bottom: 20px;">
                        <div style="background: linear-gradient(135deg, #10b981, #059669); width: 40px; height: 40px; border-radius: 10px; display: flex; align-items: center; justify-content: center; margin-right: 12px;">
                            <i class="fas fa-check-circle" style="color: white; font-size: 18px;"></i>
                        </div>
                        <div>
                            <h3 style="margin: 0; color: #1f2937; font-size: 18px; font-weight: 600;">🌐 翻译结果</h3>
                            <p style="margin: 4px 0 0 0; color: #6b7280; font-size: 14px;">${typeText}翻译 (${directionText})</p>
                        </div>
                    </div>

                    <div style="background: #f8fafc; border: 1px solid #e2e8f0; border-radius: 8px; padding: 16px; margin-bottom: 16px;">
                        <h5 style="margin: 0 0 8px 0; color: #374151; font-size: 14px; font-weight: 500;">
                            <i class="fas fa-file-text" style="margin-right: 6px; color: #10b981;"></i>翻译结果：
                        </h5>
                        <div style="background: white; border: 1px solid #d1d5db; border-radius: 6px; padding: 12px; font-size: 14px; line-height: 1.6; white-space: pre-wrap; word-wrap: break-word;">
${translation}
                        </div>
                    </div>

                    <div style="display: flex; gap: 12px; margin-top: 16px;">
                        <button onclick="copyTranslation()" style="flex: 1; background: #3b82f6; color: white; border: none; padding: 10px 16px; border-radius: 6px; font-size: 14px; cursor: pointer; transition: all 0.2s;">
                            <i class="fas fa-copy" style="margin-right: 6px;"></i>复制结果
                        </button>
                        <button onclick="downloadTranslation()" style="flex: 1; background: #10b981; color: white; border: none; padding: 10px 16px; border-radius: 6px; font-size: 14px; cursor: pointer; transition: all 0.2s;">
                            <i class="fas fa-download" style="margin-right: 6px;"></i>下载文档
                        </button>
                    </div>

                    <div style="margin-top: 16px; padding: 12px; background: #eff6ff; border: 1px solid #bfdbfe; border-radius: 6px;">
                        <h5 style="margin: 0 0 6px 0; color: #1e40af; font-size: 14px; font-weight: 500;">
                            <i class="fas fa-lightbulb" style="margin-right: 4px;"></i>翻译建议：
                        </h5>
                        <ul style="margin: 0; padding-left: 16px; color: #1e40af; font-size: 13px; line-height: 1.5;">
                            <li>建议对翻译结果进行人工校对，确保专业术语准确</li>
                            <li>注意检查数字、公式、引用格式是否正确</li>
                            <li>如需投稿，建议请母语人士进行最终润色</li>
                        </ul>
                    </div>
                </div>
            `;

            addMessage('✅ 翻译完成！', 'assistant');
            addMessage(resultHtml, 'assistant', true);

            // 保存翻译结果到全局变量，供复制和下载使用
            window.lastTranslation = translation;
        }

        // 复制翻译结果
        function copyTranslation() {
            if (window.lastTranslation) {
                navigator.clipboard.writeText(window.lastTranslation).then(() => {
                    addMessage('📋 翻译结果已复制到剪贴板！', 'assistant');
                }).catch(err => {
                    console.error('复制失败:', err);
                    addMessage('❌ 复制失败，请手动选择文本复制。', 'assistant');
                });
            }
        }

        // 下载翻译结果
        function downloadTranslation() {
            if (window.lastTranslation) {
                const blob = new Blob([window.lastTranslation], { type: 'text/plain;charset=utf-8' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `translation_${new Date().getTime()}.txt`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
                addMessage('📥 翻译结果已下载！', 'assistant');
            }
        }

        // 取消翻译
        function cancelTranslation() {
            addMessage('❌ 已取消论文翻译。', 'assistant');
        }

        // 论文润色功能
        function showPaperPolishInterface() {
            const interfaceHtml = `
                <div class="analysis-interface" style="background: white; border-radius: 12px; padding: 24px; margin: 16px 0; border: 1px solid #e5e7eb;">
                    <div style="display: flex; align-items: center; margin-bottom: 20px;">
                        <div style="background: linear-gradient(135deg, #f59e0b, #d97706); width: 40px; height: 40px; border-radius: 10px; display: flex; align-items: center; justify-content: center; margin-right: 12px;">
                            <i class="fas fa-magic" style="color: white; font-size: 18px;"></i>
                        </div>
                        <div>
                            <h3 style="margin: 0; color: #1f2937; font-size: 18px; font-weight: 600;">✨ 论文润色</h3>
                            <p style="margin: 4px 0 0 0; color: #6b7280; font-size: 14px;">专业的英文论文语言润色服务</p>
                        </div>
                    </div>

                    <form id="paperPolishForm" style="space-y: 16px;">
                        <div style="margin-bottom: 16px;">
                            <label style="display: block; font-weight: 500; color: #374151; margin-bottom: 6px;">
                                <i class="fas fa-file-alt" style="margin-right: 6px; color: #f59e0b;"></i>润色类型 *
                            </label>
                            <select id="polishType" required
                                    style="width: 100%; padding: 10px 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px;">
                                <option value="">请选择润色类型</option>
                                <option value="title">标题润色</option>
                                <option value="abstract">摘要润色</option>
                                <option value="introduction">引言润色</option>
                                <option value="methods">方法部分润色</option>
                                <option value="results">结果部分润色</option>
                                <option value="discussion">讨论部分润色</option>
                                <option value="conclusion">结论润色</option>
                                <option value="paragraph">段落润色</option>
                                <option value="full-paper">全文润色</option>
                            </select>
                        </div>

                        <div style="margin-bottom: 16px;">
                            <label style="display: block; font-weight: 500; color: #374151; margin-bottom: 6px;">
                                <i class="fas fa-graduation-cap" style="margin-right: 6px; color: #f59e0b;"></i>学术领域
                            </label>
                            <select id="academicField"
                                    style="width: 100%; padding: 10px 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px;">
                                <option value="">请选择学术领域（可选）</option>
                                <option value="medicine">医学</option>
                                <option value="biology">生物学</option>
                                <option value="chemistry">化学</option>
                                <option value="physics">物理学</option>
                                <option value="engineering">工程学</option>
                                <option value="computer-science">计算机科学</option>
                                <option value="materials">材料科学</option>
                                <option value="environmental">环境科学</option>
                                <option value="social-science">社会科学</option>
                                <option value="other">其他</option>
                            </select>
                        </div>

                        <div style="margin-bottom: 16px;">
                            <label style="display: block; font-weight: 500; color: #374151; margin-bottom: 6px;">
                                <i class="fas fa-edit" style="margin-right: 6px; color: #f59e0b;"></i>待润色内容 *
                            </label>
                            <textarea id="polishContent" required rows="10"
                                      style="width: 100%; padding: 10px 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px; resize: vertical;"
                                      placeholder="请输入需要润色的英文内容..."></textarea>
                            <div style="font-size: 12px; color: #6b7280; margin-top: 4px;">
                                <i class="fas fa-info-circle" style="margin-right: 4px;"></i>
                                支持英文论文的各个部分润色，包括语法、用词、表达等方面的改进
                            </div>
                        </div>

                        <div style="margin-bottom: 16px;">
                            <label style="display: block; font-weight: 500; color: #374151; margin-bottom: 6px;">
                                <i class="fas fa-cogs" style="margin-right: 6px; color: #f59e0b;"></i>润色重点
                            </label>
                            <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 8px;">
                                <label style="display: flex; align-items: center; font-size: 14px;">
                                    <input type="checkbox" id="grammarCheck" checked style="margin-right: 6px;">
                                    语法检查
                                </label>
                                <label style="display: flex; align-items: center; font-size: 14px;">
                                    <input type="checkbox" id="vocabularyImprovement" checked style="margin-right: 6px;">
                                    词汇提升
                                </label>
                                <label style="display: flex; align-items: center; font-size: 14px;">
                                    <input type="checkbox" id="sentenceStructure" checked style="margin-right: 6px;">
                                    句式优化
                                </label>
                                <label style="display: flex; align-items: center; font-size: 14px;">
                                    <input type="checkbox" id="academicTone" checked style="margin-right: 6px;">
                                    学术语调
                                </label>
                                <label style="display: flex; align-items: center; font-size: 14px;">
                                    <input type="checkbox" id="logicalFlow" style="margin-right: 6px;">
                                    逻辑流畅
                                </label>
                                <label style="display: flex; align-items: center; font-size: 14px;">
                                    <input type="checkbox" id="clarityImprovement" style="margin-right: 6px;">
                                    表达清晰
                                </label>
                            </div>
                        </div>

                        <div style="display: flex; gap: 12px; margin-top: 20px;">
                            <button type="submit" style="flex: 1; background: linear-gradient(135deg, #f59e0b, #d97706); color: white; border: none; padding: 12px 24px; border-radius: 8px; font-weight: 500; cursor: pointer; transition: all 0.2s;">
                                <i class="fas fa-magic" style="margin-right: 6px;"></i>开始润色
                            </button>
                            <button type="button" onclick="cancelPolish()" style="background: #f3f4f6; color: #6b7280; border: 1px solid #d1d5db; padding: 12px 24px; border-radius: 8px; font-weight: 500; cursor: pointer; transition: all 0.2s;">
                                取消
                            </button>
                        </div>
                    </form>
                </div>
            `;

            addMessage('✨ 请选择润色选项并输入需要润色的英文内容：', 'assistant');
            addMessage(interfaceHtml, 'assistant', true);

            // 绑定表单提交事件
            setTimeout(() => {
                const form = document.getElementById('paperPolishForm');
                if (form) {
                    form.addEventListener('submit', handlePaperPolish);
                }
            }, 100);
        }

        // 处理论文润色
        async function handlePaperPolish(event) {
            event.preventDefault();

            const polishType = document.getElementById('polishType').value;
            const academicField = document.getElementById('academicField').value;
            const content = document.getElementById('polishContent').value.trim();

            const grammarCheck = document.getElementById('grammarCheck').checked;
            const vocabularyImprovement = document.getElementById('vocabularyImprovement').checked;
            const sentenceStructure = document.getElementById('sentenceStructure').checked;
            const academicTone = document.getElementById('academicTone').checked;
            const logicalFlow = document.getElementById('logicalFlow').checked;
            const clarityImprovement = document.getElementById('clarityImprovement').checked;

            if (!polishType || !content) {
                alert('请选择润色类型并输入内容');
                return;
            }

            // 显示润色中状态
            const submitBtn = event.target.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin" style="margin-right: 6px;"></i>润色中...';
            submitBtn.disabled = true;

            try {
                addMessage('🔄 正在进行专业润色，请稍候...', 'assistant');

                const response = await fetch('/api/paper_polish', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        type: polishType,
                        field: academicField,
                        content: content,
                        focus_areas: {
                            grammar_check: grammarCheck,
                            vocabulary_improvement: vocabularyImprovement,
                            sentence_structure: sentenceStructure,
                            academic_tone: academicTone,
                            logical_flow: logicalFlow,
                            clarity_improvement: clarityImprovement
                        }
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const result = await response.json();

                if (result.success) {
                    displayPolishResult(result.polished_text, result.improvements, polishType);
                } else {
                    addMessage(`❌ 润色失败: ${result.error}`, 'assistant');
                }

            } catch (error) {
                console.error('论文润色错误:', error);
                addMessage(`❌ 润色过程中出现错误: ${error.message}`, 'assistant');
            } finally {
                // 恢复按钮状态
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            }
        }

        // 显示润色结果
        function displayPolishResult(polishedText, improvements, polishType) {
            const typeText = {
                'title': '标题',
                'abstract': '摘要',
                'introduction': '引言',
                'methods': '方法部分',
                'results': '结果部分',
                'discussion': '讨论部分',
                'conclusion': '结论',
                'paragraph': '段落',
                'full-paper': '全文'
            }[polishType] || polishType;

            const resultHtml = `
                <div style="background: white; border-radius: 12px; padding: 24px; margin: 16px 0; border: 1px solid #e5e7eb;">
                    <div style="display: flex; align-items: center; margin-bottom: 20px;">
                        <div style="background: linear-gradient(135deg, #f59e0b, #d97706); width: 40px; height: 40px; border-radius: 10px; display: flex; align-items: center; justify-content: center; margin-right: 12px;">
                            <i class="fas fa-check-circle" style="color: white; font-size: 18px;"></i>
                        </div>
                        <div>
                            <h3 style="margin: 0; color: #1f2937; font-size: 18px; font-weight: 600;">✨ 润色结果</h3>
                            <p style="margin: 4px 0 0 0; color: #6b7280; font-size: 14px;">${typeText}润色完成</p>
                        </div>
                    </div>

                    <div style="background: #f8fafc; border: 1px solid #e2e8f0; border-radius: 8px; padding: 16px; margin-bottom: 16px;">
                        <h5 style="margin: 0 0 8px 0; color: #374151; font-size: 14px; font-weight: 500;">
                            <i class="fas fa-file-text" style="margin-right: 6px; color: #f59e0b;"></i>润色后文本：
                        </h5>
                        <div style="background: white; border: 1px solid #d1d5db; border-radius: 6px; padding: 12px; font-size: 14px; line-height: 1.6; white-space: pre-wrap; word-wrap: break-word;">
${polishedText}
                        </div>
                    </div>

                    ${improvements ? `
                    <div style="background: #fef3c7; border: 1px solid #fbbf24; border-radius: 8px; padding: 16px; margin-bottom: 16px;">
                        <h5 style="margin: 0 0 8px 0; color: #92400e; font-size: 14px; font-weight: 500;">
                            <i class="fas fa-list-ul" style="margin-right: 6px;"></i>主要改进：
                        </h5>
                        <div style="color: #92400e; font-size: 13px; line-height: 1.5; white-space: pre-wrap;">
${improvements}
                        </div>
                    </div>
                    ` : ''}

                    <div style="display: flex; gap: 12px; margin-top: 16px;">
                        <button onclick="copyPolishedText()" style="flex: 1; background: #3b82f6; color: white; border: none; padding: 10px 16px; border-radius: 6px; font-size: 14px; cursor: pointer; transition: all 0.2s;">
                            <i class="fas fa-copy" style="margin-right: 6px;"></i>复制润色结果
                        </button>
                        <button onclick="downloadPolishedText()" style="flex: 1; background: #f59e0b; color: white; border: none; padding: 10px 16px; border-radius: 6px; font-size: 14px; cursor: pointer; transition: all 0.2s;">
                            <i class="fas fa-download" style="margin-right: 6px;"></i>下载文档
                        </button>
                    </div>

                    <div style="margin-top: 16px; padding: 12px; background: #eff6ff; border: 1px solid #bfdbfe; border-radius: 6px;">
                        <h5 style="margin: 0 0 6px 0; color: #1e40af; font-size: 14px; font-weight: 500;">
                            <i class="fas fa-lightbulb" style="margin-right: 4px;"></i>润色建议：
                        </h5>
                        <ul style="margin: 0; padding-left: 16px; color: #1e40af; font-size: 13px; line-height: 1.5;">
                            <li>建议对润色结果进行人工审核，确保符合期刊要求</li>
                            <li>注意检查专业术语和引用格式是否正确</li>
                            <li>如需投稿顶级期刊，建议请专业编辑进行最终校对</li>
                        </ul>
                    </div>
                </div>
            `;

            addMessage('✅ 润色完成！', 'assistant');
            addMessage(resultHtml, 'assistant', true);

            // 保存润色结果到全局变量，供复制和下载使用
            window.lastPolishedText = polishedText;
        }

        // 复制润色结果
        function copyPolishedText() {
            if (window.lastPolishedText) {
                navigator.clipboard.writeText(window.lastPolishedText).then(() => {
                    addMessage('📋 润色结果已复制到剪贴板！', 'assistant');
                }).catch(err => {
                    console.error('复制失败:', err);
                    addMessage('❌ 复制失败，请手动选择文本复制。', 'assistant');
                });
            }
        }

        // 下载润色结果
        function downloadPolishedText() {
            if (window.lastPolishedText) {
                const blob = new Blob([window.lastPolishedText], { type: 'text/plain;charset=utf-8' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `polished_text_${new Date().getTime()}.txt`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
                addMessage('📥 润色结果已下载！', 'assistant');
            }
        }

        // 取消润色
        function cancelPolish() {
            addMessage('❌ 已取消论文润色。', 'assistant');
        }

        // AI选题功能
        function showAITopicSelectionInterface() {
            const interfaceHtml = `
                <div class="analysis-interface" style="background: white; border-radius: 12px; padding: 24px; margin: 16px 0; border: 1px solid #e5e7eb;">
                    <div style="display: flex; align-items: center; margin-bottom: 20px;">
                        <div style="background: linear-gradient(135deg, #8b5cf6, #7c3aed); width: 40px; height: 40px; border-radius: 10px; display: flex; align-items: center; justify-content: center; margin-right: 12px;">
                            <i class="fas fa-lightbulb" style="color: white; font-size: 18px;"></i>
                        </div>
                        <div>
                            <h3 style="margin: 0; color: #1f2937; font-size: 18px; font-weight: 600;">💡 AI选题</h3>
                            <p style="margin: 4px 0 0 0; color: #6b7280; font-size: 14px;">基于前沿文献的创新研究方向推荐</p>
                        </div>
                    </div>

                    <form id="aiTopicSelectionForm" style="space-y: 16px;">
                        <div style="margin-bottom: 16px;">
                            <label style="display: block; font-weight: 500; color: #374151; margin-bottom: 6px;">
                                <i class="fas fa-flask" style="margin-right: 6px; color: #8b5cf6;"></i>研究领域 *
                            </label>
                            <select id="researchDomain" required
                                    style="width: 100%; padding: 10px 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px;">
                                <option value="">请选择研究领域</option>
                                <option value="medicine">医学</option>
                                <option value="biology">生物学</option>
                                <option value="chemistry">化学</option>
                                <option value="physics">物理学</option>
                                <option value="engineering">工程学</option>
                                <option value="computer-science">计算机科学</option>
                                <option value="materials">材料科学</option>
                                <option value="environmental">环境科学</option>
                                <option value="neuroscience">神经科学</option>
                                <option value="biotechnology">生物技术</option>
                                <option value="other">其他</option>
                            </select>
                        </div>

                        <div style="margin-bottom: 16px;">
                            <label style="display: block; font-weight: 500; color: #374151; margin-bottom: 6px;">
                                <i class="fas fa-tags" style="margin-right: 6px; color: #8b5cf6;"></i>研究兴趣关键词
                            </label>
                            <input type="text" id="researchKeywords"
                                   style="width: 100%; padding: 10px 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px;"
                                   placeholder="请输入您感兴趣的研究关键词，用逗号分隔">
                            <div style="font-size: 12px; color: #6b7280; margin-top: 4px;">
                                <i class="fas fa-info-circle" style="margin-right: 4px;"></i>
                                例如：机器学习, 癌症治疗, 纳米材料等
                            </div>
                        </div>

                        <div style="margin-bottom: 16px;">
                            <label style="display: block; font-weight: 500; color: #374151; margin-bottom: 6px;">
                                <i class="fas fa-user-graduate" style="margin-right: 6px; color: #8b5cf6;"></i>研究背景
                            </label>
                            <textarea id="researchBackground" rows="3"
                                      style="width: 100%; padding: 10px 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px; resize: vertical;"
                                      placeholder="请简要描述您的研究背景、已有经验或特定方向（可选）"></textarea>
                        </div>

                        <div style="margin-bottom: 16px;">
                            <label style="display: block; font-weight: 500; color: #374151; margin-bottom: 6px;">
                                <i class="fas fa-target" style="margin-right: 6px; color: #8b5cf6;"></i>研究目标
                            </label>
                            <select id="researchGoal"
                                    style="width: 100%; padding: 10px 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px;">
                                <option value="">请选择研究目标（可选）</option>
                                <option value="basic-research">基础研究</option>
                                <option value="applied-research">应用研究</option>
                                <option value="clinical-research">临床研究</option>
                                <option value="technology-development">技术开发</option>
                                <option value="review-analysis">综述分析</option>
                                <option value="interdisciplinary">跨学科研究</option>
                            </select>
                        </div>

                        <div style="margin-bottom: 16px;">
                            <label style="display: block; font-weight: 500; color: #374151; margin-bottom: 6px;">
                                <i class="fas fa-cogs" style="margin-right: 6px; color: #8b5cf6;"></i>选题偏好
                            </label>
                            <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 8px;">
                                <label style="display: flex; align-items: center; font-size: 14px;">
                                    <input type="checkbox" id="innovativeTopics" checked style="margin-right: 6px;">
                                    创新性强
                                </label>
                                <label style="display: flex; align-items: center; font-size: 14px;">
                                    <input type="checkbox" id="feasibleTopics" checked style="margin-right: 6px;">
                                    可行性高
                                </label>
                                <label style="display: flex; align-items: center; font-size: 14px;">
                                    <input type="checkbox" id="hotTopics" style="margin-right: 6px;">
                                    研究热点
                                </label>
                                <label style="display: flex; align-items: center; font-size: 14px;">
                                    <input type="checkbox" id="emergingTopics" style="margin-right: 6px;">
                                    新兴方向
                                </label>
                                <label style="display: flex; align-items: center; font-size: 14px;">
                                    <input type="checkbox" id="practicalTopics" style="margin-right: 6px;">
                                    实用价值
                                </label>
                                <label style="display: flex; align-items: center; font-size: 14px;">
                                    <input type="checkbox" id="fundingFriendly" style="margin-right: 6px;">
                                    易获资助
                                </label>
                            </div>
                        </div>

                        <div style="display: flex; gap: 12px; margin-top: 20px;">
                            <button type="submit" style="flex: 1; background: linear-gradient(135deg, #8b5cf6, #7c3aed); color: white; border: none; padding: 12px 24px; border-radius: 8px; font-weight: 500; cursor: pointer; transition: all 0.2s;">
                                <i class="fas fa-lightbulb" style="margin-right: 6px;"></i>生成选题建议
                            </button>
                            <button type="button" onclick="cancelTopicSelection()" style="background: #f3f4f6; color: #6b7280; border: 1px solid #d1d5db; padding: 12px 24px; border-radius: 8px; font-weight: 500; cursor: pointer; transition: all 0.2s;">
                                取消
                            </button>
                        </div>
                    </form>
                </div>
            `;

            addMessage('💡 请填写您的研究信息，我将为您推荐创新的研究选题：', 'assistant');
            addMessage(interfaceHtml, 'assistant', true);

            // 绑定表单提交事件
            setTimeout(() => {
                const form = document.getElementById('aiTopicSelectionForm');
                if (form) {
                    form.addEventListener('submit', handleAITopicSelection);
                }
            }, 100);
        }

        // 处理AI选题
        async function handleAITopicSelection(event) {
            event.preventDefault();

            const domain = document.getElementById('researchDomain').value;
            const keywords = document.getElementById('researchKeywords').value.trim();
            const background = document.getElementById('researchBackground').value.trim();
            const goal = document.getElementById('researchGoal').value;

            const innovative = document.getElementById('innovativeTopics').checked;
            const feasible = document.getElementById('feasibleTopics').checked;
            const hot = document.getElementById('hotTopics').checked;
            const emerging = document.getElementById('emergingTopics').checked;
            const practical = document.getElementById('practicalTopics').checked;
            const fundingFriendly = document.getElementById('fundingFriendly').checked;

            if (!domain) {
                alert('请选择研究领域');
                return;
            }

            // 显示分析中状态
            const submitBtn = event.target.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin" style="margin-right: 6px;"></i>分析中...';
            submitBtn.disabled = true;

            try {
                addMessage('🔄 正在分析前沿文献和研究趋势，为您生成创新选题...', 'assistant');

                const response = await fetch('/api/ai_topic_selection', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        domain: domain,
                        keywords: keywords,
                        background: background,
                        goal: goal,
                        preferences: {
                            innovative: innovative,
                            feasible: feasible,
                            hot: hot,
                            emerging: emerging,
                            practical: practical,
                            funding_friendly: fundingFriendly
                        }
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const result = await response.json();

                if (result.success) {
                    displayTopicSuggestions(result.topics);
                } else {
                    addMessage(`❌ 选题生成失败: ${result.error}`, 'assistant');
                }

            } catch (error) {
                console.error('AI选题错误:', error);
                addMessage(`❌ 选题生成过程中出现错误: ${error.message}`, 'assistant');
            } finally {
                // 恢复按钮状态
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            }
        }

        // 显示选题建议
        function displayTopicSuggestions(topics) {
            if (!topics || topics.length === 0) {
                addMessage('❌ 未生成合适的选题建议，请尝试调整研究领域或关键词。', 'assistant');
                return;
            }

            let resultHtml = `
                <div style="background: white; border-radius: 12px; padding: 24px; margin: 16px 0; border: 1px solid #e5e7eb;">
                    <div style="display: flex; align-items: center; margin-bottom: 20px;">
                        <div style="background: linear-gradient(135deg, #8b5cf6, #7c3aed); width: 40px; height: 40px; border-radius: 10px; display: flex; align-items: center; justify-content: center; margin-right: 12px;">
                            <i class="fas fa-check-circle" style="color: white; font-size: 18px;"></i>
                        </div>
                        <div>
                            <h3 style="margin: 0; color: #1f2937; font-size: 18px; font-weight: 600;">💡 AI选题建议</h3>
                            <p style="margin: 4px 0 0 0; color: #6b7280; font-size: 14px;">为您推荐 ${topics.length} 个创新研究方向</p>
                        </div>
                    </div>
            `;

            topics.forEach((topic, index) => {
                const innovationScore = topic.innovation_score || 85;
                const feasibilityScore = topic.feasibility_score || 80;
                const impactScore = topic.impact_score || 75;

                resultHtml += `
                    <div style="border: 1px solid #e5e7eb; border-radius: 8px; padding: 20px; margin-bottom: 20px; background: #fafafa;">
                        <div style="display: flex; justify-content: between; align-items: start; margin-bottom: 12px;">
                            <div style="flex: 1;">
                                <h4 style="margin: 0 0 8px 0; color: #1f2937; font-size: 16px; font-weight: 600;">
                                    ${index + 1}. ${topic.title}
                                </h4>
                                <div style="display: flex; gap: 8px; margin-bottom: 12px;">
                                    <span style="background: #ddd6fe; color: #5b21b6; padding: 2px 8px; border-radius: 12px; font-size: 12px; font-weight: 500;">
                                        ${topic.category || '研究方向'}
                                    </span>
                                    <span style="background: #fef3c7; color: #92400e; padding: 2px 8px; border-radius: 12px; font-size: 12px; font-weight: 500;">
                                        ${topic.difficulty || '中等难度'}
                                    </span>
                                </div>
                            </div>
                        </div>

                        <div style="margin-bottom: 16px;">
                            <h5 style="margin: 0 0 6px 0; color: #374151; font-size: 14px; font-weight: 500;">研究描述：</h5>
                            <p style="margin: 0; color: #6b7280; font-size: 13px; line-height: 1.5;">${topic.description}</p>
                        </div>

                        <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 12px; margin-bottom: 16px;">
                            <div style="text-align: center; padding: 8px; background: white; border-radius: 6px; border: 1px solid #e5e7eb;">
                                <div style="font-size: 16px; font-weight: 600; color: #8b5cf6;">${innovationScore}%</div>
                                <div style="font-size: 12px; color: #6b7280;">创新性</div>
                            </div>
                            <div style="text-align: center; padding: 8px; background: white; border-radius: 6px; border: 1px solid #e5e7eb;">
                                <div style="font-size: 16px; font-weight: 600; color: #10b981;">${feasibilityScore}%</div>
                                <div style="font-size: 12px; color: #6b7280;">可行性</div>
                            </div>
                            <div style="text-align: center; padding: 8px; background: white; border-radius: 6px; border: 1px solid #e5e7eb;">
                                <div style="font-size: 16px; font-weight: 600; color: #f59e0b;">${impactScore}%</div>
                                <div style="font-size: 12px; color: #6b7280;">影响力</div>
                            </div>
                        </div>

                        ${topic.research_methods ? `
                        <div style="margin-bottom: 12px;">
                            <h5 style="margin: 0 0 6px 0; color: #374151; font-size: 14px; font-weight: 500;">建议研究方法：</h5>
                            <p style="margin: 0; color: #6b7280; font-size: 13px; line-height: 1.5;">${topic.research_methods}</p>
                        </div>
                        ` : ''}

                        ${topic.expected_outcomes ? `
                        <div style="margin-bottom: 12px;">
                            <h5 style="margin: 0 0 6px 0; color: #374151; font-size: 14px; font-weight: 500;">预期成果：</h5>
                            <p style="margin: 0; color: #6b7280; font-size: 13px; line-height: 1.5;">${topic.expected_outcomes}</p>
                        </div>
                        ` : ''}

                        ${topic.challenges ? `
                        <div style="background: #fef2f2; border: 1px solid #fecaca; border-radius: 6px; padding: 12px;">
                            <h5 style="margin: 0 0 6px 0; color: #dc2626; font-size: 14px; font-weight: 500;">
                                <i class="fas fa-exclamation-triangle" style="margin-right: 4px;"></i>潜在挑战：
                            </h5>
                            <p style="margin: 0; color: #dc2626; font-size: 13px; line-height: 1.5;">${topic.challenges}</p>
                        </div>
                        ` : ''}
                    </div>
                `;
            });

            resultHtml += `
                    <div style="margin-top: 20px; padding: 16px; background: #f0f9ff; border: 1px solid #bae6fd; border-radius: 8px;">
                        <h5 style="margin: 0 0 8px 0; color: #0369a1; font-size: 14px; font-weight: 500;">
                            <i class="fas fa-info-circle" style="margin-right: 6px;"></i>选题建议：
                        </h5>
                        <ul style="margin: 0; padding-left: 16px; color: #0369a1; font-size: 13px; line-height: 1.6;">
                            <li>建议进一步调研相关文献，确认研究空白</li>
                            <li>评估研究所需的资源和时间</li>
                            <li>考虑与导师或同行讨论选题的可行性</li>
                            <li>关注相关领域的最新发展动态</li>
                        </ul>
                    </div>
                </div>
            `;

            addMessage('✅ AI选题分析完成！', 'assistant');
            addMessage(resultHtml, 'assistant', true);
        }

        // 取消选题
        function cancelTopicSelection() {
            addMessage('❌ 已取消AI选题分析。', 'assistant');
        }

        // 设置功能
        const defaultSettings = {
            impactFactorMin: '',
            impactFactorMax: '',
            jcrQuartiles: ['Q1', 'Q2', 'Q3', 'Q4'],
            casZones: ['1', '2', '3', '4'],
            publicationTypes: ['Journal Article', 'Review', 'Meta-Analysis'],
            yearStart: 2010,  // 改为更宽松的年份范围
            yearEnd: 2025,
            papersLimit: 500,
            languages: ['eng']
        };

        let currentSettings = loadSettings();

        function loadSettings() {
            const saved = localStorage.getItem('nnscholar_settings');
            return saved ? JSON.parse(saved) : { ...defaultSettings };
        }

        function saveSettingsToStorage() {
            try {
                localStorage.setItem('nnscholar_settings', JSON.stringify(currentSettings));
            } catch (error) {
                if (error.name === 'QuotaExceededError') {
                    console.warn('localStorage配额超出，无法保存设置');
                    // 可以考虑清理一些数据或提示用户
                } else {
                    console.error('保存设置失败:', error);
                }
            }
        }

        function openSettings() {
            const panel = document.getElementById('settingsPanel');
            panel.style.display = 'flex';

            // 填充当前设置值
            document.getElementById('impactFactorMin').value = currentSettings.impactFactorMin || '';
            document.getElementById('impactFactorMax').value = currentSettings.impactFactorMax || '';

            // JCR分区
            ['Q1', 'Q2', 'Q3', 'Q4'].forEach(q => {
                document.getElementById(`jcr${q}`).checked = currentSettings.jcrQuartiles.includes(q);
            });

            // 中科院分区
            ['1', '2', '3', '4'].forEach(zone => {
                document.getElementById(`cas${zone}`).checked = currentSettings.casZones.includes(zone);
            });

            // 文献类型
            document.getElementById('typeArticle').checked = currentSettings.publicationTypes.includes('Journal Article');
            document.getElementById('typeReview').checked = currentSettings.publicationTypes.includes('Review');
            document.getElementById('typeMeta').checked = currentSettings.publicationTypes.includes('Meta-Analysis');
            document.getElementById('typeCase').checked = currentSettings.publicationTypes.includes('Case Reports');

            // 年份
            document.getElementById('yearStart').value = currentSettings.yearStart;
            document.getElementById('yearEnd').value = currentSettings.yearEnd;

            // 文献数量
            document.getElementById('papersLimit').value = currentSettings.papersLimit;

            // 语言
            document.getElementById('langEn').checked = currentSettings.languages.includes('eng');
            document.getElementById('langCh').checked = currentSettings.languages.includes('chi');
            document.getElementById('langOther').checked = currentSettings.languages.includes('other');
        }

        function closeSettings() {
            document.getElementById('settingsPanel').style.display = 'none';
        }

        function saveSettings() {
            // 收集表单数据
            const newSettings = {
                impactFactorMin: document.getElementById('impactFactorMin').value,
                impactFactorMax: document.getElementById('impactFactorMax').value,
                jcrQuartiles: [],
                casZones: [],
                publicationTypes: [],
                yearStart: parseInt(document.getElementById('yearStart').value) || 2020,
                yearEnd: parseInt(document.getElementById('yearEnd').value) || 2025,
                papersLimit: parseInt(document.getElementById('papersLimit').value) || 500,
                languages: []
            };

            // JCR分区
            ['Q1', 'Q2', 'Q3', 'Q4'].forEach(q => {
                if (document.getElementById(`jcr${q}`).checked) {
                    newSettings.jcrQuartiles.push(q);
                }
            });

            // 中科院分区
            ['1', '2', '3', '4'].forEach(zone => {
                if (document.getElementById(`cas${zone}`).checked) {
                    newSettings.casZones.push(zone);
                }
            });

            // 文献类型
            if (document.getElementById('typeArticle').checked) newSettings.publicationTypes.push('Journal Article');
            if (document.getElementById('typeReview').checked) newSettings.publicationTypes.push('Review');
            if (document.getElementById('typeMeta').checked) newSettings.publicationTypes.push('Meta-Analysis');
            if (document.getElementById('typeCase').checked) newSettings.publicationTypes.push('Case Reports');

            // 语言
            if (document.getElementById('langEn').checked) newSettings.languages.push('eng');
            if (document.getElementById('langCh').checked) newSettings.languages.push('chi');
            if (document.getElementById('langOther').checked) newSettings.languages.push('other');

            // 验证设置
            if (newSettings.yearStart > newSettings.yearEnd) {
                alert('开始年份不能大于结束年份');
                return;
            }

            if (newSettings.impactFactorMin && newSettings.impactFactorMax &&
                parseFloat(newSettings.impactFactorMin) > parseFloat(newSettings.impactFactorMax)) {
                alert('最小影响因子不能大于最大影响因子');
                return;
            }

            if (newSettings.jcrQuartiles.length === 0) {
                alert('请至少选择一个JCR分区');
                return;
            }

            if (newSettings.publicationTypes.length === 0) {
                alert('请至少选择一种文献类型');
                return;
            }

            // 保存设置
            currentSettings = newSettings;
            saveSettingsToStorage();
            closeSettings();

            // 显示成功消息
            addMessage('✅ 设置已保存，新的筛选条件将应用于下次检索。', 'assistant');
        }

        function resetSettings() {
            if (confirm('确定要重置为默认设置吗？')) {
                currentSettings = { ...defaultSettings };
                saveSettingsToStorage();
                openSettings(); // 重新打开以显示重置后的值
                addMessage('✅ 设置已重置为默认值。', 'assistant');
            }
        }

        // 移动端侧边栏控制
        function toggleMobileSidebar() {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.toggle('open');
        }

        // 点击面板外部关闭设置
        document.addEventListener('click', function(e) {
            const settingsPanel = document.getElementById('settingsPanel');
            const settingsContent = document.querySelector('.settings-content');

            if (settingsPanel && settingsPanel.style.display === 'flex' &&
                !settingsContent.contains(e.target) &&
                !e.target.classList.contains('settings-btn')) {
                closeSettings();
            }
        });

        // 初始化Socket.IO连接
        function initializeSocket() {
            // 初始化Socket.IO连接
            socket = io({
                transports: ['websocket'],
                reconnection: true,
                reconnectionAttempts: 5,
                reconnectionDelay: 1000,
                timeout: 20000,
                query: { sessionId: backendSessionId }
            });

            // 连接事件处理
            socket.on('connect', () => {
                console.log('Socket.IO连接成功，会话ID:', backendSessionId);
                showToast('服务器连接成功', 'success');
            });

            socket.on('connect_error', (error) => {
                console.error('Socket.IO连接错误:', error);
                showToast('连接服务器失败，正在重试...', 'error');
            });

            socket.on('disconnect', (reason) => {
                console.log('Socket.IO断开连接:', reason);
                showToast('服务器连接断开，正在重连...', 'warning');
                if (reason === 'io server disconnect') {
                    socket.connect();
                }
            });

            socket.on('reconnect', (attemptNumber) => {
                console.log('Socket.IO重连成功，尝试次数:', attemptNumber);
                showToast('服务器重连成功', 'success');
            });

            // 监听搜索进度
            socket.on('search_progress', function(data) {
                console.log('收到搜索进度更新:', data);
                updateSearchProgress(data);
            });

            // 监听错误事件
            socket.on('search_error', function(data) {
                console.error('收到搜索错误:', data);
                addMessage(`❌ 搜索错误: ${data.error || '未知错误'}`, 'assistant');
            });
        }

        // 更新搜索进度
        function updateSearchProgress(data) {
            const percentage = Math.min(100, Math.max(0, data.percentage || 0));
            const message = data.message || '正在处理...';

            // 更新最后一条助手消息
            const messages = document.querySelectorAll('.message.assistant');
            const lastMessage = messages[messages.length - 1];

            if (lastMessage) {
                const content = lastMessage.querySelector('.message-content');
                if (data.stage === 'complete') {
                    // 搜索完成，将在displaySearchResults中处理
                    return;
                } else if (data.stage === 'error') {
                    content.textContent = `❌ ${message}`;
                } else {
                    content.innerHTML = `
                        <div class="progress-info">
                            <div class="progress-message">${message}</div>
                            <div class="progress-bar-container">
                                <div class="progress-bar" style="width: ${percentage}%"></div>
                            </div>
                            <div class="progress-percentage">${Math.round(percentage)}%</div>
                        </div>
                    `;
                }
            }
        }

        // 显示Toast消息
        function showToast(message, type = 'info') {
            // 创建toast元素
            const toast = document.createElement('div');
            toast.className = `toast toast-${type}`;
            toast.textContent = message;

            // 添加样式
            Object.assign(toast.style, {
                position: 'fixed',
                top: '20px',
                right: '20px',
                padding: '12px 20px',
                borderRadius: '6px',
                color: 'white',
                fontWeight: '500',
                zIndex: '9999',
                opacity: '0',
                transform: 'translateY(-20px)',
                transition: 'all 0.3s ease'
            });

            // 根据类型设置背景色
            const colors = {
                success: '#10b981',
                error: '#ef4444',
                warning: '#f59e0b',
                info: '#3b82f6'
            };
            toast.style.backgroundColor = colors[type] || colors.info;

            // 添加到页面
            document.body.appendChild(toast);

            // 显示动画
            setTimeout(() => {
                toast.style.opacity = '1';
                toast.style.transform = 'translateY(0)';
            }, 100);

            // 自动隐藏
            setTimeout(() => {
                toast.style.opacity = '0';
                toast.style.transform = 'translateY(-20px)';
                setTimeout(() => {
                    document.body.removeChild(toast);
                }, 300);
            }, 3000);
        }

        // 添加清理存储的功能
        function clearChatHistory() {
            if (confirm('确定要清除所有聊天记录吗？此操作不可撤销。')) {
                try {
                    localStorage.removeItem('nnscholar_chat_history');
                    chatHistory.sessions = [];
                    chatHistory.currentSessionId = null;

                    // 刷新页面
                    location.reload();
                } catch (error) {
                    console.error('清理聊天记录失败:', error);
                }
            }
        }

        // 检查存储使用情况
        function checkStorageUsage() {
            try {
                const chatData = localStorage.getItem('nnscholar_chat_history');
                const settingsData = localStorage.getItem('nnscholar_settings');

                const chatSize = chatData ? new Blob([chatData]).size : 0;
                const settingsSize = settingsData ? new Blob([settingsData]).size : 0;
                const totalSize = chatSize + settingsSize;

                console.log(`存储使用情况:
                    聊天记录: ${(chatSize / 1024).toFixed(2)} KB
                    设置: ${(settingsSize / 1024).toFixed(2)} KB
                    总计: ${(totalSize / 1024).toFixed(2)} KB`);

                // 如果超过4MB，提示用户清理
                if (totalSize > 4 * 1024 * 1024) {
                    console.warn('存储空间使用过多，建议清理聊天记录');
                    showToast('存储空间不足，建议清理聊天记录', 'warning');
                }
            } catch (error) {
                console.error('检查存储使用情况失败:', error);
            }
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化Socket.IO连接
            initializeSocket();

            // 检查存储使用情况
            checkStorageUsage();

            // 更新历史面板
            chatHistory.updateHistoryPanel();
            
            // 添加事件委托处理智能推荐卡片点击
            document.addEventListener('click', function(e) {
                const recCard = e.target.closest('.rec-card');
                if (recCard && recCard.dataset.type) {
                    const questionType = recCard.dataset.type;
                    const query = recCard.dataset.query;
                    const sessionId = recCard.dataset.session;
                    
                    console.log('点击智能推荐卡片:', questionType, query, sessionId);
                    handleSmartQuestion(questionType, query, sessionId);
                    return;
                }
                
                // 处理action按钮点击
                const actionBtn = e.target.closest('.action-btn');
                if (actionBtn && actionBtn.dataset.action) {
                    const action = actionBtn.dataset.action;
                    const query = actionBtn.dataset.query;
                    const sessionId = actionBtn.dataset.session;
                    
                    console.log('点击action按钮:', action, query, sessionId);
                    
                    switch(action) {
                        case 'analyzeResearchStatus':
                            analyzeResearchStatus(query, sessionId);
                            break;
                        case 'generateReviewTopicSuggestions':
                            generateReviewTopicSuggestions(query, sessionId);
                            break;
                        case 'generateResearchTopicSuggestions':
                            generateResearchTopicSuggestions(query, sessionId);
                            break;
                        case 'generateCompleteReview':
                            generateCompleteReview(query, sessionId);
                            break;
                        case 'preciseSearch':
                            preciseSearch(sessionId);
                            break;
                        case 'exportExcel':
                            exportResults('excel', sessionId);
                            break;
                        case 'exportWord':
                            exportResults('word', sessionId);
                            break;
                    }
                }
            });
            
            console.log('NNScholar 初始化完成');
        });

        // 统计分析专家界面
        function showStatisticalAnalysisInterface() {
            const interfaceHtml = `
                <div class="analysis-interface" style="background: white; border-radius: 12px; padding: 24px; margin: 16px 0; border: 1px solid #e5e7eb;">
                    <div style="display: flex; align-items: center; margin-bottom: 20px;">
                        <div style="background: linear-gradient(135deg, #10b981, #059669); width: 40px; height: 40px; border-radius: 10px; display: flex; align-items: center; justify-content: center; margin-right: 12px;">
                            📈
                        </div>
                        <div>
                            <h3 style="margin: 0; color: #1f2937; font-size: 18px; font-weight: 600;">📈 统计分析专家</h3>
                            <p style="margin: 4px 0 0 0; color: #6b7280; font-size: 14px;">为您的研究设计科学合理的统计分析方法</p>
                        </div>
                    </div>

                    <form id="statisticalAnalysisForm" style="space-y: 16px;">
                        <div style="margin-bottom: 16px;">
                            <label style="display: block; font-weight: 500; color: #374151; margin-bottom: 6px;">
                                🎯 研究问题 *
                            </label>
                            <textarea id="researchQuestion" required rows="3"
                                      style="width: 100%; padding: 10px 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px; resize: vertical;"
                                      placeholder="请详细描述您的研究问题，例如：探讨某种治疗方法对患者康复效果的影响"></textarea>
                        </div>

                        <div style="margin-bottom: 16px;">
                            <label style="display: block; font-weight: 500; color: #374151; margin-bottom: 6px;">
                                📊 研究设计类型
                            </label>
                            <select id="studyDesign" style="width: 100%; padding: 10px 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px;">
                                <option value="">请选择研究设计类型</option>
                                <option value="experimental">实验研究</option>
                                <option value="observational">观察性研究</option>
                                <option value="cross-sectional">横断面研究</option>
                                <option value="cohort">队列研究</option>
                                <option value="case-control">病例对照研究</option>
                                <option value="meta-analysis">Meta分析</option>
                                <option value="systematic-review">系统综述</option>
                                <option value="other">其他</option>
                            </select>
                        </div>

                        <div style="margin-bottom: 16px;">
                            <label style="display: block; font-weight: 500; color: #374151; margin-bottom: 6px;">
                                🔢 数据类型
                            </label>
                            <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 8px;">
                                <label style="display: flex; align-items: center; padding: 8px; border: 1px solid #d1d5db; border-radius: 6px; cursor: pointer;">
                                    <input type="checkbox" value="continuous" style="margin-right: 8px;"> 连续变量
                                </label>
                                <label style="display: flex; align-items: center; padding: 8px; border: 1px solid #d1d5db; border-radius: 6px; cursor: pointer;">
                                    <input type="checkbox" value="categorical" style="margin-right: 8px;"> 分类变量
                                </label>
                                <label style="display: flex; align-items: center; padding: 8px; border: 1px solid #d1d5db; border-radius: 6px; cursor: pointer;">
                                    <input type="checkbox" value="ordinal" style="margin-right: 8px;"> 有序变量
                                </label>
                                <label style="display: flex; align-items: center; padding: 8px; border: 1px solid #d1d5db; border-radius: 6px; cursor: pointer;">
                                    <input type="checkbox" value="time-series" style="margin-right: 8px;"> 时间序列
                                </label>
                            </div>
                        </div>

                        <div style="margin-bottom: 16px;">
                            <label style="display: block; font-weight: 500; color: #374151; margin-bottom: 6px;">
                                👥 样本信息
                            </label>
                            <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 12px;">
                                <div>
                                    <input type="number" id="sampleSize" placeholder="样本量"
                                           style="width: 100%; padding: 8px 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px;">
                                </div>
                                <div>
                                    <select id="groupNumber" style="width: 100%; padding: 8px 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px;">
                                        <option value="">组别数量</option>
                                        <option value="1">单组</option>
                                        <option value="2">两组</option>
                                        <option value="3">三组</option>
                                        <option value="multiple">多组</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div style="margin-bottom: 20px;">
                            <label style="display: block; font-weight: 500; color: #374151; margin-bottom: 6px;">
                                📝 其他要求
                            </label>
                            <textarea id="additionalRequirements" rows="2"
                                      style="width: 100%; padding: 10px 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px; resize: vertical;"
                                      placeholder="请描述其他特殊要求，如特定的统计软件、显著性水平等（可选）"></textarea>
                        </div>

                        <div style="display: flex; gap: 12px;">
                            <button type="submit" style="flex: 1; background: linear-gradient(135deg, #10b981, #059669); color: white; border: none; padding: 12px 24px; border-radius: 8px; font-weight: 500; cursor: pointer;">
                                🚀 获取统计分析建议
                            </button>
                            <button type="button" onclick="document.getElementById('analysisSection').style.display = 'block'; this.closest('.analysis-interface').remove();"
                                    style="background: #f3f4f6; color: #374151; border: none; padding: 12px 24px; border-radius: 8px; font-weight: 500; cursor: pointer;">
                                返回
                            </button>
                        </div>
                    </form>
                </div>
            `;

            addMessage(interfaceHtml, 'assistant');

            // 绑定表单提交事件
            setTimeout(() => {
                const form = document.getElementById('statisticalAnalysisForm');
                if (form) {
                    form.addEventListener('submit', handleStatisticalAnalysisSubmit);
                }
            }, 100);
        }

        // 绘图建议专家界面
        function showVisualizationExpertInterface() {
            const interfaceHtml = `
                <div class="analysis-interface" style="background: white; border-radius: 12px; padding: 24px; margin: 16px 0; border: 1px solid #e5e7eb;">
                    <div style="display: flex; align-items: center; margin-bottom: 20px;">
                        <div style="background: linear-gradient(135deg, #8b5cf6, #7c3aed); width: 40px; height: 40px; border-radius: 10px; display: flex; align-items: center; justify-content: center; margin-right: 12px;">
                            📊
                        </div>
                        <div>
                            <h3 style="margin: 0; color: #1f2937; font-size: 18px; font-weight: 600;">📊 绘图建议专家</h3>
                            <p style="margin: 4px 0 0 0; color: #6b7280; font-size: 14px;">为您的数据选择最合适的图表和可视化方法</p>
                        </div>
                    </div>

                    <form id="visualizationExpertForm" style="space-y: 16px;">
                        <div style="margin-bottom: 16px;">
                            <label style="display: block; font-weight: 500; color: #374151; margin-bottom: 6px;">
                                🎯 可视化目的 *
                            </label>
                            <textarea id="visualizationPurpose" required rows="3"
                                      style="width: 100%; padding: 10px 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px; resize: vertical;"
                                      placeholder="请描述您想要通过图表展示什么，例如：比较不同治疗组的疗效差异、展示变量间的相关关系等"></textarea>
                        </div>

                        <div style="margin-bottom: 16px;">
                            <label style="display: block; font-weight: 500; color: #374151; margin-bottom: 6px;">
                                📊 数据特征
                            </label>
                            <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 8px;">
                                <label style="display: flex; align-items: center; padding: 8px; border: 1px solid #d1d5db; border-radius: 6px; cursor: pointer;">
                                    <input type="checkbox" value="numerical" style="margin-right: 8px;"> 数值型数据
                                </label>
                                <label style="display: flex; align-items: center; padding: 8px; border: 1px solid #d1d5db; border-radius: 6px; cursor: pointer;">
                                    <input type="checkbox" value="categorical" style="margin-right: 8px;"> 分类数据
                                </label>
                                <label style="display: flex; align-items: center; padding: 8px; border: 1px solid #d1d5db; border-radius: 6px; cursor: pointer;">
                                    <input type="checkbox" value="time-based" style="margin-right: 8px;"> 时间序列
                                </label>
                                <label style="display: flex; align-items: center; padding: 8px; border: 1px solid #d1d5db; border-radius: 6px; cursor: pointer;">
                                    <input type="checkbox" value="geographical" style="margin-right: 8px;"> 地理数据
                                </label>
                            </div>
                        </div>

                        <div style="margin-bottom: 16px;">
                            <label style="display: block; font-weight: 500; color: #374151; margin-bottom: 6px;">
                                🔢 变量数量
                            </label>
                            <select id="variableCount" style="width: 100%; padding: 10px 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px;">
                                <option value="">请选择变量数量</option>
                                <option value="1">单变量</option>
                                <option value="2">双变量</option>
                                <option value="3">三变量</option>
                                <option value="multiple">多变量</option>
                            </select>
                        </div>

                        <div style="margin-bottom: 16px;">
                            <label style="display: block; font-weight: 500; color: #374151; margin-bottom: 6px;">
                                👥 目标受众
                            </label>
                            <select id="targetAudience" style="width: 100%; padding: 10px 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px;">
                                <option value="">请选择目标受众</option>
                                <option value="academic">学术期刊</option>
                                <option value="conference">学术会议</option>
                                <option value="clinical">临床医生</option>
                                <option value="public">公众科普</option>
                                <option value="policy">政策制定者</option>
                                <option value="other">其他</option>
                            </select>
                        </div>

                        <div style="margin-bottom: 20px;">
                            <label style="display: block; font-weight: 500; color: #374151; margin-bottom: 6px;">
                                📝 数据描述
                            </label>
                            <textarea id="dataDescription" rows="3"
                                      style="width: 100%; padding: 10px 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px; resize: vertical;"
                                      placeholder="请简要描述您的数据，包括样本量、主要变量、数据分布等（可选）"></textarea>
                        </div>

                        <div style="display: flex; gap: 12px;">
                            <button type="submit" style="flex: 1; background: linear-gradient(135deg, #8b5cf6, #7c3aed); color: white; border: none; padding: 12px 24px; border-radius: 8px; font-weight: 500; cursor: pointer;">
                                🎨 获取绘图建议
                            </button>
                            <button type="button" onclick="document.getElementById('analysisSection').style.display = 'block'; this.closest('.analysis-interface').remove();"
                                    style="background: #f3f4f6; color: #374151; border: none; padding: 12px 24px; border-radius: 8px; font-weight: 500; cursor: pointer;">
                                返回
                            </button>
                        </div>
                    </form>
                </div>
            `;

            addMessage(interfaceHtml, 'assistant');

            // 绑定表单提交事件
            setTimeout(() => {
                const form = document.getElementById('visualizationExpertForm');
                if (form) {
                    form.addEventListener('submit', handleVisualizationExpertSubmit);
                }
            }, 100);
        }

        // 处理统计分析专家表单提交
        function handleStatisticalAnalysisSubmit(event) {
            event.preventDefault();

            const formData = {
                researchQuestion: document.getElementById('researchQuestion').value,
                studyDesign: document.getElementById('studyDesign').value,
                dataTypes: Array.from(document.querySelectorAll('#statisticalAnalysisForm input[type="checkbox"]:checked')).map(cb => cb.value),
                sampleSize: document.getElementById('sampleSize').value,
                groupNumber: document.getElementById('groupNumber').value,
                additionalRequirements: document.getElementById('additionalRequirements').value
            };

            if (!formData.researchQuestion.trim()) {
                alert('请填写研究问题');
                return;
            }

            // 构建专业提示词
            const prompt = `作为统计分析专家，请为以下研究提供科学合理的统计分析建议：

研究问题：${formData.researchQuestion}
研究设计：${formData.studyDesign || '未指定'}
数据类型：${formData.dataTypes.join(', ') || '未指定'}
样本量：${formData.sampleSize || '未指定'}
组别数量：${formData.groupNumber || '未指定'}
其他要求：${formData.additionalRequirements || '无'}

请提供以下内容：
1. 推荐的统计方法及其理由
2. 数据预处理建议
3. 假设检验的选择
4. 效应量计算建议
5. 多重比较校正（如适用）
6. 统计软件推荐
7. 结果解释要点
8. 可能的局限性和注意事项

请确保建议科学严谨，符合统计学原理。`;

            // 发送到DeepSeek进行分析
            sendToDeepSeekAnalysis(prompt, '统计分析专家');
        }

        // 处理绘图建议专家表单提交
        function handleVisualizationExpertSubmit(event) {
            event.preventDefault();

            const formData = {
                visualizationPurpose: document.getElementById('visualizationPurpose').value,
                dataCharacteristics: Array.from(document.querySelectorAll('#visualizationExpertForm input[type="checkbox"]:checked')).map(cb => cb.value),
                variableCount: document.getElementById('variableCount').value,
                targetAudience: document.getElementById('targetAudience').value,
                dataDescription: document.getElementById('dataDescription').value
            };

            if (!formData.visualizationPurpose.trim()) {
                alert('请填写可视化目的');
                return;
            }

            // 构建专业提示词
            const prompt = `作为数据可视化专家，请为以下需求提供最合适的图表和可视化建议：

可视化目的：${formData.visualizationPurpose}
数据特征：${formData.dataCharacteristics.join(', ') || '未指定'}
变量数量：${formData.variableCount || '未指定'}
目标受众：${formData.targetAudience || '未指定'}
数据描述：${formData.dataDescription || '无'}

请提供以下内容：
1. 推荐的图表类型及其理由
2. 图表设计要点（颜色、布局、标注等）
3. 数据预处理建议
4. 统计图形的最佳实践
5. 不同软件的实现方法（R、Python、SPSS等）
6. 图表美化和专业化建议
7. 常见错误和避免方法
8. 针对目标受众的展示技巧

请确保建议实用且符合数据可视化的最佳实践。`;

            // 发送到DeepSeek进行分析
            sendToDeepSeekAnalysis(prompt, '绘图建议专家');
        }

        // 发送到DeepSeek进行分析的通用函数
        async function sendToDeepSeekAnalysis(prompt, expertType) {
            const messageId = Date.now();
            addMessage(`🔄 ${expertType}正在分析中，请稍候...`, 'assistant', messageId);

            try {
                const response = await fetch('/api/deepseek_analysis', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        prompt: prompt,
                        expert_type: expertType
                    })
                });

                const data = await response.json();

                if (data.success) {
                    updateMessage(messageId, `✅ **${expertType}分析结果**\n\n${data.analysis}`);
                } else {
                    updateMessage(messageId, `❌ 分析失败: ${data.error}`);
                }
            } catch (error) {
                console.error('DeepSeek分析错误:', error);
                updateMessage(messageId, `❌ 分析过程中出现错误: ${error.message}`);
            }
        }
    </script>
</body>
</html>
