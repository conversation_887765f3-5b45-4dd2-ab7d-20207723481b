<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NNScholar - 医学文献智能检索与分析系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <!-- 添加词云图支持 -->
    <script src="https://cdn.jsdelivr.net/npm/echarts-wordcloud@2.1.0/dist/echarts-wordcloud.min.js"></script>
    <!-- 添加vis.js网络图谱支持 - 使用可靠的CDN -->
    <script>
        // 动态加载vis.js库，带有错误处理
        function loadVisJS() {
            return new Promise((resolve, reject) => {
                // 首先检查是否已经加载
                if (typeof vis !== 'undefined') {
                    console.log('✅ vis.js库已存在');
                    resolve();
                    return;
                }

                const script = document.createElement('script');
                script.src = 'https://unpkg.com/vis-network@9.1.9/standalone/umd/vis-network.min.js';
                script.onload = function() {
                    if (typeof vis !== 'undefined') {
                        console.log('✅ vis.js库加载成功');
                        resolve();
                    } else {
                        console.error('❌ vis.js库加载后仍未定义');
                        reject(new Error('vis library not defined after loading'));
                    }
                };
                script.onerror = function() {
                    console.error('❌ vis.js库加载失败');
                    reject(new Error('Failed to load vis.js'));
                };
                document.head.appendChild(script);
            });
        }

        // 页面加载完成后加载vis.js
        document.addEventListener('DOMContentLoaded', function() {
            loadVisJS().catch(error => {
                console.error('vis.js加载错误:', error);
                if (typeof showToast === 'function') {
                    showToast('网络图谱库加载失败，请检查网络连接', 'error');
                }
            });
        });
    </script>
    <style>
        :root {
            --primary-color: #8B5CF6;
            --secondary-color: #6B7280;
            --background-color: #FFFFFF;
            --card-background: #FFFFFF;
            --text-color: #1F2937;
            --border-color: #E5E7EB;
            --hover-color: #F3F4F6;
            --card-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            --transition-speed: 0.3s;
        }

        body {
            padding-top: 70px;
            background-color: var(--background-color);
            color: var(--text-color);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
        }

        .navbar {
            background: #FFFFFF;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .navbar-brand {
            font-weight: 600;
            font-size: 1.5rem;
            color: var(--primary-color) !important;
        }

        .container {
            max-width: 1000px;
            padding: 0 20px;
        }

        .card {
            background-color: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            margin-bottom: 24px;
            box-shadow: var(--card-shadow);
            transition: transform var(--transition-speed);
        }

        .card:hover {
            transform: translateY(-2px);
        }

        .card-title {
            color: var(--primary-color);
            font-weight: 600;
            margin-bottom: 1.5rem;
            font-size: 1.25rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            font-weight: 500;
            color: var(--secondary-color);
            margin-bottom: 0.5rem;
        }

        .form-control {
            background-color: var(--card-background);
            border: 1px solid var(--border-color);
            color: var(--text-color);
            border-radius: 8px;
            padding: 0.75rem;
            transition: border-color var(--transition-speed);
        }

        .form-control:focus {
            background-color: var(--card-background);
            border-color: var(--primary-color);
            color: var(--text-color);
            box-shadow: 0 0 0 0.2rem rgba(139, 92, 246, 0.25);
        }

        .btn-primary {
            background-color: #90afff;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            font-weight: 500;
            transition: all var(--transition-speed);
        }

        .btn-primary:hover {
            background-color: #243ab6;
            transform: translateY(-1px);
        }

        /* 专业方向选择样式 */
        .field-selection {
            margin-bottom: 1.5rem;
        }

        .field-option {
            margin-bottom: 1rem;
        }

        .field-option input[type="radio"] {
            display: none;
        }

        .field-label {
            display: flex;
            align-items: center;
            padding: 1rem;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            background: white;
            height: 100%;
        }

        .field-label:hover {
            border-color: #007bff;
            box-shadow: 0 2px 8px rgba(0, 123, 255, 0.1);
            transform: translateY(-1px);
        }

        .field-option input[type="radio"]:checked + .field-label {
            border-color: #007bff;
            background: linear-gradient(135deg, #f8f9ff 0%, #e3f2fd 100%);
            box-shadow: 0 4px 12px rgba(0, 123, 255, 0.15);
        }

        .field-icon {
            font-size: 2rem;
            margin-right: 1rem;
            color: #007bff;
            min-width: 50px;
            text-align: center;
        }

        .field-info {
            flex: 1;
        }

        .field-info strong {
            font-size: 1.1rem;
            color: #2c3e50;
            display: block;
            margin-bottom: 0.25rem;
        }

        .field-info small {
            font-size: 0.85rem;
            line-height: 1.3;
            margin-bottom: 0.5rem;
        }

        .field-info .badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
        }

        .auto-recommend-option {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 1rem;
        }

        .auto-recommend-option .form-check-label {
            font-size: 0.95rem;
            color: #495057;
            cursor: pointer;
        }

        .form-check-input:checked {
            background-color: #007bff;
            border-color: #007bff;
        }

        .filters-section {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .filter-group {
            background-color: #F9FAFB;
            border: 1px solid var(--border-color);
            padding: 1rem;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }

        .filter-title {
            font-weight: 600;
            color: var(--secondary-color);
            margin-bottom: 0.75rem;
            font-size: 0.9rem;
        }

        .checkbox-group {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
        }

        .custom-checkbox {
            display: inline-flex;
            align-items: center;
            margin-right: 1rem;
        }

        .result-card {
            background-color: var(--card-background);
            border-left: 4px solid var(--primary-color);
            margin-bottom: 1rem;
            transition: all var(--transition-speed);
        }

        .result-card:hover {
            transform: translateX(4px);
        }

        .result-title {
            color: var(--primary-color);
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .result-meta {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 0.5rem;
            margin-bottom: 1rem;
            font-size: 0.9rem;
        }

        .meta-item {
            display: flex;
            align-items: center;
            color: var(--secondary-color);
        }

        .meta-item i {
            margin-right: 0.5rem;
            width: 16px;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 2rem;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            margin: 0 auto;
            border: 4px solid #f3f3f3;
            border-top: 4px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .export-section {
            background-color: #e9ecef;
            border-radius: 8px;
            padding: 1rem;
            margin-top: 1rem;
        }

        .export-title {
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .export-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .export-item {
            display: flex;
            align-items: center;
            margin-bottom: 0.25rem;
        }

        .export-item i {
            margin-right: 0.5rem;
            color: var(--primary-color);
        }

        #search-strategy-container {
            background-color: var(--card-background);
            border: 1px solid var(--border-color);
            padding: 20px;
            margin-top: 20px;
        }

        #search-strategy {
            background-color: #F9FAFB;
            color: var(--text-color);
            border: 1px solid var(--border-color);
            font-family: monospace;
            font-size: 14px;
            line-height: 1.5;
            resize: vertical;
        }

        #search-stats {
            margin: 20px 0;
        }

        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background-color: #5a6268;
        }

        .search-form {
            display: flex;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        #search-input {
            flex: 1;
            padding: 0.75rem;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            font-size: 1rem;
        }

        #search-btn {
            padding: 0.75rem 1.5rem;
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: all var(--transition-speed);
        }

        #search-btn:hover {
            background-color: #6D28D9;
            transform: translateY(-1px);
        }

        .toast-container {
            position: fixed;
            top: 80px;
            right: 20px;
            z-index: 1000;
        }

        .toast {
            padding: 1rem;
            margin-bottom: 0.5rem;
            border-radius: 8px;
            color: white;
            opacity: 0;
            transition: opacity 0.3s ease-in-out;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
            min-width: 250px;
            max-width: 400px;
        }

        .toast.show {
            opacity: 1;
        }

        .toast.success { background-color: #28a745; }
        .toast.error { background-color: #dc3545; }
        .toast.warning { background-color: #ffc107; color: #000; }
        .toast.info { background-color: #17a2b8; }

        .mode-container {
            transition: all 0.3s ease-in-out;
        }
        
        .analysis-chart {
            min-height: 400px;
            width: 100%;
            background: var(--card-background);
            border-radius: 8px;
            padding: 1rem;
            margin-top: 1rem;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            border: 1px solid var(--border-color);
        }
        
        .chart-container {
            background: var(--card-background);
            border-radius: 8px;
            padding: 20px;
            min-height: 400px;  /* 增加最小高度 */
            height: 400px;      /* 设置固定高度 */
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            position: relative;  /* 确保相对定位 */
            display: flex;       /* 添加flex布局 */
            flex-direction: column; /* 垂直排列 */
        }
        
        .no-data-message {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #666;
            font-size: 14px;
            text-align: center;
        }
        
        /* 调整图表卡片样式 */
        .chart-card {
            margin-bottom: 2rem;
            border: 1px solid var(--border-color);
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .chart-card .card-title {
            color: var(--primary-color);
            font-weight: 600;
            margin-bottom: 1rem;
            padding: 1rem;
            border-bottom: 1px solid var(--border-color);
        }
        
        .btn-check:checked + .btn-outline-light {
            background-color: rgba(255, 255, 255, 0.2);
        }

        /* 添加热点作者表格样式 */
        .table-responsive {
            margin-top: 1rem;
        }
        
        .table {
            font-size: 0.9rem;
            color: var(--text-color);
        }
        
        .table th {
            background-color: var(--primary-color);
            color: white;
            font-weight: 500;
        }
        
        .table td {
            vertical-align: middle;
            background-color: var(--card-background);
            border-color: var(--border-color);
        }
        
        .author-rank {
            font-weight: bold;
            color: var(--primary-color);
        }
        
        .author-years {
            font-size: 0.8rem;
            color: var(--secondary-color);
        }

        /* 添加搜索模式切换样式 */
        .search-mode-toggle {
            margin-bottom: 1rem;
        }
        
        .search-mode-toggle .btn-group {
            width: 100%;
            margin-bottom: 0.5rem;
            border-radius: 8px;
            overflow: hidden;
        }
        
        .search-mode-toggle .btn {
            flex: 1;
            text-align: center;
            padding: 0.75rem 1.5rem;
            border: 1px solid var(--primary-color);
            background-color: white;
            color: var(--primary-color);
            font-weight: 500;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .search-mode-toggle .btn:first-child {
            border-top-right-radius: 0;
            border-bottom-right-radius: 0;
        }

        .search-mode-toggle .btn:last-child {
            border-top-left-radius: 0;
            border-bottom-left-radius: 0;
        }
        
        .search-mode-toggle .btn:hover {
            background-color: rgba(139, 92, 246, 0.1);
            color: var(--primary-color);
            transform: translateY(-1px);
        }
        
        .search-mode-toggle .btn.active {
            background-color: var(--primary-color);
            color: white;
            box-shadow: 0 2px 4px rgba(139, 92, 246, 0.3);
        }

        .search-mode-toggle .btn.active:hover {
            background-color: var(--primary-color);
            box-shadow: 0 4px 8px rgba(139, 92, 246, 0.4);
            transform: translateY(-2px);
        }

        .search-mode-toggle .btn.active::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at center, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 70%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .search-mode-toggle .btn.active:hover::before {
            opacity: 1;
        }

        /* 添加鼠标指针样式 */
        .search-mode-toggle .btn {
            cursor: pointer;
        }

        .search-mode-toggle .btn.active {
            cursor: default;
        }

        /* 调整输入框样式 */
        #search-input {
            min-height: 60px;
            resize: vertical;
            transition: min-height 0.3s ease;
        }
        
        #search-input.paragraph-mode {
            min-height: 120px;
        }
        
        /* 段落模式的结果样式 */
        .sentence-result {
            background-color: var(--card-background);
            border: 1px solid var(--border-color);
            padding: 1rem;
            margin-bottom: 1.5rem;
            border-radius: 8px;
        }
        
        .sentence-text {
            font-size: 1.1rem;
            margin-bottom: 1rem;
            color: var(--text-color);
        }
        
        .papers-list {
            margin-top: 1rem;
        }
        
        .paper-item {
            padding: 0.75rem;
            border-bottom: 1px solid var(--border-color);
            border-color: var(--border-color);
        }
        
        .paper-item:last-child {
            border-bottom: none;
        }

        /* 添加页脚样式 */
        .footer {
            background: #ffffff;
            color: rgb(0, 0, 0);
            padding: 2rem 0;
            margin-top: 3rem;
            text-align: center;
            box-shadow: 0 -2px 4px rgba(0,0,0,0.1);
        }
        
        .footer p {
            margin: 0;
            font-size: 0.9rem;
            line-height: 1.6;
            opacity: 0.9;
        }
        
        .footer .developer-info {
            margin-top: 0.5rem;
            font-size: 0.85rem;
            opacity: 0.8;
        }
        
        .footer .wechat {
            font-weight: bold;
            color: #fff;
        }

        /* 导出按钮样式 */
        .export-btn {
            position: relative;
        }
        
        .export-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            width: 250px;
            background-color: #FFFFFF;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            z-index: 1000;
            display: none;
        }
        
        .export-dropdown.show {
            display: block;
        }
        
        .export-item {
            padding: 10px 15px;
            cursor: pointer;
            transition: background-color 0.2s;
            display: flex;
            align-items: center;
        }
        
        .export-item:hover {
            background-color: #F3F4F6;
        }
        
        .export-item i {
            margin-right: 8px;
            color: var(--primary-color);
        }

        .progress-container {
            background-color: var(--card-background);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 20px;
        }
        
        .progress-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .progress-percentage {
            font-weight: bold;
            color: var(--primary-color);
        }
        
        .progress-bar {
            background-color: var(--primary-color);
            transition: width 0.3s ease;
        }
        
        .progress-message {
            color: var(--text-color);
            font-size: 0.9rem;
        }
        
        .progress-log-container {
            background-color: #F9FAFB;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            padding: 15px;
        }
        
        .progress-log {
            font-family: monospace;
            font-size: 0.85rem;
            line-height: 1.5;
        }
        
        .log-entry {
            padding: 4px 8px;
            border-bottom: 1px solid var(--border-color);
            font-size: 0.9rem;
            line-height: 1.4;
        }
        
        .log-entry:last-child {
            border-bottom: none;
        }
        
        .log-time {
            color: var(--secondary-color);
            font-family: monospace;
            margin-right: 8px;
        }
        
        .log-message {
            color: var(--text-color);
        }
        
        .log-error {
            color: #ff4d4d;
        }

        .btn-outline-light:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }

        .alert-info {
            background-color: #EFF6FF;
            border-color: #BFDBFE;
            color: var(--text-color);
        }

        .form-text {
            color: var(--secondary-color);
        }

        .btn-outline-primary {
            color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .btn-outline-primary:hover {
            background-color: var(--primary-color);
            color: white;
        }

        .btn-check:checked + .btn-outline-primary {
            background-color: var(--primary-color);
            color: white;
        }

        /* 修改图表主题配色 */
        .visualMap {
            color: var(--text-color);
        }

        /* 修改ECharts图表的文字颜色 */
        .echarts-tooltip {
            color: var(--text-color);
        }

        .echarts-title {
            color: var(--text-color);
        }

        .echarts-legend {
            color: var(--text-color);
        }

        /* 修改导航栏样式 */
        .navbar-dark {
            background-color: #FFFFFF !important;
        }

        .navbar-dark .navbar-brand {
            color: var(--primary-color) !important;
        }

        /* 文献追踪模式样式 */
        #tracking-mode-container {
            display: none;
        }

        #search-mode-container {
            display: none;
        }

        /* 文献溯源模式样式 */
        #verification-mode-container {
            display: none;
        }

        #analysis-mode-container.active,
        #search-mode-container.active {
            display: block;
        }

        /* 文献溯源相关样式 */
        .verification-reference-card {
            transition: all 0.3s ease;
            border-left: 4px solid var(--primary-color);
        }

        .verification-reference-card.border-success {
            border-left-color: #198754;
        }

        .verification-reference-card.border-danger {
            border-left-color: #dc3545;
        }

        .verification-reference-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .citation-link {
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .citation-link:hover {
            transform: scale(1.1);
        }

        /* 验证状态徽章样式 */
        .badge.bg-success {
            background-color: #198754 !important;
        }

        .badge.bg-danger {
            background-color: #dc3545 !important;
        }

        .analysis-form {
            background: var(--card-background);
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .analysis-results {
            display: grid;
            grid-template-columns: repeat(2, 1fr);  /* 改为固定2列 */
            gap: 20px;
            margin-top: 20px;
        }

        .chart-container {
            background: var(--card-background);
            border-radius: 8px;
            padding: 20px;
            min-height: 400px;  /* 增加最小高度 */
            height: 400px;      /* 设置固定高度 */
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            position: relative;  /* 确保相对定位 */
            display: flex;       /* 添加flex布局 */
            flex-direction: column; /* 垂直排列 */
        }

        /* 添加图表标题样式 */
        .chart-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-color);
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid var(--border-color);
        }

        /* 调整热点作者表格容器 */
        .authors-container {
            grid-column: span 2;  /* 跨越两列 */
            background: var(--card-background);
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        /* 确保图表实例容器有正确的尺寸 */
        #heatmap-chart,
        #wordcloud-chart,
        #trend-chart {
            width: 100%;
            height: 100%;
            min-height: 300px;  /* 减小最小高度，留出标题空间 */
            flex-grow: 1;       /* 允许图表容器占据剩余空间 */
        }

        .hot-authors-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background: var(--card-background);
            border-radius: 8px;
            overflow: hidden;
        }

        .hot-authors-table th,
        .hot-authors-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        .hot-authors-table th {
            background: var(--primary-color);
            color: white;
        }

        .hot-authors-table tr:hover {
            background: var(--hover-color);
        }

        /* Toast样式 */
        .toast-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }

        .toast {
            padding: 12px 24px;
            border-radius: 4px;
            margin-bottom: 10px;
            color: white;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.3s ease;
        }

        .toast.show {
            opacity: 1;
            transform: translateX(0);
        }

        .toast.info {
            background: var(--primary-color);
        }

        .toast.error {
            background: #dc3545;
        }

        .toast.success {
            background: #28a745;
        }

        .toast.warning {
            background: #ffc107;
            color: #000;
        }

        /* 按钮loading状态样式 */
        .btn-loading {
            position: relative;
            pointer-events: none;
            opacity: 0.7;
        }

        .btn-loading .btn-spinner {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid transparent;
            border-top: 2px solid currentColor;
            border-radius: 50%;
            animation: btn-spin 1s linear infinite;
            margin-right: 8px;
        }

        @keyframes btn-spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .btn-loading::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.1);
            border-radius: inherit;
        }

        /* 禁用状态下的按钮样式 */
        .btn:disabled,
        .btn.disabled {
            opacity: 0.6;
            pointer-events: none;
        }

        /* 学术分析按钮特殊样式 */
        .analysis-btn {
            transition: all 0.3s ease;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
        }

        .analysis-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .analysis-btn .btn-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
        }

        .analysis-btn .btn-content small {
            font-size: 0.75rem;
            margin-top: 2px;
        }

        /* 分析工具卡片动画 */
        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .card.border-primary {
            animation: slideInUp 0.6s ease-out;
        }

        /* 响应式调整 */
        @media (max-width: 768px) {
            .analysis-btn {
                padding: 1rem !important;
                margin-bottom: 0.5rem;
            }
            
            .analysis-btn .btn-content .fw-bold {
                font-size: 0.9rem;
            }
        }

        /* 历史会话侧边栏样式 */
        .history-sidebar {
            position: fixed;
            left: 0;
            top: 0;
            width: 300px;
            height: 100vh;
            background: #f8f9fa;
            border-right: 1px solid #dee2e6;
            z-index: 1000;
            transform: translateX(-100%);
            transition: transform 0.3s ease;
            display: flex;
            flex-direction: column;
        }

        .history-sidebar.show {
            transform: translateX(0);
        }

        .sidebar-header {
            padding: 1rem;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: white;
            margin-top: 56px; /* 为导航栏留出空间 */
        }

        .sidebar-title {
            margin: 0;
            font-weight: 600;
            color: #495057;
        }

        .sidebar-actions {
            padding: 1rem;
            background: white;
        }

        .sidebar-search {
            padding: 1rem;
            border-bottom: 1px solid #dee2e6;
            background: white;
        }

        .sidebar-content {
            flex: 1;
            overflow-y: auto;
            padding: 0.5rem;
        }

        .sidebar-footer {
            padding: 1rem;
            border-top: 1px solid #dee2e6;
            background: white;
        }

        .sessions-list {
            max-height: 100%;
        }

        .session-item {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            margin-bottom: 0.5rem;
            padding: 0.75rem;
            cursor: pointer;
            transition: all 0.2s ease;
            position: relative;
        }

        .session-item::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 3px;
            background: transparent;
            border-radius: 8px 0 0 8px;
            transition: background 0.2s ease;
        }

        .session-item:hover {
            background: #f8f9fa;
            border-color: #007bff;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .session-item:hover::before {
            background: #007bff;
        }

        .session-item.active {
            background: #e3f2fd;
            border-color: #2196f3;
            box-shadow: 0 2px 8px rgba(33, 150, 243, 0.2);
        }

        .session-item.active::before {
            background: #2196f3;
        }

        .session-title {
            font-weight: 600;
            font-size: 0.9rem;
            color: #212529;
            margin-bottom: 0.25rem;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .session-meta {
            font-size: 0.75rem;
            color: #6c757d;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .session-actions {
            display: flex;
            gap: 0.25rem;
            margin-top: 0.5rem;
        }

        .session-actions .btn {
            font-size: 0.7rem;
            padding: 0.2rem 0.4rem;
        }

        /* 切换按钮 */
        .toggle-history-btn {
            position: fixed;
            left: 10px;
            top: 70px;
            z-index: 1001;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
        }

        .toggle-history-btn:hover {
            background: #0056b3;
            transform: scale(1.1);
        }

        /* 遮罩层 */
        .sidebar-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 999;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .sidebar-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        /* 当侧边栏显示时，主内容区域的样式调整 */
        .main-content-shift {
            transition: margin-left 0.3s ease;
        }

        @media (min-width: 992px) {
            .history-sidebar.show ~ .main-content-shift {
                margin-left: 300px;
            }
        }

        @media (max-width: 991px) {
            .history-sidebar {
                box-shadow: 2px 0 10px rgba(0,0,0,0.1);
            }
        }
    </style>
</head>
<body>
    <!-- 历史会话侧边栏 -->
    <div class="history-sidebar" id="historySidebar">
        <div class="sidebar-header">
            <h6 class="sidebar-title">
                <i class="fas fa-history me-2"></i>历史会话
            </h6>
            <button class="btn btn-sm btn-outline-secondary" onclick="toggleHistorySidebar()" title="关闭侧边栏">
                <i class="fas fa-times"></i>
            </button>
        </div>

        <!-- 新建检索按钮 -->
        <div class="sidebar-actions">
            <button class="btn btn-primary btn-sm w-100 mb-3" onclick="createNewSession()" title="开始新的文献检索">
                <i class="fas fa-plus me-2"></i>新建检索
            </button>
        </div>

        <div class="sidebar-search">
            <div class="input-group input-group-sm">
                <input type="text" class="form-control" id="sessionSearch" placeholder="搜索历史会话...">
                <button class="btn btn-outline-secondary" type="button" onclick="searchSessions()">
                    <i class="fas fa-search"></i>
                </button>
            </div>
        </div>

        <div class="sidebar-content">
            <div class="text-center text-muted py-2" style="font-size: 0.75rem; border-bottom: 1px solid #e9ecef;">
                <i class="fas fa-info-circle me-1"></i>点击会话卡片可切换显示内容
            </div>
            <div id="sessionsList" class="sessions-list">
                <div class="text-center text-muted py-3">
                    <i class="fas fa-clock me-1"></i>
                    <div>暂无历史会话</div>
                </div>
            </div>
        </div>

        <div class="sidebar-footer">
            <button class="btn btn-sm btn-outline-danger w-100" onclick="clearAllSessions()">
                <i class="fas fa-trash me-1"></i>清空历史
            </button>
        </div>
    </div>

    <!-- 侧边栏遮罩层 -->
    <div class="sidebar-overlay" id="sidebarOverlay" onclick="toggleHistorySidebar()"></div>

    <!-- 切换历史会话按钮 -->
    <button class="toggle-history-btn" onclick="toggleHistorySidebar()" title="历史会话">
        <i class="fas fa-history"></i>
    </button>

    <div class="container-fluid main-content-shift" id="mainContent">
        <!-- 导航栏 -->
        <nav class="navbar navbar-expand-lg">
            <div class="container-fluid">
                <a class="navbar-brand" href="/">
                    <!img src="/static/img/logo.png" alt="NNScholar Logo" height="30"!>
                    NNScholar
                </a>
                <div class="navbar-nav ms-auto">
                    <div class="btn-group me-3" role="group">
                        <button type="button" class="btn btn-primary active" data-mode="search">文献检索</button>
                        <button type="button" class="btn btn-primary" data-mode="tracking">文献追踪</button>
                        <button type="button" class="btn btn-primary" data-mode="verification">文献溯源</button>
                    </div>
                    <a href="/analysis" class="btn btn-outline-primary">
                        <i class="fas fa-microscope me-1"></i>深度分析工具箱
                    </a>
                </div>
            </div>
        </nav>

        <!-- 文献检索模式 -->
        <div id="search-mode-container" class="active">
            <div class="card">
                <div class="card-body">
                <h5 class="card-title">文献检索</h5>
                                    <div class="form-group">
                    <label for="search-input" class="form-label">检索内容</label>
                    <textarea class="form-control" id="search-input" rows="3" placeholder="请输入检索内容..."></textarea>
                        </div>

                        <!-- 专业方向选择 -->
                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-graduation-cap me-2"></i>专业方向选择
                            </label>
                            <div class="field-selection">
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="field-option">
                                            <input type="radio" id="field-medical" name="field" value="pubmed" checked>
                                            <label for="field-medical" class="field-label">
                                                <div class="field-icon">
                                                    <i class="fas fa-heartbeat"></i>
                                                </div>
                                                <div class="field-info">
                                                    <strong>医学生物</strong>
                                                    <small class="text-muted d-block">医学、生物学、药学、护理学等</small>
                                                    <span class="badge bg-success">PubMed</span>
                                                </div>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="field-option">
                                            <input type="radio" id="field-stem" name="field" value="arxiv">
                                            <label for="field-stem" class="field-label">
                                                <div class="field-icon">
                                                    <i class="fas fa-atom"></i>
                                                </div>
                                                <div class="field-info">
                                                    <strong>理工科学</strong>
                                                    <small class="text-muted d-block">物理、数学、计算机科学等</small>
                                                    <span class="badge bg-info">arXiv</span>
                                                </div>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="field-option">
                                            <input type="radio" id="field-interdisciplinary" name="field" value="semantic_scholar">
                                            <label for="field-interdisciplinary" class="field-label">
                                                <div class="field-icon">
                                                    <i class="fas fa-network-wired"></i>
                                                </div>
                                                <div class="field-info">
                                                    <strong>跨学科</strong>
                                                    <small class="text-muted d-block">综合性、交叉学科研究</small>
                                                    <span class="badge bg-warning">Semantic Scholar</span>
                                                </div>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <div class="row mt-3">
                                    <div class="col-12">
                                        <div class="auto-recommend-option">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="auto-recommend" checked>
                                                <label class="form-check-label" for="auto-recommend">
                                                    <i class="fas fa-magic me-2"></i>
                                                    <strong>智能推荐</strong> - 根据检索内容自动选择最适合的数据库
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                <div class="btn-group mb-3" role="group">
                    <button type="button" class="btn btn-primary active" data-mode="single">单句模式</button>
                    <button type="button" class="btn btn-primary" data-mode="paragraph">段落模式</button>
                        </div>

                <div class="filters-section">
                            <div class="filter-group">
                                <h6 class="filter-title">发表年份</h6>
                                <div class="d-flex gap-2">
                                    <input type="number" id="year-start" class="form-control form-control-sm" placeholder="起始年份">
                                    <span class="align-self-center">-</span>
                                    <input type="number" id="year-end" class="form-control form-control-sm" placeholder="结束年份">
                                </div>
                            </div>

                            <div class="filter-group">
                                <h6 class="filter-title">影响因子</h6>
                                <input type="number" id="min-if" class="form-control form-control-sm" placeholder="最低影响因子" step="0.1">
                            </div>

                            <div class="filter-group">
                                <h6 class="filter-title">JCR分区</h6>
                                <div class="checkbox-group">
                                    <label class="custom-checkbox">
                                        <input type="checkbox" name="jcr" value="Q1"> Q1
                                    </label>
                                    <label class="custom-checkbox">
                                        <input type="checkbox" name="jcr" value="Q2"> Q2
                                    </label>
                                    <label class="custom-checkbox">
                                        <input type="checkbox" name="jcr" value="Q3"> Q3
                                    </label>
                                    <label class="custom-checkbox">
                                        <input type="checkbox" name="jcr" value="Q4"> Q4
                                    </label>
                                </div>
                            </div>

                            <div class="filter-group">
                                <h6 class="filter-title">中科院分区</h6>
                                <div class="checkbox-group">
                                    <label class="custom-checkbox">
                                        <input type="checkbox" name="cas" value="1"> 1区
                                    </label>
                                    <label class="custom-checkbox">
                                        <input type="checkbox" name="cas" value="2"> 2区
                                    </label>
                                    <label class="custom-checkbox">
                                        <input type="checkbox" name="cas" value="3"> 3区
                                    </label>
                                    <label class="custom-checkbox">
                                        <input type="checkbox" name="cas" value="4"> 4区
                                    </label>
                                </div>
                            </div>

                            <div class="filter-group">
                                <h6 class="filter-title">显示数量</h6>
                                <select id="papers-limit" class="form-select form-select-sm">
                                    <option value="20">20篇</option>
                                    <option value="50" selected>50篇</option>
                                    <option value="100">100篇</option>
                                    <option value="200">200篇</option>
                                    <option value="500">500篇</option>
                                </select>
            </div>
        </div>

                <button id="search-btn" class="btn btn-primary w-100">
                    <i class="fas fa-magic me-2"></i>生成检索策略
                </button>
                </div>
                    </div>
                    </div>
                    
        <div id="search-strategy-container" style="display: none;">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h5 class="mb-0">检索策略</h5>
                <button id="execute-strategy-btn" class="btn btn-primary">
                    <i class="fas fa-search me-2"></i>执行检索策略
                    </button>
                            </div>
            <textarea id="search-strategy" class="form-control" style="min-height: 100px; font-family: monospace; white-space: pre; overflow-wrap: normal; overflow-x: scroll;" spellcheck="false"></textarea>
            <div class="form-text mt-2">
                <i class="fas fa-info-circle me-1"></i>
                您可以直接编辑检索策略，编辑完成后点击"执行检索策略"按钮进行搜索
                            </div>
                        </div>
                        
        <!-- 修改进度显示容器的结构 -->
        <div id="searchProgress" class="progress-container mt-4" style="display: none;">
            <div class="progress-header mb-3">
                <h6 class="progress-title mb-0">
                    <i class="fas fa-tasks me-2"></i>搜索进度
                </h6>
                <div class="progress-percentage">0%</div>
            </div>
                        
            <div class="progress mb-3" style="height: 6px;">
                <div class="progress-bar" role="progressbar" style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
            </div>

            <div class="progress-message mb-3">准备开始搜索...</div>
            
            <div class="progress-log-container">
                <div class="progress-log-header mb-2">
                    <strong><i class="fas fa-list-ul me-2"></i>详细日志</strong>
                </div>
                <div class="progress-log" style="max-height: 200px; overflow-y: auto;">
                    <!-- 日志条目将在这里动态添加 -->
                </div>
            </div>
        </div>

        <div id="search-stats" style="display: none;">
            <div class="alert alert-info">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <h6 class="m-0">检索统计</h6>
            <div class="export-btn">
                        <button id="stats-export-btn" class="btn btn-sm btn-outline-primary">
                    <i class="fas fa-file-export me-1"></i>导出
                </button>
                        <div id="stats-export-dropdown" class="export-dropdown">
                    <div class="export-item" data-type="initial_word">
                        <i class="fas fa-file-word"></i>初始检索报告文档
                    </div>
                    <div class="export-item" data-type="filtered_word">
                        <i class="fas fa-file-word"></i>筛选后检索报告文档
                    </div>
                    <div class="export-item" data-type="initial_excel">
                        <i class="fas fa-file-excel"></i>初始检索表格
                    </div>
                    <div class="export-item" data-type="filtered_excel">
                        <i class="fas fa-file-excel"></i>筛选后检索表格
                            </div>
                    </div>
                </div>
            </div>
        </div>
                <div class="d-flex flex-column gap-2">
                    <div class="d-flex justify-content-between align-items-center">
                        <span>
                            <i class="fas fa-search me-2"></i>
                            初始检索到相关文献：<strong id="total-count">0</strong> 篇
                        </span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center">
                        <span>
                            <i class="fas fa-filter me-2"></i>
                            符合筛选条件的文献：<strong id="filtered-count">0</strong> 篇
                        </span>
                    </div>
                    <div class="progress mt-2" style="height: 4px;">
                        <div class="progress-bar bg-primary" role="progressbar" 
                            style="width: 0%"
                            aria-valuenow="0" 
                            aria-valuemin="0" 
                            aria-valuemax="100">
                        </div>
                    </div>
                    <div class="mt-2 text-muted small">
                        <i class="fas fa-info-circle me-1"></i>
                        筛选条件包括：年份、影响因子、JCR分区、CAS分区
                    </div>
                </div>
            </div>
    </div>
    <!-- 文献追踪模式 -->
    <div id="tracking-mode-container" style="display: none;">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="fas fa-project-diagram me-2"></i>文献追踪网络图谱
                </h5>
                <p class="text-muted mb-4">
                    用一张动态知识图谱，5分钟把陌生领域变成熟悉战场
                </p>

                <div class="tracking-form">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="form-group">
                                <label for="tracking-input" class="form-label">输入文献信息</label>
                                <textarea class="form-control" id="tracking-input" rows="3" placeholder="请输入DOI...&#10;例如：10.1038/nature12373&#10"></textarea>
                                <div class="form-text">
                                    支持DOI，系统将基于它分析引用关系和相关文献
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>&nbsp;</label>
                                <button type="button" class="btn btn-primary w-100" id="generate-network-btn">
                                    <i class="fas fa-sitemap me-2"></i>生成网络图谱
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-3">
                        <div class="col-12">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle me-2"></i>功能特点</h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <ul class="mb-0">
                                            <li><strong>网络图谱生成：</strong>输入DOI，自动构建引用关系网络图</li>
                                            <li><strong>参考文献分析：</strong>展示种子文献引用的"前辈文献"（红色节点）</li>
                                            <li><strong>被引文献分析：</strong>展示引用种子文献的"后续文献"（蓝色节点）</li>
                                            <li><strong>智能布局：</strong>参考文献分布在左半圆，被引文献分布在右半圆</li>
                                        </ul>
                                    </div>
                                    <div class="col-md-6">
                                        <ul class="mb-0">
                                            <li><strong>相关度可视化：</strong>节点颜色深浅反映与种子文献的相关度（50%-100%）</li>
                                            <li><strong>影响因子映射：</strong>节点大小与影响因子成正比，直观显示期刊质量</li>
                                            <li><strong>距离分层：</strong>相关度高的文献距离中心更近，形成清晰的层次结构</li>
                                            <li><strong>交互探索：</strong>点击节点查看详细信息，自动定位到侧边栏对应文献</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 网络图谱显示区域 -->
                <div id="network-display-area" style="display: none;">
                    <div class="card mt-4">
                        <div class="card-body p-0">
                            <div id="citation-network-container" style="height: 80vh; width: 100%; position: relative;">
                                <!-- 网络图谱将在这里显示 -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 加载状态 -->
                <div id="tracking-loading" style="display: none;">
                    <div class="card mt-4">
                        <div class="card-body text-center py-5">
                            <div class="spinner-border text-primary mb-3" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <h5>正在生成文献网络图谱...</h5>
                            <p class="text-muted mb-0">这可能需要几分钟时间，请耐心等待</p>
                            <div class="progress mt-3" style="height: 6px;">
                                <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%" id="tracking-progress-bar"></div>
                            </div>
                            <div id="tracking-status-text" class="mt-2 text-muted">正在获取文献元数据...</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 文献溯源模式 -->
    <div id="verification-mode-container" style="display: none;">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="fas fa-shield-alt me-2"></i>文献溯源
                </h5>
                <p class="text-muted mb-4">
                    检测学术内容中的虚假文献，确保引用的真实性和可靠性
                </p>

                <div class="verification-form">
                    <div class="form-group mb-4">
                        <label for="verification-input" class="form-label">学术内容 <span class="text-danger">*</span></label>
                        <textarea
                            class="form-control"
                            id="verification-input"
                            rows="6"
                            placeholder="请输入包含参考文献的学术内容，例如：&#10;&#10;慢性肾病患者常伴有肌肉减少症，这会显著影响患者的生活质量和预后。研究表明，肌肉减少症在慢性肾病患者中的患病率高达30-60%。营养不良和炎症反应是导致肌肉减少症的主要机制。&#10;&#10;系统将自动为学术观点匹配参考文献并验证其中的参考文献真实性..."
                        ></textarea>
                        <div class="form-text">
                            💡 提示：系统将自动为学术观点匹配参考文献，并验证其真实性
                        </div>
                    </div>

                    <div class="d-flex justify-content-between align-items-center">
                        <div class="text-muted">
                            <small>基于多个数据库验证文献真实性</small>
                        </div>
                        <button type="button" class="btn btn-primary" id="start-verification-btn">
                            <i class="fas fa-search me-2"></i>开始匹配
                        </button>
                    </div>

                    <!-- 进度显示 -->
                    <div id="verification-progress" class="mt-4" style="display: none;">
                        <div class="card bg-light">
                            <div class="card-body">
                                <div class="d-flex align-items-center mb-3">
                                    <div class="spinner-border spinner-border-sm text-primary me-3" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                    <div>
                                        <div class="fw-medium">正在匹配参考文献...</div>
                                        <div id="verification-progress-message" class="text-muted small">正在分析文本内容...</div>
                                    </div>
                                </div>
                                <div class="progress">
                                    <div id="verification-progress-bar" class="progress-bar" role="progressbar" style="width: 0%"></div>
                                </div>
                                <div class="text-end mt-1">
                                    <small class="text-muted"><span id="verification-progress-percent">0%</span> 完成</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 结果显示区域 -->
                    <div id="verification-results" class="mt-4" style="display: none;">
                        <!-- 动态加载的结果内容 -->
                    </div>

                    <!-- 错误显示 -->
                    <div id="verification-error" class="mt-4" style="display: none;">
                        <div class="alert alert-danger">
                            <h6><i class="fas fa-exclamation-triangle me-2"></i>匹配失败</h6>
                            <div id="verification-error-message"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

        <div id="results-container">
            <!-- 搜索结果将在这里动态加载 -->
        </div>

    <div id="loading" style="display: none;">
        <div class="spinner"></div>
        <p>正在加载...</p>
        </div>
        
        <!-- 添加页脚 -->
        <footer class="footer mt-5">
            <div class="container">
                <p>NNScholar - 医学文献智能检索与分析系统</p>
            <p class="developer-info">Version 3.5</p>
            </div>
        </footer>


    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.socket.io/4.6.0/socket.io.min.js"></script>
    <script src="/static/js/session.js"></script>
    <script>
        // 显示搜索结果的通用函数（全局作用域）
        function displaySearchResults(papers, metadata = {}) {
            const resultsContainer = document.getElementById('results-container');
            resultsContainer.innerHTML = '';

            // 显示统计信息
            document.getElementById('search-stats').style.display = 'block';
            document.getElementById('total-count').textContent = metadata.totalCount || papers.length;
            document.getElementById('filtered-count').textContent = metadata.filteredCount || papers.length;

            if (papers && papers.length > 0) {
                papers.forEach(paper => {
                    const journalInfo = paper.journal_info || {};
                    // 段落模式可能有来源句子信息
                    const sourceInfo = paper.source_sentence ?
                        `<div class="text-muted small mb-2">
                            <i class="fas fa-quote-left me-1"></i>
                            来源句子：${paper.source_sentence}
                        </div>` : '';

                    const resultHtml = `
                        <div class="card mb-3">
                            <div class="card-body">
                                ${sourceInfo}
                                <h5 class="card-title">
                                    <a href="${paper.url || '#'}" target="_blank" class="text-decoration-none">
                                        ${paper.title}
                                    </a>
                                </h5>
                                <div class="d-flex flex-wrap gap-3 mb-2">
                                    <span class="text-muted">
                                        <i class="fas fa-users me-1"></i>
                                        ${paper.authors ? paper.authors.join(', ') : 'N/A'}
                                    </span>
                                    <span class="text-muted">
                                        <i class="fas fa-book me-1"></i>
                                        ${journalInfo.title || paper.journal || 'N/A'}
                                    </span>
                                    <span class="text-muted">
                                        <i class="fas fa-calendar me-1"></i>
                                        ${paper.pub_year || 'N/A'}
                                    </span>
                                </div>
                                <div class="d-flex flex-wrap gap-3 mb-3">
                                    <span class="badge bg-primary">
                                        IF: ${journalInfo.impact_factor || paper.impact_factor || 'N/A'}
                                    </span>
                                    <span class="badge bg-info">
                                        JCR: ${journalInfo.jcr_quartile || paper.jcr_quartile || 'N/A'}
                                    </span>
                                    <span class="badge bg-success">
                                        CAS: ${journalInfo.cas_quartile || paper.cas_quartile || 'N/A'}
                                    </span>
                                    <span class="badge bg-warning text-dark">
                                        相关度: ${paper.relevance_score ? Math.round(paper.relevance_score * 100) : 'N/A'}%
                                    </span>
                                </div>
                                <p class="card-text text-muted">
                                    ${paper.abstract || 'No abstract available'}
                                </p>
                            </div>
                        </div>
                    `;
                    resultsContainer.insertAdjacentHTML('beforeend', resultHtml);
                });

                // 添加学术分析功能区域
                const actionButtonsHtml = `
                    <div class="card mt-3 border-primary" style="background: linear-gradient(135deg, #f8f9ff 0%, #ffffff 100%); border-width: 2px;">
                        <div class="card-header bg-primary text-white py-3">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-brain me-2"></i>智能学术分析工具
                            </h5>
                            <small class="opacity-75">基于 ${papers.length} 篇文献的AI深度分析</small>
                        </div>
                        <div class="card-body py-4">
                            <p class="card-text text-secondary mb-4">
                                <i class="fas fa-lightbulb text-warning me-2"></i>
                                使用AI技术为您生成专业的学术建议，让研究更高效！点击按钮后将在新页面展示详细结果。
                            </p>
                            <div class="row g-3">
                                <div class="col-md-4">
                                    <button class="btn btn-primary w-100 py-3 analysis-btn" onclick="generateReviewTopicSuggestions()">
                                        <i class="fas fa-file-alt me-2"></i>
                                        <div class="btn-content">
                                            <div class="fw-bold">📝 综述选题建议</div>
                                            <small class="d-block opacity-75">智能推荐综述主题</small>
                                        </div>
                                    </button>
                                </div>
                                <div class="col-md-4">
                                    <button class="btn btn-success w-100 py-3 analysis-btn" onclick="generateResearchTopicSuggestions()">
                                        <i class="fas fa-microscope me-2"></i>
                                        <div class="btn-content">
                                            <div class="fw-bold">🔬 论著选题建议</div>
                                            <small class="d-block opacity-75">发现研究空白点</small>
                                        </div>
                                    </button>
                                </div>

                            </div>
                            <div class="mt-3 text-center">
                                <small class="text-muted">
                                    <i class="fas fa-info-circle me-1"></i>
                                    分析结果将在新页面显示，方便您查看和保存
                                </small>
                            </div>
                        </div>
                    </div>
                `;
                resultsContainer.insertAdjacentHTML('afterbegin', actionButtonsHtml);
            } else {
                resultsContainer.innerHTML = `
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        未找到符合条件的文献
                    </div>
                `;
            }

            // 滚动到结果区域
            resultsContainer.scrollIntoView({ behavior: 'smooth' });
        }

            // Toast提示函数
            function showToast(message, type = 'info') {
                const toastContainer = document.querySelector('.toast-container') || (() => {
                    const container = document.createElement('div');
                    container.className = 'toast-container';
                    document.body.appendChild(container);
                    return container;
                })();

                const toast = document.createElement('div');
                toast.className = `toast ${type}`;
                toast.textContent = message;

                toastContainer.appendChild(toast);
                setTimeout(() => {
                    toast.classList.add('show');
                }, 100);

                setTimeout(() => {
                    toast.classList.remove('show');
                    setTimeout(() => {
                        toastContainer.removeChild(toast);
                    }, 300);
                }, 3000);
            }

            // 显示加载状态
            function showLoading(message = '正在加载...') {
            const loading = document.getElementById('loading');
                if (loading) {
                    loading.querySelector('p').textContent = message;
                    loading.style.display = 'block';
                }
            }

            // 隐藏加载状态
            function hideLoading() {
                const loading = document.getElementById('loading');
                if (loading) {
                    loading.style.display = 'none';
                }
            }

    // 统一的按钮状态管理工具函数
    const ButtonStateManager = {
        // 保存按钮原始状态的Map
        originalStates: new Map(),
        
        /**
         * 设置按钮为loading状态
         * @param {HTMLElement|string} button - 按钮元素或选择器
         * @param {string} loadingText - loading时显示的文本
         * @param {string} spinnerType - spinner类型 ('default' | 'inline')
         */
        setLoading: function(button, loadingText = '处理中...', spinnerType = 'inline') {
            const btnElement = typeof button === 'string' ? document.querySelector(button) : button;
            if (!btnElement) return;
            
            // 保存原始状态
            if (!this.originalStates.has(btnElement)) {
                this.originalStates.set(btnElement, {
                    innerHTML: btnElement.innerHTML,
                    disabled: btnElement.disabled,
                    className: btnElement.className,
                    style: btnElement.style.cssText
                });
            }
            
            // 设置loading状态
            btnElement.disabled = true;
            btnElement.classList.add('btn-loading');
            
            // 添加spinner和文本
            if (spinnerType === 'inline') {
                btnElement.innerHTML = `<span class="btn-spinner"></span>${loadingText}`;
            } else {
                btnElement.innerHTML = loadingText;
            }
        },
        
        /**
         * 恢复按钮原始状态
         * @param {HTMLElement|string} button - 按钮元素或选择器
         */
        restore: function(button) {
            const btnElement = typeof button === 'string' ? document.querySelector(button) : button;
            if (!btnElement || !this.originalStates.has(btnElement)) return;
            
            const originalState = this.originalStates.get(btnElement);
            
            // 恢复原始状态
            btnElement.innerHTML = originalState.innerHTML;
            btnElement.disabled = originalState.disabled;
            btnElement.className = originalState.className;
            btnElement.style.cssText = originalState.style;
            
            // 清除保存的状态
            this.originalStates.delete(btnElement);
        },
        
        /**
         * 检查按钮是否在loading状态
         * @param {HTMLElement|string} button - 按钮元素或选择器
         * @returns {boolean}
         */
        isLoading: function(button) {
            const btnElement = typeof button === 'string' ? document.querySelector(button) : button;
            return btnElement && btnElement.classList.contains('btn-loading');
        },
        
        /**
         * 设置按钮为成功状态（短暂显示后恢复）
         * @param {HTMLElement|string} button - 按钮元素或选择器
         * @param {string} successText - 成功时显示的文本
         * @param {number} duration - 显示时长（毫秒）
         */
        setSuccess: function(button, successText = '完成', duration = 2000) {
            const btnElement = typeof button === 'string' ? document.querySelector(button) : button;
            if (!btnElement) return;
            
            const wasLoading = this.isLoading(btnElement);
            
            // 如果在loading状态，先清除loading
            if (wasLoading) {
                btnElement.classList.remove('btn-loading');
                btnElement.disabled = false;
            }
            
            // 临时显示成功状态
            const originalContent = btnElement.innerHTML;
            btnElement.innerHTML = `<i class="fas fa-check me-2"></i>${successText}`;
            btnElement.classList.add('btn-success');
            
            // 延迟恢复
            setTimeout(() => {
                if (wasLoading && this.originalStates.has(btnElement)) {
                    // 如果之前是loading状态，恢复到原始状态
                    this.restore(btnElement);
                } else {
                    // 否则只恢复内容
                    btnElement.innerHTML = originalContent;
                    btnElement.classList.remove('btn-success');
                }
            }, duration);
        },
        
        /**
         * 设置按钮为错误状态（短暂显示后恢复）
         * @param {HTMLElement|string} button - 按钮元素或选择器
         * @param {string} errorText - 错误时显示的文本
         * @param {number} duration - 显示时长（毫秒）
         */
        setError: function(button, errorText = '失败', duration = 3000) {
            const btnElement = typeof button === 'string' ? document.querySelector(button) : button;
            if (!btnElement) return;
            
            const wasLoading = this.isLoading(btnElement);
            
            // 如果在loading状态，先清除loading
            if (wasLoading) {
                btnElement.classList.remove('btn-loading');
                btnElement.disabled = false;
            }
            
            // 临时显示错误状态
            const originalContent = btnElement.innerHTML;
            btnElement.innerHTML = `<i class="fas fa-times me-2"></i>${errorText}`;
            btnElement.classList.add('btn-danger');
            
            // 延迟恢复
            setTimeout(() => {
                if (wasLoading && this.originalStates.has(btnElement)) {
                    // 如果之前是loading状态，恢复到原始状态
                    this.restore(btnElement);
                } else {
                    // 否则只恢复内容
                    btnElement.innerHTML = originalContent;
                    btnElement.classList.remove('btn-danger');
                }
            }, duration);
        }
    };

    // 等待页面加载完成
    document.addEventListener('DOMContentLoaded', function() {
        // 生成一个随机的会话ID
        const sessionId = 'session_' + Math.random().toString(36).substr(2, 9);
        
        // 初始化currentSessionId
        if (!currentSessionId) {
            currentSessionId = sessionId;
        }
        
        // 初始化Socket.IO连接
        const socket = io({
            transports: ['websocket'],
            reconnection: true,
            reconnectionAttempts: 5,
            reconnectionDelay: 1000,
            timeout: 20000,
            query: { sessionId: sessionId }
        });

        // 添加连接事件处理
        socket.on('connect', () => {
            console.log('Socket.IO连接成功，会话ID:', sessionId);
            showToast('服务器连接成功', 'success');
            
            // 如果当前在分析模式，重新初始化分析相关的状态
            const analysisContainer = document.getElementById('analysis-mode-container');
            if (analysisContainer.classList.contains('active')) {
                const analysisResults = document.querySelector('.analysis-results');
                if (analysisResults) {
                    analysisResults.style.display = 'none';
                }
            }
        });

        socket.on('connect_error', (error) => {
            console.error('Socket.IO连接错误:', error);
            showToast('连接服务器失败，正在重试...', 'error');
        });

        socket.on('disconnect', (reason) => {
            console.log('Socket.IO断开连接:', reason);
            showToast('服务器连接断开，正在重连...', 'warning');
            if (reason === 'io server disconnect') {
                socket.connect();
            }
            
            // 如果当前在分析模式，显示断开连接提示
            const analysisContainer = document.getElementById('analysis-mode-container');
            if (analysisContainer.classList.contains('active')) {
                showToast('服务器连接断开，分析功能可能受影响', 'warning');
            }
        });

        socket.on('reconnect', (attemptNumber) => {
            console.log('Socket.IO重连成功，尝试次数:', attemptNumber);
            showToast('服务器重连成功', 'success');
        });

        socket.on('reconnect_error', (error) => {
            console.error('Socket.IO重连失败:', error);
            showToast('服务器重连失败，请刷新页面', 'error');
        });

        // 监听进度更新事件
        socket.on('search_progress', function(data) {
            console.log('收到搜索进度更新:', data);
            const progressContainer = document.getElementById('searchProgress');
            const progressBar = progressContainer.querySelector('.progress-bar');
            const progressPercentage = progressContainer.querySelector('.progress-percentage');
            const progressMessage = progressContainer.querySelector('.progress-message');
            const progressLog = progressContainer.querySelector('.progress-log');

            // 显示进度容器
            progressContainer.style.display = 'block';

            // 更新进度条和百分比
            const percentage = Math.min(100, Math.max(0, data.percentage || 0));
            progressBar.style.width = `${percentage}%`;
            progressBar.setAttribute('aria-valuenow', percentage);
            progressPercentage.textContent = `${Math.round(percentage)}%`;

            // 更新进度消息
            if (data.message) {
                progressMessage.textContent = data.message;
            }

            // 添加日志条目
            const logEntry = document.createElement('div');
            logEntry.className = 'log-entry';
            const time = new Date().toLocaleTimeString();
            
            if (data.stage === 'complete') {
                // 检索完成
                logEntry.innerHTML = `
                    <span class="log-time">[${time}]</span>
                    <span class="log-message" style="color: #28a745;">✓ ${data.message || '检索完成'}</span>
                `;
                // 保存导出文件路径
                if (data.files) {
                    window.currentExportFiles = data.files;
                    console.log('已保存导出文件路径:', data.files);
                }
            } else if (data.stage === 'error') {
                // 发生错误
                logEntry.innerHTML = `
                    <span class="log-time">[${time}]</span>
                    <span class="log-message log-error">✗ ${data.message || '发生错误'}</span>
                `;
            } else {
                // 常规进度更新
                logEntry.innerHTML = `
                    <span class="log-time">[${time}]</span>
                    <span class="log-message">${data.message || '正在处理...'}</span>
                `;
            }
            
            progressLog.appendChild(logEntry);
            progressLog.scrollTop = progressLog.scrollHeight;
        });

        // 监听错误事件
        socket.on('search_error', function(data) {
            console.error('收到搜索错误:', data);
            const progressLog = document.querySelector('.progress-log');
            const logEntry = document.createElement('div');
            logEntry.className = 'log-entry';
            const time = new Date().toLocaleTimeString();
            logEntry.innerHTML = `
                <span class="log-time">[${time}]</span>
                <span class="log-message log-error">✗ ${data.error || '未知错误'}</span>
            `;
            progressLog.appendChild(logEntry);
            progressLog.scrollTop = progressLog.scrollHeight;
            showToast(data.error || '搜索出错', 'error');
        });

        // 创建一个通用的请求头
        const getHeaders = () => ({
            'Content-Type': 'application/json',
            'sid': currentSessionId || sessionId  // 优先使用currentSessionId，如果没有则使用sessionId
        });

        // 初始化导航栏模式切换
        const navModeButtons = document.querySelectorAll('.navbar-nav [data-mode]');
        const searchContainer = document.getElementById('search-mode-container');
        const trackingContainer = document.getElementById('tracking-mode-container');
        const verificationContainer = document.getElementById('verification-mode-container');
        const resultsContainer = document.getElementById('results-container');
        const searchStrategyContainer = document.getElementById('search-strategy-container');
        const searchProgressContainer = document.getElementById('searchProgress');
        const searchStatsContainer = document.getElementById('search-stats');

        // 设置初始状态
        searchContainer.classList.add('active');
        if (trackingContainer) trackingContainer.classList.remove('active');
        if (verificationContainer) verificationContainer.style.display = 'none';

        // 导航栏模式切换处理
        navModeButtons.forEach(button => {
            button.addEventListener('click', function() {
                const mode = this.dataset.mode;
                
                // 添加切换动画
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = 'scale(1)';
                }, 150);
                
                navModeButtons.forEach(btn => btn.classList.remove('active'));
                this.classList.add('active');

                if (mode === 'search') {
                    // 切换到检索模式
                    searchContainer.classList.add('active');
                    searchContainer.style.display = 'block';
                    if (trackingContainer) {
                        trackingContainer.classList.remove('active');
                        trackingContainer.style.display = 'none';
                    }
                    if (verificationContainer) {
                        verificationContainer.style.display = 'none';
                    }

                    // 恢复检索模式相关容器的显示
                    resultsContainer.style.display = 'block';
                    if (document.getElementById('search-strategy').value.trim()) {
                        searchStrategyContainer.style.display = 'block';
                    }

                    // 显示切换提示
                    showToast('已切换到文献检索模式', 'info');
                } else if (mode === 'tracking') {
                    // 切换到文献追踪模式
                    searchContainer.classList.remove('active');
                    searchContainer.style.display = 'none';
                    if (trackingContainer) {
                        trackingContainer.classList.add('active');
                        trackingContainer.style.display = 'block';
                    }
                    if (verificationContainer) {
                        verificationContainer.style.display = 'none';
                    }

                    // 隐藏检索结果容器
                    resultsContainer.style.display = 'none';

                    // 显示切换提示
                    showToast('已切换到文献追踪模式', 'info');
                } else if (mode === 'verification') {
                    // 切换到文献溯源模式
                    searchContainer.classList.remove('active');
                    searchContainer.style.display = 'none';
                    if (trackingContainer) {
                        trackingContainer.classList.remove('active');
                        trackingContainer.style.display = 'none';
                    }
                    if (verificationContainer) {
                        verificationContainer.style.display = 'block';
                    }

                    // 隐藏检索结果容器
                    resultsContainer.style.display = 'none';

                    // 显示切换提示
                    showToast('已切换到文献溯源模式', 'info');
                }
            });
        });

        // 搜索模式切换（单句/段落）
        const searchModeButtons = document.querySelectorAll('.btn-group [data-mode]');
        let currentSearchMode = 'single';

        searchModeButtons.forEach(button => {
            button.addEventListener('click', function() {
                const mode = this.dataset.mode;
                
                // 添加切换动画
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = 'scale(1)';
                }, 150);
                
                searchModeButtons.forEach(btn => btn.classList.remove('active'));
                this.classList.add('active');
                currentSearchMode = mode;
                    
                // 调整输入框高度
                const searchInput = document.getElementById('search-input');
                if (mode === 'paragraph') {
                    searchInput.classList.add('paragraph-mode');
                    showToast('已切换到段落模式', 'info');
                } else {
                    searchInput.classList.remove('paragraph-mode');
                    showToast('已切换到单句模式', 'info');
                }
            });
        });

        // 段落模式执行多个策略的函数

        // 段落模式执行多个策略的函数
        async function executeMultipleParagraphStrategies(filters) {
            const strategyTextarea = document.getElementById('search-strategy');
            const strategyText = strategyTextarea.value.trim();
            
            if (!strategyText) {
                showToast('没有可执行的检索策略', 'error');
                return;
            }
            
            // 解析段落模式的策略文本，提取各个句子的策略
            const strategies = [];
            const lines = strategyText.split('\n');
            let currentSentence = '';
            let currentStrategy = '';
            
            for (let line of lines) {
                line = line.trim();
                if (line.startsWith('# 句子')) {
                    // 保存上一个策略
                    if (currentStrategy) {
                        strategies.push({
                            sentence: currentSentence,
                            strategy: currentStrategy
                        });
                    }
                    currentSentence = line;
                    currentStrategy = '';
                } else if (line && !line.startsWith('#')) {
                    currentStrategy = line;
                }
            }
            
            // 保存最后一个策略
            if (currentStrategy) {
                strategies.push({
                    sentence: currentSentence,
                    strategy: currentStrategy
                });
            }
            
            if (strategies.length === 0) {
                showToast('未找到有效的检索策略', 'error');
                return;
            }
            
            console.log('解析出的策略:', strategies);
            
            // 顺序执行每个策略并合并结果
            const allPapers = [];
            const executeBtn = document.getElementById('execute-strategy-btn');
            
            try {
                ButtonStateManager.setLoading(executeBtn, '执行中...');
                
                // 显示进度容器
                const progressContainer = document.getElementById('searchProgress');
                const progressLog = progressContainer.querySelector('.progress-log');
                progressLog.innerHTML = '';
                progressContainer.style.display = 'block';
                
                // 添加开始日志
                const startEntry = document.createElement('div');
                startEntry.className = 'log-entry';
                const startTime = new Date().toLocaleTimeString();
                startEntry.innerHTML = `
                    <span class="log-time">[${startTime}]</span>
                    <span class="log-message">🚀 开始段落模式检索，共 ${strategies.length} 个句子</span>
                `;
                progressLog.appendChild(startEntry);
                
                for (let i = 0; i < strategies.length; i++) {
                    const { sentence, strategy } = strategies[i];
                    
                    // 更新按钮状态
                    ButtonStateManager.setLoading(executeBtn, `执行策略 ${i + 1}/${strategies.length}...`);
                    
                    // 添加策略执行日志
                    const strategyEntry = document.createElement('div');
                    strategyEntry.className = 'log-entry';
                    const strategyTime = new Date().toLocaleTimeString();
                    strategyEntry.innerHTML = `
                        <span class="log-time">[${strategyTime}]</span>
                        <span class="log-message">📝 执行策略 ${i + 1}：${sentence.substring(0, 30)}...</span>
                    `;
                    progressLog.appendChild(strategyEntry);
                    
                    console.log(`执行策略 ${i + 1}/${strategies.length}: ${strategy}`);
                    
                    const response = await fetch('/api/search', {
                        method: 'POST',
                        headers: getHeaders(),
                        body: JSON.stringify({
                            query: strategy,
                            mode: 'strategy',
                            filters: filters
                        })
                    });
                    
                    if (response.ok) {
                        const data = await response.json();
                        if (data.success && data.data) {
                            // 保存session_id（从第一次搜索响应中获取）
                            if (data.session_id && !currentSessionId) {
                                currentSessionId = data.session_id;
                                console.log('段落模式设置session_id:', currentSessionId);
                            }
                            
                            // 为每篇文献添加来源句子信息
                            data.data.forEach(paper => {
                                paper.source_sentence = sentence;
                                paper.source_strategy = strategy;
                            });
                            allPapers.push(...data.data);
                            
                            // 添加成功日志
                            const successEntry = document.createElement('div');
                            successEntry.className = 'log-entry';
                            const successTime = new Date().toLocaleTimeString();
                            successEntry.innerHTML = `
                                <span class="log-time">[${successTime}]</span>
                                <span class="log-message log-success">✅ 策略 ${i + 1} 完成，获得 ${data.data.length} 篇文献</span>
                            `;
                            progressLog.appendChild(successEntry);
                        } else {
                            // 添加失败日志
                            const errorEntry = document.createElement('div');
                            errorEntry.className = 'log-entry';
                            const errorTime = new Date().toLocaleTimeString();
                            errorEntry.innerHTML = `
                                <span class="log-time">[${errorTime}]</span>
                                <span class="log-message log-error">❌ 策略 ${i + 1} 失败：${data.error || '未知错误'}</span>
                            `;
                            progressLog.appendChild(errorEntry);
                        }
                    } else {
                        console.error(`策略 ${i + 1} 执行失败:`, await response.text());
                        
                        // 添加失败日志
                        const errorEntry = document.createElement('div');
                        errorEntry.className = 'log-entry';
                        const errorTime = new Date().toLocaleTimeString();
                        errorEntry.innerHTML = `
                            <span class="log-time">[${errorTime}]</span>
                            <span class="log-message log-error">❌ 策略 ${i + 1} 请求失败：${response.status}</span>
                        `;
                        progressLog.appendChild(errorEntry);
                    }
                }
                
                // 去重（基于PMID）
                const uniquePapers = [];
                const seenPmids = new Set();
                for (const paper of allPapers) {
                    if (!seenPmids.has(paper.pmid)) {
                        seenPmids.add(paper.pmid);
                        uniquePapers.push(paper);
                    }
                }
                
                console.log(`段落模式检索完成: ${strategies.length}个策略，${allPapers.length}篇文献，去重后${uniquePapers.length}篇`);
                
                // 存储合并后的结果到缓存，以便学术分析功能使用
                if (currentSessionId && uniquePapers.length > 0) {
                    try {
                        const storeResponse = await fetch('/api/store_paragraph_results', {
                            method: 'POST',
                            headers: getHeaders(),
                            body: JSON.stringify({
                                session_id: currentSessionId,
                                papers: uniquePapers,
                                query: `段落模式 (${strategies.length}个策略)`,
                                strategies: strategies.map(s => s.sentence).join('; ')
                            })
                        });
                        
                        if (storeResponse.ok) {
                            console.log('段落模式结果已存储到缓存，可用于学术分析');
                        } else {
                            console.warn('段落模式结果存储失败，但不影响显示');
                        }
                    } catch (error) {
                        console.warn('段落模式结果存储出错:', error);
                    }
                }
                
                // 添加最终完成日志
                const completeEntry = document.createElement('div');
                completeEntry.className = 'log-entry';
                const completeTime = new Date().toLocaleTimeString();
                completeEntry.innerHTML = `
                    <span class="log-time">[${completeTime}]</span>
                    <span class="log-message log-success">🎉 段落模式检索完成！共 ${strategies.length} 个策略，获得 ${allPapers.length} 篇文献，去重后 ${uniquePapers.length} 篇</span>
                `;
                progressLog.appendChild(completeEntry);
                
                // 显示结果
                displaySearchResults(uniquePapers, {
                    searchStrategy: `段落模式 (${strategies.length}个策略)`,
                    totalCount: uniquePapers.length,
                    filteredCount: uniquePapers.length
                });
                
                ButtonStateManager.setSuccess(executeBtn, '执行完成');
                showToast(`段落模式检索完成，共获得 ${uniquePapers.length} 篇文献`, 'success');
                
            } catch (error) {
                console.error('段落模式执行失败:', error);
                ButtonStateManager.setError(executeBtn, '执行失败');
                showToast('段落模式执行失败: ' + error.message, 'error');
            }
        }

        // 专业方向选择事件处理
        const fieldRadios = document.querySelectorAll('input[name="field"]');
        fieldRadios.forEach(radio => {
            radio.addEventListener('change', function() {
                const selectedField = this.value;
                const fieldInfo = {
                    'pubmed': {
                        name: 'PubMed',
                        description: '适合医学、生物学、药学等生命科学领域研究',
                        examples: '如：癌症治疗、基因编辑、药物研发等'
                    },
                    'arxiv': {
                        name: 'arXiv',
                        description: '适合物理、数学、计算机科学等理工科领域',
                        examples: '如：机器学习、量子计算、数学建模等'
                    },
                    'semantic_scholar': {
                        name: 'Semantic Scholar',
                        description: '适合跨学科研究和综合性学术研究',
                        examples: '如：人工智能应用、交叉学科研究等'
                    }
                };

                const info = fieldInfo[selectedField];
                if (info) {
                    showToast(`已选择 ${info.name} - ${info.description}`, 'info');
                }
            });
        });

        // 智能推荐开关事件处理
        const autoRecommendSwitch = document.getElementById('auto-recommend');
        if (autoRecommendSwitch) {
            autoRecommendSwitch.addEventListener('change', function() {
                const fieldRadios = document.querySelectorAll('input[name="field"]');
                fieldRadios.forEach(radio => {
                    radio.disabled = this.checked;
                });

                if (this.checked) {
                    showToast('已启用智能推荐，系统将根据检索内容自动选择最佳数据库', 'info');
                } else {
                    showToast('已关闭智能推荐，请手动选择专业方向', 'info');
                }
            });
        }

        // 生成检索策略按钮事件处理
        const searchBtn = document.getElementById('search-btn');
        if (searchBtn) {
            searchBtn.addEventListener('click', async function() {
                const query = document.getElementById('search-input').value.trim();
                if (!query) {
                    showToast('请输入检索内容', 'error');
                    return;
                }
                
                // 防止重复点击
                if (ButtonStateManager.isLoading(searchBtn)) {
                    return;
                }
                
                // 设置按钮为loading状态
                ButtonStateManager.setLoading(searchBtn, '生成策略中...');
                
                // 清空并显示进度容器
                const progressContainer = document.getElementById('searchProgress');
                const progressLog = progressContainer.querySelector('.progress-log');
                progressLog.innerHTML = '';
                progressContainer.style.display = 'block';

                // 获取专业方向选择
                const selectedField = document.querySelector('input[name="field"]:checked')?.value || 'pubmed';
                const autoRecommend = document.getElementById('auto-recommend').checked;

                // 获取筛选条件
                const filters = {
                    year_start: document.getElementById('year-start').value,
                    year_end: document.getElementById('year-end').value,
                    min_if: document.getElementById('min-if').value,
                    jcr_quartile: Array.from(document.querySelectorAll('input[name="jcr"]:checked')).map(cb => cb.value),
                    cas_quartile: Array.from(document.querySelectorAll('input[name="cas"]:checked')).map(cb => cb.value),
                    papers_limit: document.getElementById('papers-limit').value
                };

                // 如果启用智能推荐，先获取推荐的数据库
                let database = selectedField;
                if (autoRecommend) {
                    try {
                        const recommendResponse = await fetch('/api/databases/recommend', {
                            method: 'POST',
                            headers: getHeaders(),
                            body: JSON.stringify({ query: query })
                        });

                        if (recommendResponse.ok) {
                            const recommendData = await recommendResponse.json();
                            if (recommendData.success) {
                                database = recommendData.recommended_database;
                                // 保存推荐的数据库供后续使用
                                window.recommendedDatabase = database;
                                showToast(`智能推荐使用 ${recommendData.database_info.name} 数据库`, 'info');
                            }
                        }
                    } catch (error) {
                        console.warn('数据库推荐失败，使用默认选择:', error);
                    }
                } else {
                    // 保存手动选择的数据库
                    window.recommendedDatabase = database;
                }

                try {
                    const response = await fetch('/api/generate_strategy', {
                            method: 'POST',
                        headers: getHeaders(),
                            body: JSON.stringify({
                            query: query,
                            mode: currentSearchMode,
                            filters: filters,
                            database: database,
                            auto_recommend: autoRecommend
                            })
                        });

                        if (!response.ok) {
                        throw new Error(await response.text());
                    }

                    const data = await response.json();
                    if (data.success) {
                        // 保存当前查询信息，供后续分析使用
                        currentQuery = query;
                        currentSessionId = data.session_id || '';

                        // 刷新历史会话侧边栏
                        loadHistorySessions();

                        // 显示检索策略容器
                        const strategyContainer = document.getElementById('search-strategy-container');
                        const strategyTextarea = document.getElementById('search-strategy');
                        
                        if (currentSearchMode === 'paragraph') {
                            // 段落模式：显示多个检索策略
                            const strategies = data.sentences.map((sentence, index) => 
                                `# 句子 ${index + 1}：${sentence.text}\n${sentence.search_strategy}\n`
                            ).join('\n');
                            strategyTextarea.value = strategies;
                        } else {
                            // 单句模式：显示单个检索策略
                            strategyTextarea.value = data.search_strategy;
                        }
                        
                        strategyContainer.style.display = 'block';
                        
                        // 显示成功状态
                        ButtonStateManager.setSuccess(searchBtn, '生成完成');
                        showToast('检索策略生成完成', 'success');
                    } else {
                        // 显示错误状态
                        ButtonStateManager.setError(searchBtn, '生成失败');
                        throw new Error(data.error || '生成检索策略失败');
                    }
                } catch (error) {
                    console.error('Generate strategy failed:', error);
                    // 如果还没有设置错误状态，则设置
                    if (ButtonStateManager.isLoading(searchBtn)) {
                        ButtonStateManager.setError(searchBtn, '生成失败');
                    }
                    showToast(error.message || '生成检索策略失败，请稍后重试', 'error');
                }
            });
        }

        // 执行检索策略按钮事件处理
        const executeBtn = document.getElementById('execute-strategy-btn');
        if (executeBtn) {
            executeBtn.addEventListener('click', async function() {
                const strategy = document.getElementById('search-strategy').value.trim();
                if (!strategy) {
                    showToast('请先生成检索策略', 'error');
                    return;
                }

                // 防止重复点击
                if (ButtonStateManager.isLoading(executeBtn)) {
                    return;
                }
                
                // 设置按钮为loading状态
                ButtonStateManager.setLoading(executeBtn, '检索中...');

                // 清空并显示进度容器
                const progressContainer = document.getElementById('searchProgress');
                const progressLog = progressContainer.querySelector('.progress-log');
                progressLog.innerHTML = '';
                progressContainer.style.display = 'block';

                // 重置进度条
                const progressBar = progressContainer.querySelector('.progress-bar');
                const progressPercentage = progressContainer.querySelector('.progress-percentage');
                const progressMessage = progressContainer.querySelector('.progress-message');
                
                progressBar.style.width = '0%';
                progressBar.setAttribute('aria-valuenow', 0);
                progressPercentage.textContent = '0%';
                progressMessage.textContent = '准备开始检索...';

                // 获取专业方向选择和筛选条件
                const selectedField = document.querySelector('input[name="field"]:checked')?.value || 'pubmed';
                const autoRecommend = document.getElementById('auto-recommend').checked;

                const filters = {
                    year_start: document.getElementById('year-start').value,
                    year_end: document.getElementById('year-end').value,
                    min_if: document.getElementById('min-if').value,
                    jcr_quartile: Array.from(document.querySelectorAll('input[name="jcr"]:checked')).map(cb => cb.value),
                    cas_quartile: Array.from(document.querySelectorAll('input[name="cas"]:checked')).map(cb => cb.value),
                    papers_limit: document.getElementById('papers-limit').value
                };

                // 确定使用的数据库
                let database = selectedField;
                if (autoRecommend) {
                    // 如果启用智能推荐，从策略生成时已经确定了数据库
                    // 这里可以从策略文本中提取或使用全局变量
                    database = window.recommendedDatabase || selectedField;
                }

                try {
                    // 根据当前搜索模式决定执行方式
                    let searchMode, searchQuery;
                    if (currentSearchMode === 'paragraph') {
                        // 段落模式：需要特殊处理，执行多个策略
                        await executeMultipleParagraphStrategies(filters);
                        return; // 直接返回，不继续执行单一搜索
                    } else {
                        // 单句模式：使用生成的策略
                        searchMode = 'strategy';
                        searchQuery = strategy;
                    }
                    
                    // 根据数据库选择不同的API端点
                    // 🔧 修复：所有数据库都使用统一的 /api/search 端点
                    let apiEndpoint = '/api/search';
                    let requestBody = {
                        query: searchQuery,
                        mode: searchMode,
                        filters: filters,
                        database: database,
                        include_relevance: true,  // 确保包含相关性分析
                        original_query: searchQuery  // 添加原始查询用于相关性分析
                    };

                    console.log(`使用数据库: ${database}, API端点: ${apiEndpoint}`);

                    const response = await fetch(apiEndpoint, {
                        method: 'POST',
                        headers: getHeaders(),
                        body: JSON.stringify(requestBody)
                    });

                    if (!response.ok) {
                        throw new Error(await response.text());
                    }

                    const data = await response.json();
                    if (data.success) {
                        // 处理不同数据库的返回格式
                        let papers = [];
                        let totalCount = 0;
                        let filteredCount = 0;

                        if (database === 'pubmed') {
                            // PubMed 格式
                            papers = data.data || [];
                            totalCount = data.total_count || 0;
                            filteredCount = data.filtered_count || 0;

                            // 更新会话ID
                            if (data.session_id) {
                                currentSessionId = data.session_id;
                            }

                            // 保存导出文件路径
                            if (data.export_files) {
                                window.currentExportFiles = data.export_files;
                                console.log('导出文件路径已保存:', data.export_files);
                            }
                        } else {
                            // 🔧 修复：统一API返回格式（所有数据库都使用 data.data）
                            papers = data.data || [];
                            totalCount = data.total_count || 0;
                            filteredCount = papers.length;

                            // 🔧 修复：更新会话ID（关键修复）
                            if (data.session_id) {
                                currentSessionId = data.session_id;
                                console.log('多数据库会话ID已更新:', currentSessionId);
                            }

                            // 保存文献数据供导出使用
                            window.currentPapers = papers;
                            window.currentDatabase = database;

                            // 为多数据库设置导出文件标识
                            window.currentExportFiles = {
                                'excel': 'available',
                                'word': 'available'
                            };
                            console.log('多数据库导出文件已准备:', window.currentExportFiles);
                        }

                        // 显示检索统计
                        document.getElementById('search-stats').style.display = 'block';
                        document.getElementById('total-count').textContent = totalCount;
                        document.getElementById('filtered-count').textContent = filteredCount;

                        // 更新进度条到100%
                        progressBar.style.width = '100%';
                        progressBar.setAttribute('aria-valuenow', 100);
                        progressPercentage.textContent = '100%';
                        progressMessage.textContent = '检索完成';
                        
                        // 显示搜索结果
                        const resultsContainer = document.getElementById('results-container');
                        resultsContainer.innerHTML = '';

                        if (papers && papers.length > 0) {
                            papers.forEach(paper => {
                                const resultHtml = generatePaperHtml(paper, database);
                                resultsContainer.insertAdjacentHTML('beforeend', resultHtml);
                            });
                        } else {
                            resultsContainer.innerHTML = `
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    未找到符合条件的文献
                                </div>
                            `;
                        }

                        // 添加学术分析功能区域 - 优化后的布局
                        if (data.data && data.data.length > 0) {
                            const actionButtonsHtml = `
                                <div class="card mt-3 border-primary" style="background: linear-gradient(135deg, #f8f9ff 0%, #ffffff 100%); border-width: 2px;">
                                    <div class="card-header bg-primary text-white py-3">
                                        <h5 class="card-title mb-0">
                                            <i class="fas fa-brain me-2"></i>智能学术分析工具
                                        </h5>
                                        <small class="opacity-75">基于 ${data.data.length} 篇文献的AI深度分析</small>
                                    </div>
                                    <div class="card-body py-4">
                                        <p class="card-text text-secondary mb-4">
                                            <i class="fas fa-lightbulb text-warning me-2"></i>
                                            使用AI技术为您生成专业的学术建议，让研究更高效！点击按钮后将在新页面展示详细结果。
                                        </p>
                                        <div class="row g-3">
                                            <div class="col-md-4">
                                                <button class="btn btn-primary w-100 py-3 analysis-btn" onclick="generateReviewTopicSuggestions()">
                                                    <i class="fas fa-file-alt me-2"></i>
                                                    <div class="btn-content">
                                                        <div class="fw-bold">📝 综述选题建议</div>
                                                        <small class="d-block opacity-75">智能推荐综述主题</small>
                                                    </div>
                                                </button>
                                            </div>
                                            <div class="col-md-4">
                                                <button class="btn btn-success w-100 py-3 analysis-btn" onclick="generateResearchTopicSuggestions()">
                                                    <i class="fas fa-microscope me-2"></i>
                                                    <div class="btn-content">
                                                        <div class="fw-bold">🔬 论著选题建议</div>
                                                        <small class="d-block opacity-75">发现研究空白点</small>
                                                    </div>
                                                </button>
                                            </div>

                                        </div>
                                        <div class="mt-3 text-center">
                                            <small class="text-muted">
                                                <i class="fas fa-info-circle me-1"></i>
                                                分析结果将在新页面显示，方便您查看和保存
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            `;
                            // 将分析工具插入到搜索结果之前，而不是之后
                            resultsContainer.insertAdjacentHTML('afterbegin', actionButtonsHtml);
                        }

                            // 滚动到结果区域
                            resultsContainer.scrollIntoView({ behavior: 'smooth' });

                        // 显示成功状态
                        ButtonStateManager.setSuccess(executeBtn, '检索完成');
                        showToast('检索完成', 'success');
                    } else {
                        // 显示错误状态
                        ButtonStateManager.setError(executeBtn, '检索失败');
                        throw new Error(data.error || '检索失败');
                    }
                } catch (error) {
                    console.error('Search failed:', error);
                    // 如果还没有设置错误状态，则设置
                    if (ButtonStateManager.isLoading(executeBtn)) {
                        ButtonStateManager.setError(executeBtn, '检索失败');
                    }
                    // 添加错误日志
                    const logEntry = document.createElement('div');
                    logEntry.className = 'log-entry';
                    const time = new Date().toLocaleTimeString();
                    logEntry.innerHTML = `
                        <span class="log-time">[${time}]</span>
                        <span class="log-message log-error">✗ ${error.message || '检索失败，请稍后重试'}</span>
                    `;
                    progressLog.appendChild(logEntry);
                    showToast(error.message || '检索失败，请稍后重试', 'error');
                }
            });
        }

        // 热点分析功能
        const analyzeBtn = document.getElementById('analyze-btn');
        if (analyzeBtn) {
            analyzeBtn.addEventListener('click', async function() {
                const journal = document.getElementById('journal-name').value.trim();
                const keywords = document.getElementById('keywords').value.trim();
                const startYear = document.getElementById('start-year').value;
                const endYear = document.getElementById('end-year').value;

                if (!journal) {
                    showToast('请输入期刊名称', 'error');
                    return;
                }

                if (!startYear || !endYear) {
                    showToast('请输入完整的时间范围', 'error');
                    return;
                }

                if (parseInt(startYear) > parseInt(endYear)) {
                    showToast('起始年份不能大于结束年份', 'error');
                    return;
                }

                // 防止重复点击
                if (ButtonStateManager.isLoading(analyzeBtn)) {
                    return;
                }
                
                // 设置按钮为loading状态
                ButtonStateManager.setLoading(analyzeBtn, '分析中...');
                    
                try {
                    // 使用getHeaders()函数添加会话ID
                    const response = await fetch('/api/analyze-journal', {
                        method: 'POST',
                        headers: getHeaders(), // 添加会话ID
                        body: JSON.stringify({
                            journal: journal,
                            keywords: keywords,
                            start_year: parseInt(startYear),
                            end_year: parseInt(endYear)
                        })
                    });

                    if (!response.ok) {
                        throw new Error(await response.text());
                    }

                    const data = await response.json();
                    if (data.success) {
                        // 发送分析开始事件
                        socket.emit('analysis_progress', {
                            stage: 'start',
                            message: '开始分析期刊热点...',
                            percentage: 0
                        });

                        displayAnalysisResults(data);
                        
                        // 发送分析完成事件
                        socket.emit('analysis_progress', {
                            stage: 'complete',
                            message: '分析完成',
                            percentage: 100
                        });
                        
                        // 显示成功状态
                        ButtonStateManager.setSuccess(analyzeBtn, '分析完成');
                        showToast('分析完成', 'success');
                    } else {
                        // 显示错误状态
                        ButtonStateManager.setError(analyzeBtn, '分析失败');
                        throw new Error(data.error || '分析失败');
                    }
                } catch (error) {
                    console.error('Analysis failed:', error);
                    // 如果还没有设置错误状态，则设置
                    if (ButtonStateManager.isLoading(analyzeBtn)) {
                        ButtonStateManager.setError(analyzeBtn, '分析失败');
                    }
                    // 发送错误事件
                    socket.emit('analysis_error', {
                        error: error.message || '分析失败，请稍后重试'
                    });
                    showToast(error.message || '分析失败，请稍后重试', 'error');
                }
            });
        }

        // 导出按钮点击事件处理
        const exportBtn = document.getElementById('stats-export-btn');
        const exportDropdown = document.getElementById('stats-export-dropdown');

        if (exportBtn && exportDropdown) {
            // 点击导出按钮显示/隐藏下拉菜单
            exportBtn.addEventListener('click', function(e) {
                e.stopPropagation();
                
                // 检查是否有可用的导出文件
                if (!window.currentExportFiles || Object.keys(window.currentExportFiles).length === 0) {
                    showToast('导出文件未准备好，请先执行检索', 'error');
                    return;
                }
                
                exportDropdown.classList.toggle('show');
            });

            // 导出状态管理
            let exportingItems = new Set(); // 记录正在导出的项目

            // 点击下拉菜单项处理导出请求
            exportDropdown.addEventListener('click', async function(e) {
                const exportItem = e.target.closest('.export-item');
                if (!exportItem) return;

                const exportType = exportItem.dataset.type;
                const exportFiles = window.currentExportFiles || {};
                
                console.log('当前导出文件:', exportFiles);
                console.log('选择的导出类型:', exportType);

                // 防重复点击检查
                if (exportingItems.has(exportType)) {
                    showToast('正在导出中，请勿重复点击', 'warning');
                    return;
                }

                // 确定导出的API端点和数据类型
                let apiEndpoint, dataType;
                switch(exportType) {
                    case 'initial_word':
                    case 'filtered_word':
                        apiEndpoint = '/api/export/word';
                        dataType = exportType.includes('initial') ? 'initial' : 'filtered';
                        break;
                    case 'initial_excel':
                    case 'filtered_excel':
                        apiEndpoint = '/api/export/excel';
                        dataType = exportType.includes('initial') ? 'initial' : 'filtered';
                        break;
                    default:
                        console.error('未知的导出类型:', exportType);
                        showToast('导出类型错误', 'error');
                        return;
                }

                // 检查是否可以导出
                const baseType = exportType.includes('word') ? 'word' : 'excel';
                if (!exportFiles[exportType] && !exportFiles[baseType]) {
                    console.error('导出未准备好:', exportType, '可用文件:', exportFiles);
                    showToast('导出文件未准备好，请先执行检索', 'error');
                    return;
                }

                try {
                    // 标记为正在导出
                    exportingItems.add(exportType);
                    
                    // 更新按钮状态
                    const originalText = exportItem.innerHTML;
                    exportItem.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>导出中...';
                    exportItem.style.pointerEvents = 'none';
                    exportItem.style.opacity = '0.6';
                    
                    showToast('正在准备导出文件...', 'info');
                    
                    // 设置5秒后自动恢复按钮状态的定时器
                    const autoRecoveryTimeout = setTimeout(() => {
                        exportingItems.delete(exportType);
                        exportItem.innerHTML = originalText;
                        exportItem.style.pointerEvents = '';
                        exportItem.style.opacity = '';
                        console.log('按钮状态已自动恢复');
                    }, 5000);
                    
                    // 调用导出API
                    const response = await fetch(apiEndpoint, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'sid': currentSessionId
                        },
                        body: JSON.stringify({
                            query: currentQuery || '',  // 添加查询参数
                            data_type: dataType
                        })
                    });

                    if (response.ok) {
                        // 获取文件名
                        const contentDisposition = response.headers.get('content-disposition');
                        let filename = 'export';
                        if (contentDisposition) {
                            const matches = /filename="?([^"]+)"?/.exec(contentDisposition);
                            if (matches) filename = matches[1];
                        }
                        
                        // 创建下载链接
                        const blob = await response.blob();
                        const url = window.URL.createObjectURL(blob);
                        const link = document.createElement('a');
                        link.href = url;
                        link.download = filename;
                        document.body.appendChild(link);
                        link.click();
                        
                        // 延迟清理，确保下载开始
                        setTimeout(() => {
                            document.body.removeChild(link);
                            window.URL.revokeObjectURL(url);
                        }, 100);
                        
                        showToast('文件下载成功', 'success');
                        
                        // 清除自动恢复定时器，因为操作已完成
                        clearTimeout(autoRecoveryTimeout);
                        
                        // 立即恢复按钮状态（但为了用户体验，延迟1秒）
                        setTimeout(() => {
                            exportingItems.delete(exportType);
                            exportItem.innerHTML = originalText;
                            exportItem.style.pointerEvents = '';
                            exportItem.style.opacity = '';
                        }, 1000);
                        
                    } else {
                        const errorData = await response.json();
                        console.error('导出失败:', errorData);
                        showToast(errorData.error || '导出失败', 'error');
                        
                        // 清除自动恢复定时器
                        clearTimeout(autoRecoveryTimeout);
                        
                        // 立即恢复按钮状态
                        exportingItems.delete(exportType);
                        exportItem.innerHTML = originalText;
                        exportItem.style.pointerEvents = '';
                        exportItem.style.opacity = '';
                    }
                } catch (error) {
                    console.error('导出请求失败:', error);
                    showToast('导出请求失败，请重试', 'error');
                    
                    // 发生错误时立即恢复按钮状态
                    exportingItems.delete(exportType);
                    exportItem.innerHTML = originalText;
                    exportItem.style.pointerEvents = '';
                    exportItem.style.opacity = '';
                }

                // 隐藏下拉菜单
                exportDropdown.classList.remove('show');
            });

            // 点击页面其他地方时隐藏下拉菜单
            document.addEventListener('click', function(e) {
                if (!exportBtn.contains(e.target) && !exportDropdown.contains(e.target)) {
                    exportDropdown.classList.remove('show');
                }
            });
        }

        // 在搜索进度更新时保存导出文件路径
        socket.on('search_progress', function(data) {
            console.log('收到搜索进度更新:', data);
            if (data.stage === 'complete' && data.files) {
                window.currentExportFiles = data.files;
                console.log('已保存导出文件路径:', data.files);
            }
        });

        // 添加分析进度事件监听
        socket.on('analysis_progress', function(data) {
            console.log('收到分析进度更新:', data);
            if (data.stage === 'complete') {
                showToast('分析完成', 'success');
            } else if (data.stage === 'error') {
                showToast(data.message || '分析出错', 'error');
            }
        });

        // 添加分析错误事件监听
        socket.on('analysis_error', function(data) {
            console.error('收到分析错误:', data);
            showToast(data.error || '分析出错', 'error');
        });
    });

    // 显示分析结果
    function displayAnalysisResults(data) {
        const resultsContainer = document.querySelector('.analysis-results');
        resultsContainer.style.display = 'grid';

        // 绘制热力图
        const heatmapChart = echarts.init(document.getElementById('heatmap-chart'));
        const heatmapData = data.heatmap_data || [];
        
        if (heatmapData.length > 0) {
            // 准备热力图数据
            const xAxisData = [];
            const yAxisData = [];
            const values = [];
            let maxValue = 0;

            // 从一维数组构建热力图数据
            heatmapData.forEach(([topic, count]) => {
                if (count > maxValue) maxValue = count;
                xAxisData.push(topic);  // 直接使用主题作为x轴标签
            });

            // 构建热力图数据
            heatmapData.forEach(([topic, count], index) => {
                values.push([index, 0, count]);  // 使用一维布局
            });

            const heatmapOption = {
                tooltip: {
                    position: 'top',
                    formatter: function(params) {
                        const topic = xAxisData[params.data[0]];
                        const value = params.data[2];
                        return `${topic}<br/>文章数量: ${value}`;
                    }
                },
                grid: {
                    height: '50%',
                    top: '10%'
                },
                xAxis: {
                    type: 'category',
                    data: xAxisData,
                    splitArea: {
                        show: true
                    },
                    axisLabel: {
                        interval: 0,
                        rotate: 45
                    }
                },
                yAxis: {
                    type: 'category',
                    data: [''],
                    splitArea: {
                        show: true
                    },
                    show: false
                },
                visualMap: {
                    min: 0,
                    max: maxValue,
                    calculable: true,
                    orient: 'horizontal',
                    left: 'center',
                    bottom: '15%'
                },
                series: [{
                    name: '热点分布',
                    type: 'heatmap',
                    data: values,
                    label: {
                        show: true,
                        formatter: function(params) {
                            return params.data[2];  // 只显示数值
                        }
                    },
                    emphasis: {
                        itemStyle: {
                            shadowBlur: 10,
                            shadowColor: 'rgba(0, 0, 0, 0.5)'
                        }
                    }
                }]
            };
            heatmapChart.setOption(heatmapOption);
        } else {
            document.getElementById('heatmap-chart').innerHTML = '<div class="no-data-message">暂无热点主题数据</div>';
        }

        // 绘制词云图
        const wordcloudChart = echarts.init(document.getElementById('wordcloud-chart'));
        const wordcloudData = data.wordcloud_data || [];

        if (wordcloudData.length > 0) {
            // 找出最大频率值，用于归一化
            const maxFrequency = wordcloudData.length > 0 ? 
                Math.max(...wordcloudData.map(item => item[1])) : 1;
                
            const wordcloudOption = {
                tooltip: {
                    formatter: function(params) {
                        return `${params.data.name}: ${params.data.value}篇文章`;
                    }
                },
                backgroundColor: '#ffffff',  // 使用纯白色背景，与黑色文字形成最佳对比
                series: [{
                    type: 'wordCloud',
                    shape: 'circle',
                    left: 'center',
                    top: 'center',
                    width: '90%',
                    height: '90%',
                    right: null,
                    bottom: null,
                    sizeRange: [18, 60],  // 增大字体最小值
                    rotationRange: [-30, 30],  // 减小旋转角度范围
                    rotationStep: 10,
                    gridSize: 10,  // 增大网格大小
                    drawOutOfBound: false,
                    layoutAnimation: true,
                    // 全局文字样式
                    textStyle: {
                        fontFamily: 'sans-serif',
                        fontWeight: 'bold',
                        textBorderColor: 'rgba(255,255,255,0.5)',
                        textBorderWidth: 2,
                        textShadowColor: 'rgba(0,0,0,0.3)',
                        textShadowBlur: 2,
                        color: '#000000'  // 统一使用黑色
                    },
                    // 强调样式
                    emphasis: {
                        focus: 'self',
                        textStyle: {
                            textBorderColor: '#fff',
                            textBorderWidth: 2,
                            shadowBlur: 10,
                            shadowColor: '#333',
                            color: '#000000'  // 强调时也使用黑色
                        }
                    },
                    // 直接设置数据，不使用processedData
                    data: wordcloudData.slice(0, 100).map(([name, value]) => {
                        // 增加字体大小范围，使文字更加醒目
                        const fontSize = Math.max(18, Math.min(60, 18 + (value / maxFrequency) * 42));
                        return {
                            name: name,
                            value: value,
                            textStyle: {
                                fontSize: fontSize,
                                // 增加文字描边和阴影效果
                                textBorderColor: 'rgba(255,255,255,0.5)',
                                textBorderWidth: 2,
                                textShadowColor: 'rgba(0,0,0,0.3)',
                                textShadowBlur: 2,
                                color: '#000000'  // 每个词语都使用黑色
                            }
                        };
                    })
                }]
            };

            try {
                console.log('词云图数据:', wordcloudData.slice(0, 100).map(([name, value]) => ({
                    name: name,
                    value: value
                })));
                wordcloudChart.setOption(wordcloudOption);
                
                // 添加窗口大小变化时重新渲染
                window.addEventListener('resize', function() {
                    wordcloudChart.resize();
                });
                
                // 强制重新渲染一次
                setTimeout(() => {
                    wordcloudChart.resize();
                    console.log('词云图重新渲染完成');
                }, 100);
            } catch (error) {
                console.error('词云图渲染失败:', error);
                document.getElementById('wordcloud-chart').innerHTML = 
                    '<div class="no-data-message">词云图渲染失败，请尝试减少关键词数量</div>';
            }
        } else {
            document.getElementById('wordcloud-chart').innerHTML = 
                '<div class="no-data-message">暂无关键词数据</div>';
        }

        // 绘制趋势图
        const trendChart = echarts.init(document.getElementById('trend-chart'));
        const trendData = data.trend_data || {};

        if (trendData.topics && trendData.topics.length > 0) {
            const trendOption = {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'cross',
                        label: {
                            backgroundColor: '#6a7985'
                        }
                    }
                },
                legend: {
                    data: trendData.topics,
                    top: '5%'
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    boundaryGap: false,
                    data: trendData.years || []
                },
                yAxis: {
                    type: 'value',
                    name: '文章数量'
                },
                series: (trendData.topics || []).map((topic, index) => ({
                    name: topic,
                    type: 'line',
                    data: trendData.frequencies ? trendData.frequencies[index] : [],
                    smooth: true,
                    symbol: 'circle',
                    symbolSize: 8,
                    lineStyle: {
                        width: 3
                    }
                }))
            };
            trendChart.setOption(trendOption);
        } else {
            document.getElementById('trend-chart').innerHTML = '<div class="no-data-message">暂无趋势数据</div>';
        }

        // 渲染热点作者表格
        const authorsTable = document.getElementById('hot-authors-body');
        const hotAuthors = (data.hot_authors || []).filter(author => author.name && author.name !== 'N/A');
        
        if (hotAuthors.length > 0) {
            authorsTable.innerHTML = hotAuthors
                .map((author, index) => `
                    <tr>
                        <td>${index + 1}</td>
                        <td>${author.name}</td>
                        <td>${author.total_papers}</td>
                        <td>${author.first_author}</td>
                        <td>${author.corresponding_author}</td>
                        <td>${author.years.join(', ')}</td>
                    </tr>
                `).join('');
        } else {
            authorsTable.innerHTML = `
                <tr>
                    <td colspan="6" class="text-center">暂无热点作者数据</td>
                </tr>
            `;
        }

        // 监听窗口大小变化，重绘图表
        window.addEventListener('resize', function() {
            if (heatmapData.length > 0) heatmapChart.resize();
            if (wordcloudData.length > 0) wordcloudChart.resize();
            if (trendData.topics && trendData.topics.length > 0) trendChart.resize();
        });
    }

    // 综述选题建议 - 异步版本
    async function generateReviewTopicSuggestions() {
        // 查找按钮元素
        const btnElement = event.target.closest('button');

        // 防止重复点击
        if (ButtonStateManager.isLoading(btnElement)) {
            return;
        }

        // 检查是否有文献数据
        if (!currentSessionId || !currentQuery) {
            ButtonStateManager.setError(btnElement, '请先检索');
            showToast('请先进行文献检索，然后再生成选题建议', 'warning');
            return;
        }

        try {
            // 设置按钮loading状态
            ButtonStateManager.setLoading(btnElement, '启动中...');
            showToast('正在启动综述选题建议分析任务...', 'info');

            // 调试日志
            console.log('Review topic suggestion - currentSessionId:', currentSessionId);
            console.log('Review topic suggestion - currentQuery:', currentQuery);

            // 启动异步任务
            const response = await fetch('/api/async/review_topic_suggestion', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'sid': currentSessionId
                },
                body: JSON.stringify({ query: currentQuery })
            });

            const data = await response.json();

            if (data.success) {
                // 任务启动成功，开始轮询状态
                ButtonStateManager.setLoading(btnElement, '分析中...');
                showToast('任务已启动，正在分析中...', 'success');
                
                // 立即打开加载页面
                const loadingWindow = window.open('about:blank', '_blank');
                if (loadingWindow) {
                    // 显示加载页面
                    loadingWindow.document.write(getAnalysisLoadingPage('综述选题建议'));
                }
                
                // 开始轮询任务状态
                pollTaskStatus(data.task_id, btnElement, loadingWindow, '综述选题建议');
                
            } else {
                // 启动失败
                ButtonStateManager.setError(btnElement, '启动失败');
                showToast(`任务启动失败: ${data.error}`, 'error');
            }
        } catch (error) {
            // 如果还没有设置错误状态，则设置
            if (ButtonStateManager.isLoading(btnElement)) {
                ButtonStateManager.setError(btnElement, '启动失败');
            }
            showToast(`启动过程中出现错误: ${error.message}`, 'error');
        }
    }

    // 论著选题建议 - 异步版本
    async function generateResearchTopicSuggestions() {
        // 查找按钮元素
        const btnElement = event.target.closest('button');

        // 防止重复点击
        if (ButtonStateManager.isLoading(btnElement)) {
            return;
        }

        // 检查是否有文献数据
        if (!currentSessionId || !currentQuery) {
            ButtonStateManager.setError(btnElement, '请先检索');
            showToast('请先进行文献检索，然后再生成选题建议', 'warning');
            return;
        }

        try {
            // 设置按钮loading状态
            ButtonStateManager.setLoading(btnElement, '启动中...');
            showToast('正在启动论著选题建议分析任务...', 'info');

            // 调试日志
            console.log('Research topic suggestion - currentSessionId:', currentSessionId);
            console.log('Research topic suggestion - currentQuery:', currentQuery);

            // 启动异步任务
            const response = await fetch('/api/async/research_topic_suggestion', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'sid': currentSessionId
                },
                body: JSON.stringify({ query: currentQuery })
            });

            const data = await response.json();

            if (data.success) {
                // 任务启动成功，开始轮询状态
                ButtonStateManager.setLoading(btnElement, '分析中...');
                showToast('任务已启动，正在分析中...', 'success');
                
                // 立即打开加载页面
                const loadingWindow = window.open('about:blank', '_blank');
                if (loadingWindow) {
                    // 显示加载页面
                    loadingWindow.document.write(getAnalysisLoadingPage('论著选题建议'));
                }
                
                // 开始轮询任务状态
                pollTaskStatus(data.task_id, btnElement, loadingWindow, '论著选题建议');
                
            } else {
                // 启动失败
                ButtonStateManager.setError(btnElement, '启动失败');
                showToast(`任务启动失败: ${data.error}`, 'error');
            }
        } catch (error) {
            // 如果还没有设置错误状态，则设置
            if (ButtonStateManager.isLoading(btnElement)) {
                ButtonStateManager.setError(btnElement, '启动失败');
            }
            showToast(`启动过程中出现错误: ${error.message}`, 'error');
        }
    }

    // 显示综述题目输入对话框
    function showReviewTitleDialog() {
        const btnElement = event.target.closest('button');

        // 防止重复点击
        if (ButtonStateManager.isLoading(btnElement)) {
            return;
        }

        // 检查是否有文献数据
        if (!currentSessionId || !currentQuery) {
            ButtonStateManager.setError(btnElement, '请先检索');
            showToast('请先进行文献检索，然后再生成综述', 'warning');
            return;
        }

        // 创建对话框HTML
        const dialogHtml = `
            <div class="modal fade" id="reviewTitleModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">📚 生成完整综述</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="mb-3">
                                <label for="reviewTitle" class="form-label">综述题目（可选）</label>
                                <input type="text" class="form-control" id="reviewTitle"
                                       placeholder="请输入自定义综述题目，留空则使用默认题目">
                                <div class="form-text">
                                    默认题目：${currentQuery} - 文献综述
                                </div>
                            </div>
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>高级综述生成流程：</strong>
                                <ol class="mb-0 mt-2">
                                    <li>基于题目分析文献相关度，筛选最相关的100篇文献</li>
                                    <li>生成结构严谨的综述大纲</li>
                                    <li>分章节填充内容，使用PMID引用格式</li>
                                    <li>整合格式化，生成标准参考文献列表</li>
                                </ol>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                            <button type="button" class="btn btn-primary" onclick="generateCompleteReview()">
                                <i class="fas fa-magic me-2"></i>开始生成
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 移除已存在的对话框
        const existingModal = document.getElementById('reviewTitleModal');
        if (existingModal) {
            existingModal.remove();
        }

        // 添加对话框到页面
        document.body.insertAdjacentHTML('beforeend', dialogHtml);

        // 显示对话框
        const modal = new bootstrap.Modal(document.getElementById('reviewTitleModal'));
        modal.show();
    }

    // 生成完整综述 - 异步版本
    async function generateCompleteReview() {
        // 查找按钮元素
        const btnElement = event.target.closest('button');

        // 防止重复点击
        if (ButtonStateManager.isLoading(btnElement)) {
            return;
        }

        // 获取用户输入的题目
        const titleInput = document.getElementById('reviewTitle');
        const userTitle = titleInput ? titleInput.value.trim() : '';

        // 关闭对话框
        const modal = bootstrap.Modal.getInstance(document.getElementById('reviewTitleModal'));
        if (modal) {
            modal.hide();
        }

        try {
            // 设置按钮loading状态
            ButtonStateManager.setLoading(btnElement, '启动中...');
            showToast('正在启动高级综述生成任务...', 'info');

            // 启动异步任务
            const response = await fetch('/api/async/generate_full_review', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'sid': currentSessionId
                },
                body: JSON.stringify({
                    query: currentQuery,
                    user_title: userTitle
                })
            });

            const data = await response.json();

            if (data.success) {
                // 任务启动成功，开始轮询状态
                ButtonStateManager.setLoading(btnElement, '生成中...');
                showToast('任务已启动，正在生成综述...', 'success');
                
                // 立即打开加载页面
                const loadingWindow = window.open('about:blank', '_blank');
                if (loadingWindow) {
                    // 显示加载页面
                    loadingWindow.document.write(getAnalysisLoadingPage('完整综述'));
                }
                
                // 开始轮询任务状态
                pollTaskStatus(data.task_id, btnElement, loadingWindow, '完整综述');
                
            } else {
                // 启动失败
                ButtonStateManager.setError(btnElement, '启动失败');
                showToast(`任务启动失败: ${data.error}`, 'error');
            }
        } catch (error) {
            // 如果还没有设置错误状态，则设置
            if (ButtonStateManager.isLoading(btnElement)) {
                ButtonStateManager.setError(btnElement, '启动失败');
            }
            showToast(`启动过程中出现错误: ${error.message}`, 'error');
        }
    }

    // 生成分析加载页面HTML - 安全版本
    function getAnalysisLoadingPage(analysisType) {
        // 使用字符编码避免HTML解析问题
        const htmlStart = String.fromCharCode(60) + '!DOCTYPE html' + String.fromCharCode(62);
        const htmlEnd = String.fromCharCode(60) + '/html' + String.fromCharCode(62);
        
        return htmlStart + '<html><head><title>' + analysisType + '生成中</title>' +
               '<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">' +
               '<style>body{background:linear-gradient(135deg,#667eea 0%,#764ba2 100%);height:100vh;display:flex;align-items:center;justify-content:center;color:white;font-family:sans-serif}' +
               '.container{text-align:center;padding:2rem}.icon{font-size:4rem;margin-bottom:2rem}.spinner{width:60px;height:60px;border:4px solid rgba(255,255,255,0.3);border-top:4px solid white;border-radius:50%;animation:spin 1s linear infinite;margin:2rem auto}' +
               '.progress{margin:2rem 0;font-size:1.1rem}.status{margin:1rem 0;opacity:0.8}' +
               '@keyframes spin{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}</style></head>' +
               '<body><div class="container"><div class="icon">🧠</div><h2>' + analysisType + '生成中...</h2>' +
               '<div class="status" id="status">AI正在基于您的文献进行深度分析，请稍候</div>' +
               '<div class="spinner"></div><div class="progress" id="progress">准备中...</div></div></body>' + htmlEnd;
    }

    // 轮询任务状态
    async function pollTaskStatus(taskId, btnElement, loadingWindow, analysisType) {
        const pollInterval = 2000; // 每2秒轮询一次
        const maxPolls = 120; // 最多轮询4分钟
        let pollCount = 0;
        
        const poll = async () => {
            try {
                console.log(`Polling task ${taskId}, attempt ${pollCount + 1}`);
                const response = await fetch(`/api/async/task/${taskId}/status`);
                const data = await response.json();
                console.log('Poll response:', data);

                if (data.success && data.task) {
                    const task = data.task;
                    console.log('Task status:', task.status, 'Progress:', task.progress);
                    
                    // 更新加载页面的状态
                    if (loadingWindow && !loadingWindow.closed) {
                        try {
                            const statusElement = loadingWindow.document.getElementById('status');
                            const progressElement = loadingWindow.document.getElementById('progress');
                            
                            if (statusElement) {
                                statusElement.textContent = task.message || '处理中...';
                            }
                            if (progressElement) {
                                progressElement.textContent = `进度: ${task.progress}%`;
                            }
                        } catch (e) {
                            // 忽略跨域错误
                        }
                    }
                    
                    // 检查任务状态
                    if (task.status === 'completed') {
                        // 任务完成
                        console.log('Task completed:', task);
                        ButtonStateManager.setSuccess(btnElement, '分析完成');
                        showToast(`${analysisType}分析完成，正在打开结果页面...`, 'success');

                        // 获取结果并重定向
                        console.log('Fetching result for task:', taskId);
                        const resultResponse = await fetch(`/api/async/task/${taskId}/result`);
                        const resultData = await resultResponse.json();
                        console.log('Result data:', resultData);

                        if (resultData.success && resultData.redirect_url) {
                            console.log('Redirecting to:', resultData.redirect_url);
                            // 跳转到结果页面
                            if (loadingWindow && !loadingWindow.closed) {
                                loadingWindow.location.href = resultData.redirect_url;
                            } else {
                                window.open(resultData.redirect_url, '_blank');
                            }
                        } else {
                            console.error('No redirect URL or failed result:', resultData);
                            showToast('获取结果失败，请刷新页面重试', 'error');
                        }
                        return;
                        
                    } else if (task.status === 'failed') {
                        // 任务失败
                        ButtonStateManager.setError(btnElement, '分析失败');
                        showToast(`${analysisType}分析失败: ${task.error}`, 'error');
                        
                        if (loadingWindow && !loadingWindow.closed) {
                            loadingWindow.close();
                        }
                        return;
                        
                    } else if (task.status === 'running' || task.status === 'pending') {
                        // 任务仍在进行中，继续轮询
                        pollCount++;
                        if (pollCount < maxPolls) {
                            setTimeout(poll, pollInterval);
                        } else {
                            // 超时
                            ButtonStateManager.setError(btnElement, '超时');
                            showToast(`${analysisType}分析超时，请稍后重试`, 'error');
                            
                            if (loadingWindow && !loadingWindow.closed) {
                                loadingWindow.close();
                            }
                        }
                    }
                } else {
                    // 获取状态失败
                    throw new Error(data.error || '获取任务状态失败');
                }
                
            } catch (error) {
                console.error('轮询任务状态失败:', error);
                pollCount++;
                
                if (pollCount < maxPolls) {
                    // 继续重试
                    setTimeout(poll, pollInterval);
                } else {
                    // 彻底失败
                    ButtonStateManager.setError(btnElement, '网络错误');
                    showToast(`${analysisType}分析出现网络错误，请重试`, 'error');
                    
                    if (loadingWindow && !loadingWindow.closed) {
                        loadingWindow.close();
                    }
                }
            }
        };
        
        // 开始轮询
        setTimeout(poll, pollInterval);
    }

    // 全局变量存储当前查询和会话ID
    let currentQuery = '';
    let currentSessionId = '';

    // 历史会话管理功能已移至侧边栏

    // displayHistoryModal函数已删除，使用侧边栏替代

    // generateSessionsList和旧的restoreSession函数已删除，使用侧边栏新函数替代

    async function downloadSessionExcel(sessionId) {
        try {
            const response = await fetch('/api/export/excel', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'sid': sessionId
                },
                body: JSON.stringify({
                    query: '历史会话导出',
                    data_type: 'filtered'
                })
            });

            if (response.ok) {
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `session_${sessionId}_${new Date().toISOString().slice(0, 10)}.xlsx`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                window.URL.revokeObjectURL(url);

                showToast('Excel文件下载已开始', 'success');
            } else {
                showToast('Excel导出失败', 'error');
            }
        } catch (error) {
            console.error('Excel导出失败:', error);
            showToast('Excel导出失败', 'error');
        }
    }

    async function downloadSessionWord(sessionId) {
        try {
            const response = await fetch('/api/export/word', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'sid': sessionId
                },
                body: JSON.stringify({
                    query: '历史会话导出',
                    data_type: 'filtered'
                })
            });

            if (response.ok) {
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `session_${sessionId}_${new Date().toISOString().slice(0, 10)}.docx`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                window.URL.revokeObjectURL(url);

                showToast('Word文件下载已开始', 'success');
            } else {
                showToast('Word导出失败', 'error');
            }
        } catch (error) {
            console.error('Word导出失败:', error);
            showToast('Word导出失败', 'error');
        }
    }

    // 旧的deleteSession函数已删除，使用deleteSessionFromSidebar替代

    // Toast提示功能
    function showToast(message, type = 'info') {
        const toastContainer = document.getElementById('toast-container') || createToastContainer();

        const toast = document.createElement('div');
        toast.className = `toast align-items-center text-white bg-${type === 'success' ? 'success' : type === 'error' ? 'danger' : 'primary'} border-0`;
        toast.setAttribute('role', 'alert');
        toast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">${message}</div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        `;

        toastContainer.appendChild(toast);
        const bsToast = new bootstrap.Toast(toast);
        bsToast.show();

        toast.addEventListener('hidden.bs.toast', () => {
            toast.remove();
        });
    }

    function createToastContainer() {
        const container = document.createElement('div');
        container.id = 'toast-container';
        container.className = 'toast-container position-fixed bottom-0 end-0 p-3';
        document.body.appendChild(container);
        return container;
    }

    // 侧边栏管理功能
    let currentActiveSession = null;

    // 页面加载时初始化侧边栏
    document.addEventListener('DOMContentLoaded', function() {
        loadHistorySessions();
    });

    // 新建检索会话
    function createNewSession() {
        // 清空当前搜索结果
        const resultsContainer = document.getElementById('results-container');
        if (resultsContainer) {
            resultsContainer.innerHTML = '';
        }

        // 隐藏统计信息
        const searchStats = document.getElementById('search-stats');
        if (searchStats) {
            searchStats.style.display = 'none';
        }

        // 清空搜索框
        const searchInput = document.getElementById('search-input');
        if (searchInput) {
            searchInput.value = '';
        }

        // 清空检索策略
        const strategyTextarea = document.getElementById('search-strategy');
        if (strategyTextarea) {
            strategyTextarea.value = '';
        }

        // 隐藏检索策略容器
        const strategyContainer = document.getElementById('search-strategy-container');
        if (strategyContainer) {
            strategyContainer.style.display = 'none';
        }

        // 重置全局变量
        currentQuery = '';
        currentSessionId = '';
        currentActiveSession = null;

        // 清除当前导出文件
        window.currentExportFiles = {};

        // 移除所有会话的选中状态
        document.querySelectorAll('.session-item').forEach(item => {
            item.classList.remove('active');
        });

        // 关闭侧边栏
        toggleHistorySidebar();

        // 显示成功提示
        showToast('已创建新检索会话，可以开始新的文献搜索', 'success');

        // 聚焦到搜索框
        if (searchInput) {
            searchInput.focus();
        }
    }

    // 切换历史会话侧边栏显示/隐藏
    function toggleHistorySidebar() {
        const sidebar = document.getElementById('historySidebar');
        const overlay = document.getElementById('sidebarOverlay');
        const mainContent = document.getElementById('mainContent');

        const isShowing = sidebar.classList.contains('show');

        if (isShowing) {
            // 隐藏侧边栏
            sidebar.classList.remove('show');
            overlay.classList.remove('show');
            if (window.innerWidth >= 992) {
                mainContent.style.marginLeft = '0';
            }
        } else {
            // 显示侧边栏
            sidebar.classList.add('show');
            overlay.classList.add('show');
            if (window.innerWidth >= 992) {
                mainContent.style.marginLeft = '300px';
            }
        }
    }

    // 加载历史会话列表
    async function loadHistorySessions() {
        try {
            const response = await fetch('/api/sessions');
            const data = await response.json();

            if (data.success) {
                displaySessionsList(data.sessions);
            } else {
                console.error('获取历史会话失败:', data.error);
            }
        } catch (error) {
            console.error('获取历史会话失败:', error);
        }
    }

    // 显示会话列表
    function displaySessionsList(sessions) {
        const sessionsList = document.getElementById('sessionsList');

        if (sessions.length === 0) {
            sessionsList.innerHTML = `
                <div class="text-center text-muted py-3">
                    <i class="fas fa-clock me-1"></i>
                    <div>暂无历史会话</div>
                </div>
            `;
            return;
        }

        const sessionsHtml = sessions.map(session => {
            const date = new Date(session.timestamp).toLocaleDateString('zh-CN');
            const time = new Date(session.timestamp).toLocaleTimeString('zh-CN', {
                hour: '2-digit',
                minute: '2-digit'
            });

            return `
                <div class="session-item" data-session-id="${session.session_id}" onclick="selectSession('${session.session_id}')">
                    <div class="session-title">${session.query}</div>
                    <div class="session-meta">
                        <span><i class="fas fa-file-alt me-1"></i>${session.paper_count} 篇</span>
                        <span>${date} ${time}</span>
                    </div>
                    <div class="session-actions" onclick="event.stopPropagation()">
                        <button class="btn btn-sm btn-success" onclick="downloadSessionExcel('${session.session_id}')" title="导出Excel">
                            <i class="fas fa-file-excel"></i>
                        </button>
                        <button class="btn btn-sm btn-info" onclick="downloadSessionWord('${session.session_id}')" title="导出Word">
                            <i class="fas fa-file-word"></i>
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="deleteSessionFromSidebar('${session.session_id}')" title="删除">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            `;
        }).join('');

        sessionsList.innerHTML = sessionsHtml;
    }

    // 选择会话（高亮显示并自动恢复内容）
    async function selectSession(sessionId) {
        // 移除之前的选中状态
        document.querySelectorAll('.session-item').forEach(item => {
            item.classList.remove('active');
        });

        // 添加当前选中状态
        const selectedItem = document.querySelector(`[data-session-id="${sessionId}"]`);
        if (selectedItem) {
            selectedItem.classList.add('active');
            currentActiveSession = sessionId;
        }

        // 自动恢复该会话的内容
        try {
            console.log('开始恢复会话:', sessionId);
            const response = await fetch(`/api/sessions/${sessionId}/restore`, {
                method: 'POST'
            });
            console.log('响应状态:', response.status);

            const data = await response.json();
            console.log('响应数据:', data);

            if (data.success) {
                // 更新当前会话信息
                currentSessionId = sessionId;
                currentQuery = data.data.query;

                // 显示恢复的文献数据
                const papers = data.data.papers || [];
                console.log('恢复的文献数量:', papers.length);

                displaySearchResults(papers, {
                    searchStrategy: data.data.query,
                    totalCount: papers.length,
                    filteredCount: papers.length
                });

                // 显示成功提示
                showToast(`已切换到会话: ${data.data.query}`, 'success');
            } else {
                console.error('会话恢复失败:', data.error);
                showToast(`切换会话失败: ${data.error}`, 'error');
                // 如果失败，移除选中状态
                if (selectedItem) {
                    selectedItem.classList.remove('active');
                }
            }
        } catch (error) {
            console.error('切换会话异常:', error);
            showToast('切换会话失败', 'error');
            // 如果失败，移除选中状态
            if (selectedItem) {
                selectedItem.classList.remove('active');
            }
        }
    }

    // 从侧边栏恢复会话
    async function restoreSessionFromSidebar(sessionId) {
        try {
            const response = await fetch(`/api/sessions/${sessionId}/restore`, {
                method: 'POST'
            });
            const data = await response.json();

            if (data.success) {
                // 更新当前会话信息
                currentSessionId = sessionId;
                currentQuery = data.data.query;

                // 显示恢复的文献数据
                const papers = data.data.papers || [];
                displaySearchResults(papers, {
                    searchStrategy: data.data.query,
                    totalCount: papers.length,
                    filteredCount: papers.length
                });

                // 选中当前会话
                selectSession(sessionId);

                showToast('会话已恢复', 'success');
            } else {
                showToast(`恢复会话失败: ${data.error}`, 'error');
            }
        } catch (error) {
            console.error('恢复会话失败:', error);
            showToast('恢复会话失败', 'error');
        }
    }

    // 从侧边栏删除会话
    async function deleteSessionFromSidebar(sessionId) {
        if (!confirm('确定要删除这个会话吗？删除后无法恢复。')) {
            return;
        }

        try {
            const response = await fetch(`/api/sessions/${sessionId}`, {
                method: 'DELETE'
            });
            const data = await response.json();

            if (data.success) {
                showToast('会话已删除', 'success');
                // 重新加载历史会话列表
                loadHistorySessions();

                // 如果删除的是当前选中的会话，清除选中状态
                if (currentActiveSession === sessionId) {
                    currentActiveSession = null;
                }
            } else {
                showToast(`删除会话失败: ${data.error}`, 'error');
            }
        } catch (error) {
            console.error('删除会话失败:', error);
            showToast('删除会话失败', 'error');
        }
    }

    // 搜索会话
    function searchSessions() {
        const searchTerm = document.getElementById('sessionSearch').value.toLowerCase();
        const sessionItems = document.querySelectorAll('.session-item');

        sessionItems.forEach(item => {
            const title = item.querySelector('.session-title').textContent.toLowerCase();
            if (title.includes(searchTerm)) {
                item.style.display = 'block';
            } else {
                item.style.display = 'none';
            }
        });
    }

    // 清空所有历史会话
    async function clearAllSessions() {
        if (!confirm('确定要清空所有历史会话吗？此操作无法恢复。')) {
            return;
        }

        try {
            // 获取所有会话ID
            const response = await fetch('/api/sessions');
            const data = await response.json();

            if (data.success && data.sessions.length > 0) {
                // 逐个删除会话
                const deletePromises = data.sessions.map(session =>
                    fetch(`/api/sessions/${session.session_id}`, { method: 'DELETE' })
                );

                await Promise.all(deletePromises);

                showToast('所有历史会话已清空', 'success');
                loadHistorySessions();
                currentActiveSession = null;
            } else {
                showToast('没有可清空的会话', 'info');
            }
        } catch (error) {
            console.error('清空历史会话失败:', error);
            showToast('清空历史会话失败', 'error');
        }
    }

    // 监听搜索框输入
    document.addEventListener('DOMContentLoaded', function() {
        const searchInput = document.getElementById('sessionSearch');
        if (searchInput) {
            searchInput.addEventListener('input', searchSessions);
        }
    });

    // 移除原来的模态框相关函数，因为我们现在使用侧边栏
    // showHistoryModal, displayHistoryModal, generateSessionsList 等函数已不需要

    // 文献追踪功能
    // 更新种子文献选择器
    function updateSeedPaperSelector() {
        const selector = document.getElementById('seed-paper-select');
        const generateBtn = document.getElementById('generate-network-btn');

        if (!selector || !generateBtn) return;

        // 清空现有选项
        selector.innerHTML = '<option value="">请先进行文献检索，然后选择种子文献</option>';
        generateBtn.disabled = true;

        // 如果有搜索结果，添加到选择器
        if (currentPapers && currentPapers.length > 0) {
            selector.innerHTML = '<option value="">请选择种子文献</option>';

            currentPapers.slice(0, 50).forEach(paper => { // 只显示前50篇
                const option = document.createElement('option');
                option.value = paper.pmid || paper.title;
                option.textContent = `${paper.title.substring(0, 80)}... (${paper.pub_year})`;
                selector.appendChild(option);
            });

            // 启用选择器事件
            selector.addEventListener('change', function() {
                generateBtn.disabled = !this.value;
            });
        }
    }

    // 生成网络图谱
    const generateNetworkBtn = document.getElementById('generate-network-btn');
    if (generateNetworkBtn) {
        generateNetworkBtn.addEventListener('click', function() {
            const trackingInput = document.getElementById('tracking-input');
            const userInput = trackingInput.value.trim();

            if (!userInput) {
                showToast('请输入DOI、PMID或文献标题', 'warning');
                return;
            }

            // 开始生成网络图谱
            generateCitationNetwork(userInput);
        });
    }

    // 生成文献引用网络图谱
    async function generateCitationNetwork(userInput) {
        const loadingDiv = document.getElementById('tracking-loading');
        const networkDiv = document.getElementById('network-display-area');
        const progressBar = document.getElementById('tracking-progress-bar');
        const statusText = document.getElementById('tracking-status-text');

        try {
            // 显示加载状态
            loadingDiv.style.display = 'block';
            networkDiv.style.display = 'none';
            progressBar.style.width = '10%';
            statusText.textContent = '正在解析文献信息...';

            // 发送请求
            const response = await fetch('/api/citation-network', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    input: userInput
                })
            });

            const result = await response.json();

            if (result.success) {
                // 更新进度
                progressBar.style.width = '100%';
                statusText.textContent = '网络图谱生成完成！';

                // 延迟一下再显示结果
                setTimeout(() => {
                    loadingDiv.style.display = 'none';
                    displayCitationNetwork(result.data);
                }, 1000);

                showToast(result.message, 'success');
            } else {
                throw new Error(result.error || '生成失败');
            }

        } catch (error) {
            console.error('生成网络图谱失败:', error);
            loadingDiv.style.display = 'none';
            showToast('生成失败: ' + error.message, 'error');
        }
    }

    // 显示文献引用网络图谱
    function displayCitationNetwork(networkData) {
        const networkDiv = document.getElementById('network-display-area');
        const container = document.getElementById('citation-network-container');

        // 显示网络区域
        networkDiv.style.display = 'block';

        // 生成HTML内容
        const htmlContent = generateNetworkHTML(networkData);
        container.innerHTML = htmlContent;

        // 生成侧边栏内容
        setTimeout(() => {
            generateSidebarContent(networkData);
            initVisNetwork(networkData);
        }, 100);

        // 滚动到网络图谱
        networkDiv.scrollIntoView({ behavior: 'smooth' });
    }

    // 生成侧边栏内容
    function generateSidebarContent(networkData) {
        // 分类节点并按不同阈值过滤：祖先文献≥40%，后代文献≥53%
        const references = networkData.nodes
            .filter(node => node.node_type === 'reference')
            .filter(node => !node.similarity_score || node.similarity_score >= 0.4);

        const citations = networkData.nodes
            .filter(node => node.node_type === 'citation')
            .filter(node => !node.similarity_score || node.similarity_score >= 0.53);

        // 按相关度排序 - 相关度高的在上面
        references.sort((a, b) => {
            const scoreA = a.similarity_score || 0;
            const scoreB = b.similarity_score || 0;
            return scoreB - scoreA; // 降序排列，相关度高的在前
        });

        citations.sort((a, b) => {
            const scoreA = a.similarity_score || 0;
            const scoreB = b.similarity_score || 0;
            return scoreB - scoreA; // 降序排列，相关度高的在前
        });

        // 生成引用文献列表
        const referencesContainer = document.getElementById('references-list');
        if (referencesContainer) {
            referencesContainer.innerHTML = generatePapersList(references, 'references');
        }

        // 生成被引文献列表
        const citationsContainer = document.getElementById('citations-list');
        if (citationsContainer) {
            citationsContainer.innerHTML = generatePapersList(citations, 'citations');
        }
    }

    // 生成网络图谱HTML
    function generateNetworkHTML(networkData) {
        return `
            <div style="display: flex; height: 100%; background: white;">
                <!-- 左侧边栏：引用文献 -->
                <div style="width: 300px; background: #f8f9fa; overflow-y: auto; padding: 20px; border-right: 1px solid #e9ecef;">
                    <h5 style="text-align: center; color: #495057; margin-bottom: 20px; padding-bottom: 10px; border-bottom: 2px solid #007bff;">
                        📚 引用文献 (Prior Works)
                    </h5>
                    <div id="references-list">
                        <!-- 引用文献列表将在这里动态生成 -->
                    </div>
                </div>

                <!-- 中间网络图谱 -->
                <div style="flex: 1; position: relative;">
                    <div style="position: absolute; top: 20px; left: 20px; right: 20px; z-index: 1000; background: rgba(255, 255, 255, 0.95); padding: 15px 20px; border-radius: 10px; box-shadow: 0 4px 12px rgba(0,0,0,0.1);">
                        <h4 style="margin: 0 0 5px 0; color: #212529;">文献引用网络图谱</h4>
                        <p style="margin: 0; color: #6c757d; font-size: 14px;">基于 Connected Papers 风格的可视化分析</p>
                    </div>

                    <div id="vis-network" style="width: 100%; height: 100%;"></div>

                    <div style="position: absolute; bottom: 20px; left: 20px; background: rgba(255, 255, 255, 0.95); padding: 15px; border-radius: 10px; box-shadow: 0 4px 12px rgba(0,0,0,0.1);">
                        <h6 style="margin-bottom: 10px; color: #495057;">图例</h6>
                        <div style="display: flex; align-items: center; margin-bottom: 5px; font-size: 12px;">
                            <div style="width: 12px; height: 12px; border-radius: 50%; background: #ff6b6b; margin-right: 8px;"></div>
                            <span>中心文献</span>
                        </div>
                        <div style="display: flex; align-items: center; margin-bottom: 5px; font-size: 12px;">
                            <div style="width: 12px; height: 12px; border-radius: 50%; background: #4ecdc4; margin-right: 8px;"></div>
                            <span>引用文献</span>
                        </div>
                        <div style="display: flex; align-items: center; font-size: 12px;">
                            <div style="width: 12px; height: 12px; border-radius: 50%; background: #45b7d1; margin-right: 8px;"></div>
                            <span>被引文献</span>
                        </div>
                    </div>

                    <div style="position: absolute; bottom: 20px; right: 20px; background: rgba(255, 255, 255, 0.95); padding: 15px; border-radius: 10px; box-shadow: 0 4px 12px rgba(0,0,0,0.1); text-align: center;">
                        <h6 style="margin-bottom: 10px; color: #495057;">网络统计</h6>
                        <div style="font-size: 12px; margin-bottom: 3px; color: #6c757d;">节点数: <span style="font-weight: 600; color: #007bff;">${networkData.nodes.length}</span></div>
                        <div style="font-size: 12px; margin-bottom: 3px; color: #6c757d;">连接数: <span style="font-weight: 600; color: #007bff;">${networkData.edges.length}</span></div>
                        <div style="font-size: 12px; color: #6c757d;">密度: <span style="font-weight: 600; color: #007bff;">${(2 * networkData.edges.length / (networkData.nodes.length * (networkData.nodes.length - 1))).toFixed(3)}</span></div>
                    </div>
                </div>

                <!-- 右侧边栏：被引文献 -->
                <div style="width: 300px; background: #f8f9fa; overflow-y: auto; padding: 20px; border-left: 1px solid #e9ecef;">
                    <h5 style="text-align: center; color: #495057; margin-bottom: 20px; padding-bottom: 10px; border-bottom: 2px solid #007bff;">
                        🔗 被引文献 (Derivative Works)
                    </h5>
                    <div id="citations-list">
                        <!-- 被引文献列表将在这里动态生成 -->
                    </div>
                </div>
            </div>
        `;
    }

    // 生成文献列表HTML - 增强版，显示更丰富信息
    function generatePapersList(papers, listType = '') {
        if (!papers || papers.length === 0) {
            return '<div style="text-align: center; color: #6c757d; padding: 20px;">暂无数据</div>';
        }

        // 注意：papers已经在调用前按时间排序了，这里不再重新排序

        return papers.map((paper, index) => {
            const authors = paper.authors || [];
            const authorsText = authors.length > 0 ? authors.slice(0, 3).join(', ') + (authors.length > 3 ? ' et al.' : '') : '未知作者';

            // 处理摘要 - 截取前200字符
            const abstract = paper.abstract || paper.summary || '';
            const truncatedAbstract = abstract.length > 200 ? abstract.substring(0, 200) + '...' : abstract;

            // 处理期刊信息
            const journal = paper.journal || '未知期刊';
            const impactFactor = paper.impact_factor || paper.if || 'N/A';
            const jcrQuartile = paper.jcr_quartile || paper.quartile || '';
            const casQuartile = paper.cas_quartile || '';

            // 生成期刊信息标签
            const journalInfo = `
                <div style="display: flex; align-items: center; gap: 6px; margin: 8px 0; flex-wrap: wrap;">
                    <span style="color: #495057; font-size: 12px; font-weight: 500;">${journal}</span>
                    ${impactFactor !== 'N/A' && impactFactor !== '' ? `<span style="background: #28a745; color: white; padding: 2px 6px; border-radius: 4px; font-size: 10px; font-weight: 600;">IF: ${impactFactor}</span>` : ''}
                    ${jcrQuartile && jcrQuartile !== 'N/A' ? `<span style="background: #007bff; color: white; padding: 2px 6px; border-radius: 4px; font-size: 10px; font-weight: 600;">JCR: ${jcrQuartile}</span>` : ''}
                    ${casQuartile && casQuartile !== 'N/A' ? `<span style="background: #6f42c1; color: white; padding: 2px 6px; border-radius: 4px; font-size: 10px; font-weight: 600;">CAS: ${casQuartile}</span>` : ''}
                </div>
            `;

            // 生成摘要部分
            const abstractSection = abstract ? `
                <div style="margin: 10px 0; padding: 8px; background: #f8f9fa; border-radius: 6px; border-left: 3px solid #007bff;">
                    <div style="font-size: 11px; color: #6c757d; margin-bottom: 4px; font-weight: 600;">摘要</div>
                    <div style="font-size: 12px; color: #495057; line-height: 1.4;">
                        ${truncatedAbstract}
                    </div>
                </div>
            ` : '';

            // 生成DOI链接
            const doiSection = paper.doi ? `
                <div style="margin-top: 8px;">
                    <a href="https://doi.org/${paper.doi}" target="_blank"
                       style="color: #007bff; text-decoration: none; font-size: 11px; font-weight: 500;"
                       onmouseover="this.style.textDecoration='underline'"
                       onmouseout="this.style.textDecoration='none'">
                        🔗 DOI: ${paper.doi}
                    </a>
                </div>
            ` : '';

            return `
                <div style="background: white; border: 1px solid #e9ecef; border-radius: 12px; padding: 16px; margin-bottom: 16px; cursor: pointer; transition: all 0.3s ease; box-shadow: 0 2px 8px rgba(0,0,0,0.08);"
                     onmouseover="this.style.transform='translateY(-3px)'; this.style.boxShadow='0 6px 20px rgba(0,0,0,0.15)'; this.style.borderColor='#007bff';"
                     onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.08)'; this.style.borderColor='#e9ecef';"
                     onclick="showPaperDetails('${paper.id}')"
                     data-paper-id="${paper.id}">

                    <!-- 时间排序标号 -->
                    <div style="position: absolute; top: 12px; right: 12px; background: #28a745; color: white; width: 28px; height: 28px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 11px; font-weight: 600; box-shadow: 0 2px 4px rgba(0,0,0,0.2);" title="按发表时间排序 (最新在前)">
                        ${index + 1}
                    </div>

                    <!-- 标题 -->
                    <div style="font-weight: 600; color: #212529; font-size: 15px; line-height: 1.4; margin-bottom: 10px; padding-right: 30px;">
                        ${paper.full_title || paper.label || '未知标题'}
                    </div>

                    <!-- 作者信息 -->
                    <div style="color: #6c757d; font-size: 13px; margin-bottom: 8px; font-weight: 500;">
                        👥 ${authorsText}
                    </div>

                    <!-- 期刊信息 -->
                    ${journalInfo}

                    <!-- 摘要 -->
                    ${abstractSection}

                    <!-- 底部信息栏 -->
                    <div style="margin-top: 12px; padding-top: 10px; border-top: 1px solid #e9ecef;">
                        <!-- 信息标签行 -->
                        <div style="display: flex; gap: 8px; align-items: center; flex-wrap: wrap; margin-bottom: 8px;">
                            <span style="background: #e9ecef; color: #495057; padding: 3px 8px; border-radius: 6px; font-size: 11px; font-weight: 600;">
                                📅 ${paper.year || '未知年份'}
                            </span>
                            <span style="color: #007bff; font-weight: 600; font-size: 12px;">
                                📊 被引 ${paper.citation_count || 0} 次
                            </span>
                            ${paper.similarity_score !== undefined ? `
                                <span style="background: #28a745; color: white; padding: 3px 8px; border-radius: 6px; font-size: 11px; font-weight: 600;" title="与种子文献的相关度">
                                    🎯 相关度 ${(paper.similarity_score * 100).toFixed(1)}%
                                </span>
                            ` : ''}
                        </div>

                        <!-- 操作按钮行 -->
                        <div style="display: flex; justify-content: flex-end; gap: 6px;">
                            <button onclick="event.stopPropagation(); showPaperDetails('${paper.id}')"
                                    style="background: #17a2b8; border: 1px solid #17a2b8; border-radius: 4px; padding: 4px 8px; font-size: 10px; color: white; cursor: pointer; transition: all 0.2s;"
                                    onmouseover="this.style.background='#138496'"
                                    onmouseout="this.style.background='#17a2b8'"
                                    title="查看详细信息">
                                📖
                            </button>
                            <button onclick="event.stopPropagation(); focusOnNode('${paper.id}')"
                                    style="background: #ffc107; border: 1px solid #ffc107; border-radius: 4px; padding: 4px 8px; font-size: 10px; color: #212529; cursor: pointer; transition: all 0.2s;"
                                    onmouseover="this.style.background='#e0a800'"
                                    onmouseout="this.style.background='#ffc107'"
                                    title="在图谱中定位">
                                🎯
                            </button>
                            <button onclick="event.stopPropagation(); copyPaperInfo('${paper.id}')"
                                    style="background: #6c757d; border: 1px solid #6c757d; border-radius: 4px; padding: 4px 8px; font-size: 10px; color: white; cursor: pointer; transition: all 0.2s;"
                                    onmouseover="this.style.background='#5a6268'"
                                    onmouseout="this.style.background='#6c757d'"
                                    title="复制文献信息">
                                📋
                            </button>
                            ${paper.doi ? `
                                <button onclick="event.stopPropagation(); openPaperLink('${paper.doi}', 'doi')"
                                        style="background: #007bff; border: 1px solid #007bff; border-radius: 4px; padding: 4px 8px; font-size: 10px; color: white; cursor: pointer; transition: all 0.2s;"
                                        onmouseover="this.style.background='#0056b3'"
                                        onmouseout="this.style.background='#007bff'"
                                        title="打开DOI链接">
                                    🔗
                                </button>
                            ` : ''}
                            ${paper.pmid ? `
                                <button onclick="event.stopPropagation(); openPaperLink('${paper.pmid}', 'pubmed')"
                                        style="background: #28a745; border: 1px solid #28a745; border-radius: 4px; padding: 4px 8px; font-size: 10px; color: white; cursor: pointer; transition: all 0.2s;"
                                        onmouseover="this.style.background='#1e7e34'"
                                        onmouseout="this.style.background='#28a745'"
                                        title="打开PubMed链接">
                                    🏥
                                </button>
                            ` : ''}
                        </div>
                    </div>

                    <!-- DOI信息 -->
                    ${doiSection}
                </div>
            `;
        }).join('');
    }

    // 初始化vis.js网络图谱
    function initVisNetwork(networkData) {
        console.log('🎯 开始初始化网络图谱', networkData);

        const container = document.getElementById('vis-network');
        if (!container) {
            console.error('❌ vis-network容器不存在');
            return;
        }

        if (typeof vis === 'undefined') {
            console.error('❌ vis.js库未加载');
            showToast('网络图谱库加载失败，请刷新页面重试', 'error');
            return;
        }

        // 验证数据
        if (!networkData || !networkData.nodes || !networkData.edges) {
            console.error('❌ 网络数据无效', networkData);
            showToast('网络数据无效', 'error');
            return;
        }

        console.log(`📊 原始网络数据: ${networkData.nodes.length} 个节点, ${networkData.edges.length} 条边`);

        // 按不同阈值过滤节点（保留中心节点）
        const filteredNodes = networkData.nodes.filter(node => {
            if (node.node_type === 'center') {
                return true; // 始终保留中心节点
            }
            if (node.node_type === 'reference') {
                return !node.similarity_score || node.similarity_score >= 0.4; // 祖先文献≥40%
            }
            if (node.node_type === 'citation') {
                return !node.similarity_score || node.similarity_score >= 0.53; // 后代文献≥53%
            }
            return true;
        });

        // 获取过滤后的节点ID集合
        const filteredNodeIds = new Set(filteredNodes.map(node => node.id));

        // 过滤相关的边（只保留连接到过滤后节点的边）
        const filteredEdges = networkData.edges.filter(edge => {
            return filteredNodeIds.has(edge.from) && filteredNodeIds.has(edge.to);
        });

        console.log(`📊 过滤后网络数据: ${filteredNodes.length} 个节点, ${filteredEdges.length} 条边`);

        // 创建数据集
        const nodes = new vis.DataSet(filteredNodes);
        const edges = new vis.DataSet(filteredEdges);

        // 预设节点位置 - 实现左中右布局，智能分布（使用过滤后的节点）
        // 按相关度排序，相关度高的排在前面（靠近中心）
        const referenceNodes = filteredNodes
            .filter(node => node.node_type === 'reference')
            .sort((a, b) => (b.similarity_score || 0) - (a.similarity_score || 0));
        const citationNodes = filteredNodes
            .filter(node => node.node_type === 'citation')
            .sort((a, b) => (b.similarity_score || 0) - (a.similarity_score || 0));

        const positionedNodes = nodes.map(node => {
            const nodeData = { ...node };

            // 根据影响因子调整节点大小 - 直接成正比
            const impactFactor = node.impact_factor || 0;
            let nodeSize;

            if (impactFactor === 0) {
                nodeSize = 8; // 无影响因子的默认大小
            } else {
                // 影响因子和节点大小成正比
                // 基础大小8 + 影响因子 * 4
                const baseSize = 8;
                const scaleFactor = 4; // 每1分影响因子增加4像素
                const maxSize = 120; // 设置最大节点大小，避免过大

                nodeSize = Math.min(baseSize + impactFactor * scaleFactor, maxSize);
            }

            nodeData.size = nodeSize;

            // 设置节点标题和悬停信息（纯文本格式）
            nodeData.title = `${node.full_title || node.title || node.label}
被引次数: ${node.citation_count || 0}
影响因子: ${node.impact_factor || 'N/A'}
JCR分区: ${node.jcr_quartile || 'N/A'}
中科院分区: ${node.cas_quartile || 'N/A'}
期刊: ${node.journal || 'Unknown'}
年份: ${node.year || 'N/A'}
相关度: ${((node.similarity_score || 0) * 100).toFixed(1)}%`;

            // 存储完整信息用于点击事件
            nodeData.fullInfo = {
                title: node.full_title || node.title || node.label,
                citation_count: node.citation_count || 0,
                impact_factor: node.impact_factor || 'N/A',
                jcr_quartile: node.jcr_quartile || 'N/A',
                cas_quartile: node.cas_quartile || 'N/A',
                journal: node.journal || 'Unknown',
                year: node.year || 'N/A',
                similarity: ((node.similarity_score || 0) * 100).toFixed(1),
                pmid: node.pmid,
                node_type: node.node_type
            };

            if (node.node_type === 'center') {
                // 中心文献放在中间，使用特殊大小
                nodeData.x = 0;
                nodeData.y = 0;
                // 中心节点也根据影响因子调整大小，但保证最小为30
                nodeData.size = Math.max(nodeSize, 30);
                nodeData.fixed = { x: true, y: true }; // 完全固定中心位置
                // 中心节点保持原有颜色（通常是橙色或特殊颜色）
            } else if (node.node_type === 'reference') {
                // 参考文献分布在左侧扇形区域，根据相关度调整距离
                const index = referenceNodes.findIndex(n => n.pmid === node.pmid || n.id === node.id);
                if (index === -1) {
                    console.log('Reference node not found:', node.pmid || node.id);
                    return nodeData; // 如果找不到，跳过定位
                }
                const totalRefs = referenceNodes.length;
                const similarity = node.similarity_score || 0.5;

                // 根据相关度调整基础距离
                const baseDistance = 300 + (1 - similarity) * 600; // 300-900px范围

                // 计算所有参考文献节点的最大半径
                const maxNodeRadius = Math.max(...referenceNodes.map(n => {
                    const impactFactor = n.impact_factor || 0;
                    const size = impactFactor === 0 ? 8 : Math.min(8 + impactFactor * 4, 120);
                    return size / 2;
                }));

                // 确保节点圆形之间不重叠：计算所需的最小角度间距
                const nodeRadius = nodeSize / 2; // 当前节点半径
                const safetyMargin = 10; // 安全边距
                const minArcDistance = (nodeRadius + maxNodeRadius + safetyMargin) * 2; // 两个最大节点之间的最小弧长
                const minAngleStep = (minArcDistance * 180) / (Math.PI * baseDistance); // 转换为角度

                // 计算实际角度步长：确保不小于最小角度间距
                const idealAngleStep = 180 / (totalRefs + 1);
                const angleStep = Math.max(idealAngleStep, minAngleStep);

                // 如果角度步长太大，增加距离来容纳所有节点
                let distance = baseDistance;
                if (angleStep * totalRefs > 180) {
                    // 需要增加距离来容纳所有节点
                    distance = (minArcDistance * totalRefs * 180) / (Math.PI * 180);
                }

                // 参考文献：从270°开始顺时针分布
                let angle = 270 + (index + 1) * angleStep;

                // 处理角度超过360°的情况
                if (angle >= 360) {
                    angle = angle - 360;
                }

                console.log(`参考文献 ${index + 1}/${totalRefs}: 角度=${angle}°, 相关度=${similarity}, 距离=${distance}`);
                const angleRad = (angle * Math.PI) / 180;

                // 计算精确位置，不添加随机偏移以避免重叠
                nodeData.x = Math.cos(angleRad) * distance;
                nodeData.y = Math.sin(angleRad) * distance;

                nodeData.fixed = { x: true, y: true }; // 固定位置

                // 参考文献使用红色，根据相关度区间调整深浅
                let bgOpacity, borderOpacity;
                const similarityPercent = similarity * 100; // 转换为百分比

                if (similarityPercent >= 81) {
                    // 81%以上：深红色，完全不透明
                    bgOpacity = 1.0;
                    borderOpacity = 1.0;
                } else if (similarityPercent >= 74) {
                    // 74-81%：中深红色
                    bgOpacity = 0.85;
                    borderOpacity = 0.9;
                } else if (similarityPercent >= 67) {
                    // 67-74%：中红色
                    bgOpacity = 0.7;
                    borderOpacity = 0.8;
                } else if (similarityPercent >= 60) {
                    // 60-67%：中浅红色
                    bgOpacity = 0.55;
                    borderOpacity = 0.7;
                } else {
                    // 53-60%：浅红色，最浅
                    bgOpacity = 0.4;
                    borderOpacity = 0.6;
                }

                nodeData.color = {
                    background: `rgba(231, 76, 60, ${bgOpacity})`,  // 红色背景，透明度根据相关度
                    border: `rgba(192, 57, 43, ${borderOpacity})`,  // 深红色边框
                    highlight: {
                        background: `rgba(236, 112, 99, ${Math.min(1, bgOpacity + 0.2)})`,
                        border: `rgba(169, 50, 38, ${Math.min(1, borderOpacity + 0.2)})`
                    },
                    hover: {
                        background: `rgba(236, 112, 99, ${Math.min(1, bgOpacity + 0.2)})`,
                        border: `rgba(169, 50, 38, ${Math.min(1, borderOpacity + 0.2)})`
                    }
                };
            } else if (node.node_type === 'citation') {
                // 被引文献分布在右侧扇形区域，根据相关度调整距离
                const index = citationNodes.findIndex(n => n.pmid === node.pmid || n.id === node.id);
                if (index === -1) {
                    console.log('Citation node not found:', node.pmid || node.id);
                    return nodeData; // 如果找不到，跳过定位
                }
                const totalCits = citationNodes.length;
                const similarity = node.similarity_score || 0.5;

                // 根据相关度调整基础距离
                const baseDistance = 300 + (1 - similarity) * 600; // 300-900px范围

                // 计算所有被引文献节点的最大半径
                const maxNodeRadius = Math.max(...citationNodes.map(n => {
                    const impactFactor = n.impact_factor || 0;
                    const size = impactFactor === 0 ? 8 : Math.min(8 + impactFactor * 4, 120);
                    return size / 2;
                }));

                // 确保节点圆形之间不重叠：计算所需的最小角度间距
                const nodeRadius = nodeSize / 2; // 当前节点半径
                const safetyMargin = 10; // 安全边距
                const minArcDistance = (nodeRadius + maxNodeRadius + safetyMargin) * 2; // 两个最大节点之间的最小弧长
                const minAngleStep = (minArcDistance * 180) / (Math.PI * baseDistance); // 转换为角度

                // 计算实际角度步长：确保不小于最小角度间距
                const idealAngleStep = 180 / (totalCits + 1);
                const angleStep = Math.max(idealAngleStep, minAngleStep);

                // 如果角度步长太大，增加距离来容纳所有节点
                let distance = baseDistance;
                if (angleStep * totalCits > 180) {
                    // 需要增加距离来容纳所有节点
                    distance = (minArcDistance * totalCits * 180) / (Math.PI * 180);
                }

                // 引用文献（被引文献）：从90°开始顺时针分布
                const angle = 90 + (index + 1) * angleStep; // 从90°开始

                console.log(`被引文献 ${index + 1}/${totalCits}: 角度=${angle}°, 相关度=${similarity}, 距离=${distance}`);
                const angleRad = (angle * Math.PI) / 180;

                // 计算精确位置，不添加随机偏移以避免重叠
                nodeData.x = Math.cos(angleRad) * distance;
                nodeData.y = Math.sin(angleRad) * distance;

                nodeData.fixed = { x: true, y: true }; // 固定位置

                // 被引文献使用蓝色，根据相关度区间调整深浅
                let bgOpacity, borderOpacity;
                const similarityPercent = similarity * 100; // 转换为百分比

                if (similarityPercent >= 81) {
                    // 81%以上：深蓝色，完全不透明
                    bgOpacity = 1.0;
                    borderOpacity = 1.0;
                } else if (similarityPercent >= 74) {
                    // 74-81%：中深蓝色
                    bgOpacity = 0.85;
                    borderOpacity = 0.9;
                } else if (similarityPercent >= 67) {
                    // 67-74%：中蓝色
                    bgOpacity = 0.7;
                    borderOpacity = 0.8;
                } else if (similarityPercent >= 60) {
                    // 60-67%：中浅蓝色
                    bgOpacity = 0.55;
                    borderOpacity = 0.7;
                } else {
                    // 53-60%：浅蓝色，最浅
                    bgOpacity = 0.4;
                    borderOpacity = 0.6;
                }

                nodeData.color = {
                    background: `rgba(52, 152, 219, ${bgOpacity})`,  // 蓝色背景，透明度根据相关度
                    border: `rgba(41, 128, 185, ${borderOpacity})`,  // 深蓝色边框
                    highlight: {
                        background: `rgba(93, 173, 226, ${Math.min(1, bgOpacity + 0.2)})`,
                        border: `rgba(31, 97, 141, ${Math.min(1, borderOpacity + 0.2)})`
                    },
                    hover: {
                        background: `rgba(93, 173, 226, ${Math.min(1, bgOpacity + 0.2)})`,
                        border: `rgba(31, 97, 141, ${Math.min(1, borderOpacity + 0.2)})`
                    }
                };

                if (totalCits === 1) {
                    nodeData.y = 0;
                } else {
                    const spacing = Math.min(80, 400 / Math.max(totalCits - 1, 1));
                    nodeData.y = (index - (totalCits - 1) / 2) * spacing;
                }

                nodeData.fixed = { x: true, y: true }; // 固定位置
            }

            return nodeData;
        });

        // 更新数据集
        nodes.clear();
        nodes.add(positionedNodes);

        // 网络配置
        const options = {
            nodes: {
                shape: 'dot',
                font: {
                    size: 14,
                    color: '#2c3e50',
                    face: 'Segoe UI, Arial, sans-serif',
                    strokeWidth: 1,
                    strokeColor: '#ffffff'
                },
                borderWidth: 3,
                borderWidthSelected: 4,
                shadow: {
                    enabled: true,
                    color: 'rgba(0,0,0,0.3)',
                    size: 8,
                    x: 3,
                    y: 3
                },
                scaling: {
                    min: 15,
                    max: 40,
                    label: {
                        enabled: true,
                        min: 12,
                        max: 18
                    }
                },
                chosen: {
                    node: function(values, id, selected, hovering) {
                        values.borderWidth = 5;
                        values.shadow = true;
                        values.shadowSize = 12;
                    }
                }
            },
            edges: {
                arrows: {
                    to: {
                        enabled: true,
                        scaleFactor: 1.0,
                        type: 'arrow'
                    }
                },
                color: {
                    color: 'rgba(127, 140, 157, 0.3)',  // 半透明灰色
                    highlight: 'rgba(52, 152, 219, 0.6)',
                    hover: 'rgba(231, 76, 60, 0.6)',
                    opacity: 0.3
                },
                width: 1,  // 细线
                widthConstraint: {
                    minimum: 2,
                    maximum: 5
                },
                smooth: {
                    enabled: true,
                    type: 'cubicBezier',
                    forceDirection: 'horizontal',
                    roundness: 0.3
                },
                shadow: {
                    enabled: false  // 禁用阴影，让线条更简洁
                },
                chosen: {
                    edge: function(values, id, selected, hovering) {
                        values.width = 5;
                        values.shadow = true;
                        values.shadowSize = 6;
                    }
                }
            },
            physics: {
                enabled: false  // 禁用物理引擎，使用我们的固定位置
            },
            layout: {
                improvedLayout: false, // 禁用自动布局，使用我们的预设位置
                randomSeed: 42
            },
            interaction: {
                hover: true,
                tooltipDelay: 200,
                hideEdgesOnDrag: false,
                hideNodesOnDrag: false
            }
        };

        // 创建网络
        try {
            const network = new vis.Network(container, { nodes: nodes, edges: edges }, options);
            console.log('✅ 网络图谱创建成功');

            // 添加事件监听器
            network.on('click', function(params) {
                if (params.nodes.length > 0) {
                    const nodeId = params.nodes[0];
                    const nodeData = nodes.get(nodeId);
                    console.log('🖱️ 点击节点:', nodeId, nodeData);

                    // 显示详细信息弹窗
                    if (nodeData && nodeData.fullInfo) {
                        showNodeDetails(nodeData.fullInfo);
                    }

                    // 滑动到对应文献
                    highlightPaperInSidebar(nodeId);
                }
            });

            // 添加悬停工具提示
            network.on('hoverNode', function(params) {
                const nodeId = params.node;
                const nodeData = networkData.nodes.find(n => n.id === nodeId);
                if (nodeData) {
                    const tooltip = `
                        <div style="max-width: 300px; padding: 10px; background: rgba(0,0,0,0.9); color: white; border-radius: 8px; font-size: 12px;">
                            <strong>${nodeData.full_title || nodeData.label}</strong><br>
                            <span style="color: #3498db;">被引次数: ${nodeData.citation_count || 0}</span><br>
                            <span style="color: #e74c3c;">影响因子: ${nodeData.impact_factor || 'N/A'}</span><br>
                            <span style="color: #f39c12;">期刊: ${nodeData.journal || 'Unknown'}</span><br>
                            <span style="color: #95a5a6;">年份: ${nodeData.year || 'N/A'}</span>
                        </div>
                    `;
                    // 这里可以添加自定义工具提示显示逻辑
                }
            });

            network.on('blurNode', function(params) {
                // 隐藏工具提示
            });

            // 添加稳定化监听器
            network.on('stabilizationProgress', function(params) {
                console.log('🔄 网络稳定化进度:', Math.round(params.iterations / params.total * 100) + '%');
            });

        // 网络稳定后调整视图以显示完整布局
        network.once('stabilizationIterationsDone', () => {
            // 适应整个网络视图
            network.fit({
                animation: {
                    duration: 1500,
                    easingFunction: 'easeInOutQuad'
                }
            });

            // 稍后聚焦到中心，但保持能看到左右两侧
            setTimeout(() => {
                const centerNode = networkData.nodes.find(node => node.node_type === 'center');
                if (centerNode) {
                    network.focus(centerNode.id, {
                        scale: 0.8, // 稍微缩小一点，确保能看到左右两侧
                        animation: {
                            duration: 1000,
                            easingFunction: 'easeInOutQuad'
                        }
                    });
                }
            }, 1000);
        });

            // 保存网络实例和数据供其他函数使用
            window.currentNetwork = network;
            window.currentNetworkData = networkData;

        } catch (error) {
            console.error('❌ 创建网络图谱失败:', error);
            showToast('网络图谱创建失败: ' + error.message, 'error');
        }
    }

    // 聚焦到指定节点
    function focusOnNode(nodeId) {
        if (window.currentNetwork) {
            window.currentNetwork.focus(nodeId, {
                scale: 1.5,
                animation: {
                    duration: 1000,
                    easingFunction: 'easeInOutQuad'
                }
            });
            highlightPaperInSidebar(nodeId);
        }
    }

    // 复制文献信息到剪贴板
    function copyPaperInfo(paperId) {
        // 从当前网络数据中找到对应文献
        if (!window.currentNetworkData) {
            showToast('无法获取文献信息', 'error');
            return;
        }

        const paper = window.currentNetworkData.nodes.find(node => node.id === paperId);
        if (!paper) {
            showToast('文献信息不存在', 'error');
            return;
        }

        // 构建文献信息文本
        const authors = paper.authors ? paper.authors.join(', ') : '未知作者';
        const journal = paper.journal || '未知期刊';
        const year = paper.year || '未知年份';
        const impactFactor = paper.impact_factor || paper.if || 'N/A';
        const citationCount = paper.citation_count || 0;
        const doi = paper.doi || '';

        let paperInfo = `标题: ${paper.full_title || paper.label || '未知标题'}\n`;
        paperInfo += `作者: ${authors}\n`;
        paperInfo += `期刊: ${journal}\n`;
        paperInfo += `年份: ${year}\n`;
        paperInfo += `影响因子: ${impactFactor}\n`;
        paperInfo += `被引次数: ${citationCount}\n`;
        if (doi) {
            paperInfo += `DOI: ${doi}\n`;
        }
        if (paper.abstract || paper.summary) {
            paperInfo += `摘要: ${paper.abstract || paper.summary}\n`;
        }

        // 复制到剪贴板
        navigator.clipboard.writeText(paperInfo).then(() => {
            showToast('文献信息已复制到剪贴板', 'success');
        }).catch(err => {
            console.error('复制失败:', err);
            showToast('复制失败，请手动选择文本', 'error');
        });
    }

    // 显示文献详细信息模态框
    function showPaperDetails(paperId) {
        if (!window.currentNetworkData) {
            showToast('无法获取文献信息', 'error');
            return;
        }

        const paper = window.currentNetworkData.nodes.find(node => node.id === paperId);
        if (!paper) {
            showToast('文献信息不存在', 'error');
            return;
        }

        // 创建模态框
        const modal = document.createElement('div');
        modal.id = 'paper-details-modal';
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.7);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10000;
            animation: fadeIn 0.3s ease;
        `;

        // 处理数据
        const authors = paper.authors ? paper.authors.join(', ') : '未知作者';
        const journal = paper.journal || '未知期刊';
        const year = paper.year || '未知年份';
        const impactFactor = paper.impact_factor || paper.if || 'N/A';
        const citationCount = paper.citation_count || 0;
        const abstract = paper.abstract || paper.summary || '暂无摘要信息';
        const doi = paper.doi || '';
        const pmid = paper.pmid || '';
        const quartile = paper.quartile || paper.jcr_quartile || '';

        // 模态框内容
        modal.innerHTML = `
            <div style="background: white; border-radius: 16px; max-width: 800px; max-height: 90vh; overflow-y: auto; box-shadow: 0 20px 60px rgba(0,0,0,0.3); animation: slideUp 0.3s ease;">
                <!-- 头部 -->
                <div style="padding: 24px 32px 16px; border-bottom: 2px solid #f8f9fa; position: sticky; top: 0; background: white; border-radius: 16px 16px 0 0;">
                    <div style="display: flex; justify-content: space-between; align-items: flex-start;">
                        <h2 style="margin: 0; color: #2c3e50; font-size: 20px; font-weight: 700; line-height: 1.3; flex: 1; padding-right: 20px;">
                            ${paper.full_title || paper.label || '未知标题'}
                        </h2>
                        <button onclick="closePaperDetails()"
                                style="background: #e74c3c; color: white; border: none; border-radius: 50%; width: 36px; height: 36px; cursor: pointer; font-size: 18px; display: flex; align-items: center; justify-content: center; transition: all 0.2s;"
                                onmouseover="this.style.background='#c0392b'"
                                onmouseout="this.style.background='#e74c3c'"
                                title="关闭">
                            ×
                        </button>
                    </div>
                </div>

                <!-- 内容区域 -->
                <div style="padding: 24px 32px;">
                    <!-- 基本信息 -->
                    <div style="margin-bottom: 24px;">
                        <h3 style="color: #34495e; font-size: 16px; margin: 0 0 12px 0; font-weight: 600;">📋 基本信息</h3>
                        <div style="background: #f8f9fa; padding: 16px; border-radius: 8px; border-left: 4px solid #3498db;">
                            <div style="margin-bottom: 8px;"><strong>作者：</strong> ${authors}</div>
                            <div style="margin-bottom: 8px;"><strong>期刊：</strong> ${journal}</div>
                            <div style="margin-bottom: 8px;"><strong>发表年份：</strong> ${year}</div>
                            <div style="display: flex; gap: 16px; margin-bottom: 8px;">
                                <span><strong>影响因子：</strong> <span style="color: #27ae60; font-weight: 600;">${impactFactor}</span></span>
                                ${quartile ? `<span><strong>分区：</strong> <span style="color: #f39c12; font-weight: 600;">${quartile}</span></span>` : ''}
                                <span><strong>被引次数：</strong> <span style="color: #e74c3c; font-weight: 600;">${citationCount}</span></span>
                            </div>
                            ${doi ? `<div style="margin-bottom: 8px;"><strong>DOI：</strong> <a href="https://doi.org/${doi}" target="_blank" style="color: #3498db; text-decoration: none;">${doi}</a></div>` : ''}
                            ${pmid ? `<div><strong>PMID：</strong> <a href="https://pubmed.ncbi.nlm.nih.gov/${pmid}/" target="_blank" style="color: #27ae60; text-decoration: none;">${pmid}</a></div>` : ''}
                        </div>
                    </div>

                    <!-- 摘要 -->
                    <div style="margin-bottom: 24px;">
                        <h3 style="color: #34495e; font-size: 16px; margin: 0 0 12px 0; font-weight: 600;">📄 摘要</h3>
                        <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #9b59b6; line-height: 1.6; color: #2c3e50;">
                            ${abstract}
                        </div>
                    </div>

                    <!-- 操作按钮 -->
                    <div style="display: flex; gap: 12px; justify-content: center; padding-top: 16px; border-top: 1px solid #ecf0f1;">
                        <button onclick="copyPaperInfo('${paper.id}')"
                                style="background: #34495e; color: white; border: none; padding: 12px 24px; border-radius: 8px; cursor: pointer; font-weight: 600; transition: all 0.2s;"
                                onmouseover="this.style.background='#2c3e50'"
                                onmouseout="this.style.background='#34495e'">
                            📋 复制信息
                        </button>
                        <button onclick="focusOnNode('${paper.id}'); closePaperDetails();"
                                style="background: #f39c12; color: white; border: none; padding: 12px 24px; border-radius: 8px; cursor: pointer; font-weight: 600; transition: all 0.2s;"
                                onmouseover="this.style.background='#e67e22'"
                                onmouseout="this.style.background='#f39c12'">
                            🎯 图谱定位
                        </button>
                        ${doi ? `
                            <button onclick="openPaperLink('${doi}', 'doi')"
                                    style="background: #3498db; color: white; border: none; padding: 12px 24px; border-radius: 8px; cursor: pointer; font-weight: 600; transition: all 0.2s;"
                                    onmouseover="this.style.background='#2980b9'"
                                    onmouseout="this.style.background='#3498db'">
                                🔗 打开DOI
                            </button>
                        ` : ''}
                        ${pmid ? `
                            <button onclick="openPaperLink('${pmid}', 'pubmed')"
                                    style="background: #27ae60; color: white; border: none; padding: 12px 24px; border-radius: 8px; cursor: pointer; font-weight: 600; transition: all 0.2s;"
                                    onmouseover="this.style.background='#229954'"
                                    onmouseout="this.style.background='#27ae60'">
                                🏥 PubMed
                            </button>
                        ` : ''}
                    </div>
                </div>
            </div>
        `;

        // 添加CSS动画
        if (!document.getElementById('paper-modal-styles')) {
            const style = document.createElement('style');
            style.id = 'paper-modal-styles';
            style.textContent = `
                @keyframes fadeIn {
                    from { opacity: 0; }
                    to { opacity: 1; }
                }
                @keyframes fadeOut {
                    from { opacity: 1; }
                    to { opacity: 0; }
                }
                @keyframes slideUp {
                    from { transform: translateY(50px); opacity: 0; }
                    to { transform: translateY(0); opacity: 1; }
                }
            `;
            document.head.appendChild(style);
        }

        // 添加到页面
        document.body.appendChild(modal);

        // 点击背景关闭
        modal.addEventListener('click', function(e) {
            if (e.target === modal) {
                closePaperDetails();
            }
        });

        // ESC键关闭
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closePaperDetails();
            }
        });
    }

    // 关闭文献详情模态框
    function closePaperDetails() {
        const modal = document.getElementById('paper-details-modal');
        if (modal) {
            modal.style.animation = 'fadeOut 0.3s ease';
            setTimeout(() => {
                modal.remove();
            }, 300);
        }
    }

    // 打开文献链接
    function openPaperLink(identifier, type) {
        let url = '';

        switch (type) {
            case 'doi':
                url = `https://doi.org/${identifier}`;
                break;
            case 'pubmed':
                url = `https://pubmed.ncbi.nlm.nih.gov/${identifier}/`;
                break;
            default:
                showToast('未知的链接类型', 'error');
                return;
        }

        window.open(url, '_blank');
        showToast(`正在打开${type.toUpperCase()}链接...`, 'info');
    }

    // 显示节点详细信息弹窗
    function showNodeDetails(nodeInfo) {
        // 创建弹窗HTML
        const modalHtml = `
            <div class="modal fade" id="nodeDetailsModal" tabindex="-1" aria-hidden="true">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">文献详细信息</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                <div class="col-12">
                                    <h6 class="text-primary">${nodeInfo.title}</h6>
                                </div>
                            </div>
                            <div class="row mt-3">
                                <div class="col-md-6">
                                    <div class="info-item">
                                        <strong>被引次数:</strong>
                                        <span class="badge bg-info">${nodeInfo.citation_count}</span>
                                    </div>
                                    <div class="info-item mt-2">
                                        <strong>影响因子:</strong>
                                        <span class="badge bg-danger">${nodeInfo.impact_factor}</span>
                                    </div>
                                    <div class="info-item mt-2">
                                        <strong>JCR分区:</strong>
                                        <span class="badge bg-warning">${nodeInfo.jcr_quartile}</span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="info-item">
                                        <strong>中科院分区:</strong>
                                        <span class="badge bg-secondary">${nodeInfo.cas_quartile}</span>
                                    </div>
                                    <div class="info-item mt-2">
                                        <strong>期刊:</strong>
                                        <span class="text-muted">${nodeInfo.journal}</span>
                                    </div>
                                    <div class="info-item mt-2">
                                        <strong>年份:</strong>
                                        <span class="text-muted">${nodeInfo.year}</span>
                                    </div>
                                    <div class="info-item mt-2">
                                        <strong>相关度:</strong>
                                        <span class="badge bg-success">${nodeInfo.similarity}%</span>
                                    </div>
                                </div>
                            </div>
                            ${nodeInfo.pmid ? `
                            <div class="row mt-3">
                                <div class="col-12">
                                    <div class="info-item">
                                        <strong>PMID:</strong>
                                        <a href="https://pubmed.ncbi.nlm.nih.gov/${nodeInfo.pmid}" target="_blank" class="text-primary">${nodeInfo.pmid}</a>
                                    </div>
                                </div>
                            </div>
                            ` : ''}
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 移除已存在的弹窗
        const existingModal = document.getElementById('nodeDetailsModal');
        if (existingModal) {
            existingModal.remove();
        }

        // 添加新弹窗到页面
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // 显示弹窗
        const modal = new bootstrap.Modal(document.getElementById('nodeDetailsModal'));
        modal.show();
    }

    // 生成不同数据库的文献HTML
    function generatePaperHtml(paper, database) {
        if (database === 'pubmed') {
            // PubMed 格式
            const journalInfo = paper.journal_info || {};
            return `
                <div class="card mb-3">
                    <div class="card-body">
                        <h5 class="card-title">
                            <a href="https://pubmed.ncbi.nlm.nih.gov/${paper.pmid}" target="_blank" class="text-decoration-none">
                                ${paper.title}
                            </a>
                        </h5>
                        <p class="card-text text-muted mb-2">
                            <strong>作者:</strong> ${paper.authors || 'N/A'}<br>
                            <strong>期刊:</strong> ${paper.journal || 'N/A'}<br>
                            <strong>发表时间:</strong> ${paper.pub_date || 'N/A'}<br>
                            <div class="mt-2">
                                ${paper.relevance_score ? `<strong>相关度:</strong> <span class="badge bg-warning text-dark">${Math.round(paper.relevance_score * 100)}%</span>` : ''}
                                ${journalInfo.impact_factor ? `<strong class="ms-3">影响因子:</strong> <span class="badge bg-success">${journalInfo.impact_factor}</span>` : ''}
                                ${journalInfo.jcr_quartile ? `<strong class="ms-3">JCR分区:</strong> <span class="badge bg-primary">${journalInfo.jcr_quartile}</span>` : ''}
                                ${journalInfo.cas_quartile ? `<strong class="ms-3">中科院分区:</strong> <span class="badge bg-info">${journalInfo.cas_quartile}</span>` : ''}
                            </div>
                        </p>
                        <p class="card-text">${paper.abstract || '暂无摘要'}</p>
                        <div class="d-flex justify-content-between align-items-center">
                            <small class="text-muted">PMID: ${paper.pmid}</small>
                            <div>
                                ${paper.doi ? `<a href="https://doi.org/${paper.doi}" target="_blank" class="btn btn-sm btn-outline-primary me-2">DOI</a>` : ''}
                                <a href="https://pubmed.ncbi.nlm.nih.gov/${paper.pmid}" target="_blank" class="btn btn-sm btn-primary">查看详情</a>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        } else if (database === 'arxiv') {
            // arXiv 格式
            return `
                <div class="card mb-3">
                    <div class="card-body">
                        <h5 class="card-title">
                            <a href="${paper.url}" target="_blank" class="text-decoration-none">
                                ${paper.title}
                            </a>
                        </h5>
                        <p class="card-text text-muted mb-2">
                            <strong>作者:</strong> ${paper.author_list || 'N/A'}<br>
                            <strong>分类:</strong> ${paper.category_list || 'N/A'}<br>
                            <strong>发表时间:</strong> ${paper.publication_date || 'N/A'}<br>
                            ${paper.relevance_score ? `<strong>相关度:</strong> <span class="badge bg-warning text-dark">${Math.round(paper.relevance_score * 100)}%</span>` : ''}
                        </p>
                        <p class="card-text">${paper.abstract || '暂无摘要'}</p>
                        <div class="d-flex justify-content-between align-items-center">
                            <small class="text-muted">arXiv ID: ${paper.arxiv_id}</small>
                            <div>
                                ${paper.pdf_url ? `<a href="${paper.pdf_url}" target="_blank" class="btn btn-sm btn-outline-success me-2">PDF</a>` : ''}
                                ${paper.doi ? `<a href="https://doi.org/${paper.doi}" target="_blank" class="btn btn-sm btn-outline-primary me-2">DOI</a>` : ''}
                                <a href="${paper.url}" target="_blank" class="btn btn-sm btn-primary">查看详情</a>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        } else if (database === 'semantic_scholar') {
            // Semantic Scholar 格式
            return `
                <div class="card mb-3">
                    <div class="card-body">
                        <h5 class="card-title">
                            <a href="${paper.url}" target="_blank" class="text-decoration-none">
                                ${paper.title}
                            </a>
                        </h5>
                        <p class="card-text text-muted mb-2">
                            <strong>作者:</strong> ${paper.author_list || 'N/A'}<br>
                            <strong>期刊:</strong> ${paper.journal || 'N/A'}
                            ${paper.journal_info && paper.journal_info.impact_factor ? `(IF: ${paper.journal_info.impact_factor})` : ''}
                            ${paper.journal_info && paper.journal_info.jcr_quartile ? `[JCR: ${paper.journal_info.jcr_quartile}]` : ''}
                            ${paper.journal_info && paper.journal_info.cas_quartile ? `[中科院: ${paper.journal_info.cas_quartile}]` : ''}<br>
                            <strong>发表时间:</strong> ${paper.year || 'N/A'}
                            ${paper.field_list ? `<br><strong>研究领域:</strong> ${paper.field_list}` : ''}<br>
                            ${paper.relevance_score ? `<strong>相关度:</strong> <span class="badge bg-warning text-dark">${Math.round(paper.relevance_score * 100)}%</span>` : ''}
                        </p>
                        <p class="card-text">${paper.abstract || '暂无摘要'}</p>
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <small class="text-muted">被引次数: ${paper.citation_count || 0}</small>
                                ${paper.influential_citation_count ? `<small class="text-muted ms-2">有影响力引用: ${paper.influential_citation_count}</small>` : ''}
                            </div>
                            <div>
                                ${paper.pdf_url ? `<a href="${paper.pdf_url}" target="_blank" class="btn btn-sm btn-outline-success me-2">PDF</a>` : ''}
                                ${paper.doi ? `<a href="https://doi.org/${paper.doi}" target="_blank" class="btn btn-sm btn-outline-primary me-2">DOI</a>` : ''}
                                ${paper.pmid ? `<a href="https://pubmed.ncbi.nlm.nih.gov/${paper.pmid}" target="_blank" class="btn btn-sm btn-outline-info me-2">PubMed</a>` : ''}
                                <a href="${paper.url}" target="_blank" class="btn btn-sm btn-primary">查看详情</a>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        return '<div class="alert alert-warning">未知的数据库格式</div>';
    }

    // 在侧边栏中高亮文献
    function highlightPaperInSidebar(nodeId) {
        // 移除之前的高亮
        document.querySelectorAll('[onclick*="focusOnNode"]').forEach(item => {
            item.style.borderColor = '#e9ecef';
            item.style.background = 'white';
        });

        // 高亮当前项
        const targetItem = document.querySelector(`[onclick="focusOnNode('${nodeId}')"]`);
        if (targetItem) {
            targetItem.style.borderColor = '#007bff';
            targetItem.style.background = '#f8f9ff';
            targetItem.scrollIntoView({ behavior: 'smooth', block: 'center' });
            console.log('✅ 已滑动到对应文献位置');
        } else {
            console.log('⚠️ 未找到对应的文献项');
        }
    }

    // 文献溯源功能
    let currentVerificationTaskId = null;

    // 开始匹配按钮事件
    const startVerificationBtn = document.getElementById('start-verification-btn');
    if (startVerificationBtn) {
        startVerificationBtn.addEventListener('click', startReferenceMatching);
    }

    async function startReferenceMatching() {
        const textContent = document.getElementById('verification-input').value.trim();

        if (!textContent) {
            showToast('请输入需要匹配参考文献的内容', 'warning');
            return;
        }

        // 隐藏之前的结果和错误
        document.getElementById('verification-results').style.display = 'none';
        document.getElementById('verification-error').style.display = 'none';

        // 显示进度
        document.getElementById('verification-progress').style.display = 'block';
        startVerificationBtn.disabled = true;
        startVerificationBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>匹配中...';

        try {
            // 启动匹配任务
            const response = await fetch('/api/reference_matching', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    text_content: textContent
                })
            });

            const result = await response.json();

            if (!result.success) {
                throw new Error(result.error || '启动匹配任务失败');
            }

            currentVerificationTaskId = result.task_id;

            // 开始轮询任务状态
            pollVerificationTaskStatus();

        } catch (error) {
            console.error('匹配失败:', error);
            showVerificationError(error.message);
            resetVerificationUI();
        }
    }

    async function pollVerificationTaskStatus() {
        if (!currentVerificationTaskId) return;

        try {
            const response = await fetch(`/api/async/task/${currentVerificationTaskId}/status`);
            const statusData = await response.json();

            // 从API响应中获取任务信息
            const taskInfo = statusData.task || {};

            // 更新进度
            updateVerificationProgress(taskInfo.progress || 0, taskInfo.message || '处理中...');

            if (taskInfo.status === 'completed') {
                // 直接从taskInfo中获取结果
                console.log('任务完成，状态数据:', taskInfo);

                if (taskInfo.result) {
                    showVerificationResults(taskInfo.result);
                } else {
                    // 如果taskInfo中没有结果，再尝试获取
                    try {
                        const resultResponse = await fetch(`/api/async/task/${currentVerificationTaskId}/result`);
                        const resultData = await resultResponse.json();

                        if (resultData.success && resultData.result) {
                            showVerificationResults(resultData.result);
                        } else {
                            throw new Error('无法获取任务结果');
                        }
                    } catch (resultError) {
                        console.error('获取结果失败:', resultError);
                        showVerificationError('任务完成但无法获取结果');
                    }
                }
                resetVerificationUI();
            } else if (taskInfo.status === 'failed') {
                throw new Error(taskInfo.error || '任务执行失败');
            } else {
                // 继续轮询
                setTimeout(pollVerificationTaskStatus, 2000);
            }

        } catch (error) {
            console.error('获取任务状态失败:', error);
            showVerificationError(error.message);
            resetVerificationUI();
        }
    }

    function updateVerificationProgress(percent, message) {
        document.getElementById('verification-progress-bar').style.width = percent + '%';
        document.getElementById('verification-progress-percent').textContent = percent + '%';
        document.getElementById('verification-progress-message').textContent = message;
    }

    function showVerificationResults(data) {
        document.getElementById('verification-progress').style.display = 'none';

        // 保存当前的验证参考文献数据供导出使用
        currentVerificationReferences = data.references || [];
        console.log('保存验证参考文献数据，数量:', currentVerificationReferences.length);

        // 创建结果HTML
        const resultsHtml = createVerificationResultsHTML(data);
        document.getElementById('verification-results').innerHTML = resultsHtml;
        document.getElementById('verification-results').style.display = 'block';
    }

    function createVerificationResultsHTML(data) {
        let html = '';

        // 验证摘要
        html += `
            <div class="alert alert-info mb-4">
                <h6 class="alert-heading mb-2">
                    <i class="fas fa-info-circle me-2"></i>验证结果摘要
                </h6>
                <p class="mb-0">${data.summary || '验证完成'}</p>
            </div>
        `;

        // 统计信息
        html += `
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-chart-bar me-2"></i>验证统计</h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-4">
                            <div class="p-3 bg-primary bg-opacity-10 rounded">
                                <div class="h4 text-primary mb-1">${data.total_count || 0}</div>
                                <div class="small text-muted">总文献数</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="p-3 bg-success bg-opacity-10 rounded">
                                <div class="h4 text-success mb-1">${data.valid_count || 0}</div>
                                <div class="small text-muted">通过验证</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="p-3 bg-danger bg-opacity-10 rounded">
                                <div class="h4 text-danger mb-1">${data.invalid_count || 0}</div>
                                <div class="small text-muted">验证失败</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // AI生成的带引用文本
        if (data.ai_response) {
            html += createGeneratedTextHTML(data.ai_response, data.references);
        }

        // 参考文献列表
        if (data.references && data.references.length > 0) {
            html += `
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <div class="d-flex align-items-center">
                            <h6 class="mb-0"><i class="fas fa-list me-2"></i>检测到的参考文献</h6>
                            <small class="text-muted ms-2">共 ${data.references.length} 篇</small>
                        </div>
                        <div class="d-flex align-items-center gap-2">
                            <small class="text-muted me-2">
                                <i class="fas fa-info-circle me-1"></i>NBIB格式可导入EndNote、Zotero
                            </small>
                            <button onclick="exportVerificationToNBIB()" class="btn btn-success btn-sm">
                                <i class="fas fa-download me-1"></i>导出NBIB文件
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
            `;

            data.references.forEach((ref, index) => {
                html += createReferenceCardHTML(ref, index + 1);
            });

            html += `
                    </div>
                </div>
            `;
        } else {
            html += `
                <div class="card">
                    <div class="card-body text-center text-muted">
                        <i class="fas fa-search fa-2x mb-3"></i>
                        <p>未检测到参考文献</p>
                    </div>
                </div>
            `;
        }

        return html;
    }

    function createGeneratedTextHTML(aiResponse, references) {
        if (!aiResponse) return '';

        // 提取AI响应中的主要文本部分（去掉参考文献列表部分）
        let mainText = aiResponse;
        const refSectionIndex = aiResponse.indexOf('参考文献：');
        if (refSectionIndex !== -1) {
            mainText = aiResponse.substring(0, refSectionIndex).trim();
        }

        // 处理文本，为引用序号添加点击功能
        let processedText = mainText.replace(/\[(\d+)\]/g, (match, number) => {
            const refIndex = parseInt(number) - 1;
            if (references && references[refIndex]) {
                return `<span class="badge bg-primary text-decoration-none" style="cursor: pointer;" onclick="scrollToVerificationReference(${number})" title="点击查看文献详情">${match}</span>`;
            }
            return match;
        });

        return `
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-file-text me-2"></i>带引用的学术文本</h6>
                </div>
                <div class="card-body">
                    <div class="p-3 bg-light rounded border-start border-primary border-4">
                        <div class="text-dark">${processedText}</div>
                    </div>
                    <div class="mt-3 text-muted small">
                        <i class="fas fa-info-circle me-1"></i>
                        <strong>说明：</strong>上述文本中的序号[1][2][3]等对应下方的参考文献列表，点击序号可快速定位到对应文献。
                    </div>
                </div>
            </div>
        `;
    }

    function createReferenceCardHTML(ref, index) {
        const verified = ref.verified;
        const statusClass = ref.status_class || (verified ? 'success' : 'danger');
        const verificationStatus = ref.verification_status || (verified ? '✅ 已验证' : '❌ 验证失败');
        const cardBorderClass = verified ? 'border-success' : 'border-danger';

        return `
            <div class="card mb-3 verification-reference-card ${cardBorderClass}" id="verification-ref-${index}">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start mb-3">
                        <h6 class="card-title mb-0">${index}. ${ref.sentence || '检测到的引用内容'}</h6>
                        <span class="badge bg-${statusClass}">${verificationStatus}</span>
                    </div>

                    <div class="row g-2 small">
                        <div class="col-12"><strong>文献标题:</strong> ${ref.title || '未提供'}</div>
                        <div class="col-12"><strong>作者:</strong> ${Array.isArray(ref.authors) ? ref.authors.join(', ') : (ref.authors || '未提供')}</div>
                        <div class="col-12"><strong>期刊:</strong> ${ref.journal || '未提供'}</div>
                        <div class="col-12"><strong>发表年份:</strong> ${ref.year || '未提供'}</div>
                        ${ref.pmid ? `<div class="col-12"><strong>PMID:</strong> <a href="https://pubmed.ncbi.nlm.nih.gov/${ref.pmid}/" target="_blank" class="text-primary">${ref.pmid}</a></div>` : ''}
                        ${ref.doi ? `<div class="col-12"><strong>DOI:</strong> <a href="https://doi.org/${ref.doi}" target="_blank" class="text-primary">${ref.doi}</a></div>` : ''}
                        ${ref.citation_count ? `<div class="col-12"><strong>引用次数:</strong> ${ref.citation_count}</div>` : ''}
                        ${ref.abstract ? `<div class="col-12"><strong>摘要:</strong> ${ref.abstract}</div>` : ''}
                        ${ref.selection_reason ? `<div class="col-12"><strong>选择理由:</strong> ${ref.selection_reason}</div>` : ''}
                        ${ref.relevance ? `<div class="col-12"><strong>相关性:</strong> ${ref.relevance}</div>` : ''}
                        ${ref.error ? `<div class="col-12 text-danger"><strong>验证错误:</strong> ${ref.error}</div>` : ''}
                    </div>

                    ${verified ? `
                        <div class="mt-3 p-2 bg-success bg-opacity-10 border border-success rounded small text-success">
                            <i class="fas fa-check-circle me-1"></i>
                            <strong>验证通过：</strong>该文献在学术数据库中存在，可以安全引用。
                        </div>
                    ` : `
                        <div class="mt-3 p-2 bg-danger bg-opacity-10 border border-danger rounded small text-danger">
                            <i class="fas fa-exclamation-triangle me-1"></i>
                            <strong>验证失败：</strong>该文献在学术数据库中未找到，可能是虚假文献，请谨慎使用。
                        </div>
                    `}
                </div>
            </div>
        `;
    }

    function scrollToVerificationReference(number) {
        // 滚动到对应的参考文献
        const referenceCard = document.getElementById(`verification-ref-${number}`);
        if (referenceCard) {
            referenceCard.scrollIntoView({
                behavior: 'smooth',
                block: 'center'
            });

            // 高亮显示对应的文献卡片
            referenceCard.style.backgroundColor = '#fff3cd';
            setTimeout(() => {
                referenceCard.style.backgroundColor = '';
            }, 2000);
        }
    }

    function showVerificationError(message) {
        document.getElementById('verification-progress').style.display = 'none';
        document.getElementById('verification-error').style.display = 'block';
        document.getElementById('verification-error-message').textContent = message;
    }

    function resetVerificationUI() {
        startVerificationBtn.disabled = false;
        startVerificationBtn.innerHTML = '<i class="fas fa-search me-2"></i>开始匹配';
        currentVerificationTaskId = null;
    }

    // 全局变量存储当前的验证参考文献数据
    let currentVerificationReferences = [];

    // 导出NBIB文件函数
    function exportVerificationToNBIB() {
        console.log('exportVerificationToNBIB called, currentVerificationReferences:', currentVerificationReferences);
        if (!currentVerificationReferences || currentVerificationReferences.length === 0) {
            alert('没有可导出的参考文献');
            return;
        }

        // 检查是否有验证成功的文献
        const verifiedRefs = currentVerificationReferences.filter(ref => ref.verified);
        if (verifiedRefs.length === 0) {
            alert('没有验证成功的文献可导出');
            return;
        }

        // 显示加载状态
        const button = event.target;
        const originalText = button.innerHTML;
        button.innerHTML = `
            <i class="fas fa-spinner fa-spin me-1"></i>导出中...
        `;
        button.disabled = true;

        // 发送导出请求
        fetch('/api/export/nbib', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                references: currentVerificationReferences
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 创建下载链接
                const blob = new Blob([data.content], { type: 'text/plain;charset=utf-8' });
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = data.filename;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                window.URL.revokeObjectURL(url);

                // 显示成功消息
                showToast('NBIB文件导出成功！', 'success');
            } else {
                showToast('导出失败: ' + data.error, 'error');
            }
        })
        .catch(error => {
            console.error('Export error:', error);
            showToast('导出失败: ' + error.message, 'error');
        })
        .finally(() => {
            // 恢复按钮状态
            button.innerHTML = originalText;
            button.disabled = false;
        });
    }

    </script>

    </div> <!-- container-fluid main-content-shift -->
</body>
</html>
