#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
输入 DOI / PMID / 标题 → 生成交互式引用图谱
"""
import re
import time
import requests
import networkx as nx
from pyvis.network import Network
import urllib3

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

HEADERS = {"User-Agent": "Mozilla/5.0 (CitationGraph/1.0)"}
TIMEOUT = 30  # 增加超时时间
MAX_NODES = 10  # 增加节点数量以获得更丰富的图谱
MAX_RETRIES = 3  # 最大重试次数

# 影响因子颜色映射
def get_if_color(impact_factor):
    """根据影响因子返回颜色深浅"""
    if impact_factor is None or impact_factor == 0:
        return "#808080"  # 灰色 - 无数据
    elif impact_factor < 2:
        return "#E3F2FD"  # 浅蓝色 - 低影响因子
    elif impact_factor < 5:
        return "#2196F3"  # 蓝色 - 中等影响因子
    elif impact_factor < 10:
        return "#1976D2"  # 深蓝色 - 高影响因子
    else:
        return "#0D47A1"  # 深蓝色 - 顶级影响因子

def get_citation_size(citation_count):
    """根据被引次数返回节点大小"""
    if citation_count is None or citation_count == 0:
        return 15  # 最小尺寸
    elif citation_count < 10:
        return 20
    elif citation_count < 50:
        return 25
    elif citation_count < 100:
        return 30
    elif citation_count < 500:
        return 35
    else:
        return 40  # 最大尺寸


# ---------- 网络请求 ----------
def safe_get(url, params=None):
    for attempt in range(MAX_RETRIES):
        try:
            print(f"   🌐 请求 {url} (尝试 {attempt + 1}/{MAX_RETRIES})")
            r = requests.get(
                url,
                headers=HEADERS,
                params=params,
                timeout=TIMEOUT,
                verify=False,
            )
            if r.status_code == 200:
                print(f"   ✅ 请求成功")
                return r.json()
            else:
                print(f"   ⚠️ HTTP {r.status_code}")
        except Exception as e:
            print(f"   ❌ 尝试 {attempt + 1} 失败：{str(e)[:60]}")
            if attempt < MAX_RETRIES - 1:
                time.sleep(2)  # 重试前等待2秒
    return None


# ---------- 输入解析 ----------
def parse_input(user_in: str):
    user_in = user_in.strip()

    # 1) 直接 DOI
    if re.match(r"^10\.\d+/.+", user_in):
        return user_in

    # 2) PMID → DOI
    if user_in.isdigit():
        url = f"https://api.ncbi.nlm.nih.gov/lit/ctxp/v1/pubmed/?format=json&id={user_in}"
        data = safe_get(url)
        if data and "records" in data:
            return data["records"][0]["doi"]
        raise ValueError(f"无法解析 PMID {user_in}")

    # 3) 标题 → DOI
    url = "https://api.crossref.org/works"
    params = {"query.title": user_in, "rows": 1, "select": "DOI"}
    data = safe_get(url, params=params)
    if data and data["message"]["items"]:
        return data["message"]["items"][0]["DOI"]
    raise ValueError("标题未匹配任何文献")


# ---------- 元数据获取 ----------
def get_crossref_work(doi):
    """获取Crossref基本元数据"""
    url = f"https://api.crossref.org/works/{doi}"
    data = safe_get(url)
    return data["message"] if data else None

def get_semantic_scholar_data(doi):
    """获取Semantic Scholar的详细数据（包含被引次数）"""
    url = f"https://api.semanticscholar.org/graph/v1/paper/DOI:{doi}"
    params = {"fields": "paperId,title,citationCount,influentialCitationCount,year,journal,authors"}
    data = safe_get(url, params=params)
    return data if data else None

def get_impact_factor(journal_name):
    """获取期刊影响因子（这里需要你的本地影响因子匹配脚本）"""
    # TODO: 集成你的本地影响因子匹配脚本
    # 这里先返回模拟数据，你可以替换为实际的影响因子查询逻辑

    # 模拟一些常见期刊的影响因子
    if_mapping = {
        "Nature": 49.962,
        "Science": 47.728,
        "Cell": 41.582,
        "The Lancet": 79.321,
        "New England Journal of Medicine": 91.245,
        "International Journal of Molecular Sciences": 5.923,
        "PLOS ONE": 3.240,
        "Scientific Reports": 4.379,
        "Frontiers": 3.201,
        "BMC": 2.967
    }

    if journal_name:
        # 简单的模糊匹配
        for journal, if_value in if_mapping.items():
            if journal.lower() in journal_name.lower():
                return if_value

    # 如果没有匹配到，返回随机值用于演示
    import random
    return random.uniform(1.0, 10.0)

def get_enhanced_metadata(doi):
    """获取增强的文献元数据（包含被引次数和影响因子）"""
    print(f"   📊 获取增强元数据: {doi}")

    # 获取基本信息
    crossref_data = get_crossref_work(doi)
    semantic_data = get_semantic_scholar_data(doi)

    if not crossref_data:
        return None

    # 提取基本信息
    title = crossref_data.get("title", ["Unknown"])[0]
    journal_name = ""
    if "container-title" in crossref_data:
        journal_name = crossref_data["container-title"][0] if crossref_data["container-title"] else ""

    # 获取被引次数
    citation_count = 0
    if semantic_data:
        citation_count = semantic_data.get("citationCount", 0)

    # 获取影响因子
    impact_factor = get_impact_factor(journal_name)

    # 获取年份
    year = None
    try:
        if "published-print" in crossref_data and crossref_data["published-print"]:
            date_parts = crossref_data["published-print"].get("date-parts", [])
            if date_parts and len(date_parts) > 0 and len(date_parts[0]) > 0:
                year = date_parts[0][0]
        elif "published-online" in crossref_data and crossref_data["published-online"]:
            date_parts = crossref_data["published-online"].get("date-parts", [])
            if date_parts and len(date_parts) > 0 and len(date_parts[0]) > 0:
                year = date_parts[0][0]
    except Exception as e:
        print(f"   ⚠️ 提取年份时出错: {e}")
        year = None

    # 获取作者信息
    authors = []
    try:
        if "author" in crossref_data:
            for author in crossref_data["author"]:
                if "family" in author and "given" in author:
                    authors.append(f"{author['given']} {author['family']}")
                elif "family" in author:
                    authors.append(author["family"])
                elif "name" in author:
                    authors.append(author["name"])
    except Exception as e:
        print(f"   ⚠️ 提取作者信息时出错: {e}")

    return {
        "title": title,
        "journal": journal_name,
        "year": year,
        "citation_count": citation_count,
        "impact_factor": impact_factor,
        "doi": doi,
        "authors": authors
    }

# ---------- 视觉样式计算 ----------
def get_if_color(impact_factor):
    """根据影响因子返回颜色深浅"""
    if impact_factor is None or impact_factor == 0:
        return "#808080"  # 灰色 - 无数据
    elif impact_factor < 2:
        return "#E8F5E8"  # 浅绿色 - 低影响因子
    elif impact_factor < 5:
        return "#81C784"  # 中绿色 - 中等影响因子
    elif impact_factor < 10:
        return "#4CAF50"  # 绿色 - 高影响因子
    elif impact_factor < 20:
        return "#2E7D32"  # 深绿色 - 很高影响因子
    else:
        return "#1B5E20"  # 最深绿色 - 顶级影响因子

def get_citation_size(citation_count):
    """根据被引次数返回节点大小（对数缩放，便于排列）"""
    import math

    if citation_count is None or citation_count <= 0:
        return 12  # 最小尺寸

    # 使用对数缩放，避免线性增长导致的排列问题
    # 公式：size = base_size + log_scale * log(citation_count + 1)
    base_size = 12
    log_scale = 8

    size = base_size + log_scale * math.log(citation_count + 1)

    # 限制最大最小值，便于排列
    return max(12, min(30, int(size)))

def calculate_time_position(year, center_year):
    """根据发表年份计算时间轴位置"""
    if not year or not center_year:
        return 0

    # 每年对应50像素的距离
    pixels_per_year = 50
    return (year - center_year) * pixels_per_year

def get_node_type_color(node_type):
    """根据节点类型返回基础颜色"""
    colors = {
        "center": "#FF9800",    # 橙色 - 中心文献
        "reference": "#2196F3", # 蓝色 - 参考文献
        "citation": "#F44336"   # 红色 - 被引文献
    }
    return colors.get(node_type, "#757575")

def get_if_color_variant(impact_factor, node_type):
    """根据影响因子调整颜色深浅"""
    if not impact_factor or impact_factor <= 0:
        return get_node_type_color(node_type)

    # 根据影响因子调整颜色深浅
    if node_type == "reference":  # 蓝色系
        if impact_factor >= 10:
            return "#0D47A1"  # 深蓝
        elif impact_factor >= 5:
            return "#1565C0"  # 中深蓝
        elif impact_factor >= 3:
            return "#1976D2"  # 中蓝
        else:
            return "#42A5F5"  # 浅蓝
    elif node_type == "citation":  # 红色系
        if impact_factor >= 10:
            return "#B71C1C"  # 深红
        elif impact_factor >= 5:
            return "#C62828"  # 中深红
        elif impact_factor >= 3:
            return "#D32F2F"  # 中红
        else:
            return "#EF5350"  # 浅红
    else:
        return get_node_type_color(node_type)

def get_if_color_variant(impact_factor, base_color):
    """根据影响因子调整颜色深浅"""
    if not impact_factor or impact_factor <= 0:
        return base_color

    # 根据影响因子调整透明度和深浅
    if base_color == "#2196F3":  # 蓝色系
        if impact_factor >= 10:
            return "#0D47A1"  # 深蓝
        elif impact_factor >= 5:
            return "#1565C0"  # 中深蓝
        elif impact_factor >= 3:
            return "#1976D2"  # 中蓝
        else:
            return "#42A5F5"  # 浅蓝
    elif base_color == "#F44336":  # 红色系
        if impact_factor >= 10:
            return "#B71C1C"  # 深红
        elif impact_factor >= 5:
            return "#C62828"  # 中深红
        elif impact_factor >= 3:
            return "#D32F2F"  # 中红
        else:
            return "#EF5350"  # 浅红
    else:
        return base_color


# ---------- 引用关系 ----------
def get_references(doi):
    """返回参考文献 DOI 列表（最多 MAX_NODES 条）"""
    print(f"🔍 正在获取参考文献: {doi}")
    data = safe_get(f"https://api.crossref.org/works/{doi}")
    if not data:
        print("   ❌ 无法获取数据")
        return []

    message = data.get("message", {})
    references = message.get("reference", [])
    print(f"   📚 找到 {len(references)} 条参考文献记录")

    refs = []
    for ref in references:
        if "DOI" in ref:
            refs.append(ref["DOI"])
            if len(refs) >= MAX_NODES:
                break

    print(f"   ✅ 提取到 {len(refs)} 个有效DOI")
    return refs


def get_citedby_semantic_scholar(doi):
    """使用Semantic Scholar API获取被引文献"""
    print(f"   � 尝试Semantic Scholar API")

    # 通过DOI获取被引文献
    url = f"https://api.semanticscholar.org/graph/v1/paper/DOI:{doi}"
    params = {"fields": "paperId,citations,citations.title,citations.externalIds"}

    data = safe_get(url, params=params)
    if not data:
        return []

    citations = data.get("citations", [])
    print(f"   📖 Semantic Scholar找到 {len(citations)} 篇被引文献")

    cited_dois = []
    for citation in citations[:MAX_NODES]:
        external_ids = citation.get("externalIds", {})
        # 检查是否有DOI
        if external_ids and "DOI" in external_ids and external_ids["DOI"]:
            cited_dois.append(external_ids["DOI"])

    print(f"   ✅ 提取到 {len(cited_dois)} 个有效DOI")
    return cited_dois


def get_citedby_opencitations(doi):
    """使用OpenCitations API获取被引文献"""
    print(f"   📝 尝试OpenCitations API")

    url = f"https://opencitations.net/index/coci/api/v1/citations/{doi}"
    data = safe_get(url)
    if not data:
        return []

    print(f"   📖 OpenCitations找到 {len(data)} 篇被引文献")

    cited_dois = []
    for citation in data[:MAX_NODES]:
        citing_doi = citation.get("citing")
        if citing_doi:
            cited_dois.append(citing_doi)

    print(f"   ✅ 提取到 {len(cited_dois)} 个有效DOI")
    return cited_dois


def get_citedby(doi):
    """返回被引文献 DOI 列表（最多 MAX_NODES 条）"""
    print(f"🔍 正在获取被引文献: {doi}")

    # 尝试多种数据源获取被引文献
    methods = [
        # 方法1：Semantic Scholar API（推荐）
        get_citedby_semantic_scholar,
        # 方法2：OpenCitations API
        get_citedby_opencitations,
        # 方法3：Crossref API（备用）
        lambda doi: get_citedby_crossref(doi)
    ]

    for i, method in enumerate(methods, 1):
        try:
            cited_dois = method(doi)
            if cited_dois:
                return cited_dois
        except Exception as e:
            print(f"   ❌ 方法 {i} 失败：{str(e)[:50]}")

    print("   ⚠️ 所有方法都失败，返回空列表")
    return []


def get_citedby_crossref(doi):
    """使用Crossref API获取被引文献（备用方法）"""
    print(f"   📝 尝试Crossref API")

    # 尝试不同的过滤器语法
    filters = [
        f"references-to:{doi}",
        f"cites:{doi}",
        f"reference-to:{doi}"
    ]

    for filter_str in filters:
        url = "https://api.crossref.org/works"
        params = {"filter": filter_str, "rows": MAX_NODES, "select": "DOI,title"}
        data = safe_get(url, params=params)

        if data:
            items = data.get("message", {}).get("items", [])
            if items:
                print(f"   📖 Crossref找到 {len(items)} 篇被引文献")
                cited_dois = [item["DOI"] for item in items if "DOI" in item]
                print(f"   ✅ 提取到 {len(cited_dois)} 个有效DOI")
                return cited_dois

    return []


# ---------- 构建增强图谱 ----------
def build_enhanced_graph(center_doi):
    """构建包含被引次数和影响因子的增强图谱"""
    G = nx.DiGraph()
    print(f"🎯 构建以 {center_doi} 为中心的增强引用图谱")

    # 获取中心文献的增强元数据
    print("📊 获取中心文献元数据...")
    center_meta = get_enhanced_metadata(center_doi)
    if not center_meta:
        raise ValueError("无法获取中心文献元数据")

    # 添加中心节点
    center_size = get_citation_size(center_meta["citation_count"]) + 10  # 中心节点稍大
    center_color = get_node_type_color("center")

    # 创建中心文献的简洁标签：作者 + 年份
    center_authors = center_meta.get("authors", [])
    if center_authors and len(center_authors) > 0 and center_authors[0]:
        try:
            # 取第一作者姓氏
            author_parts = center_authors[0].strip().split()
            if author_parts:
                first_author = author_parts[-1]
                if len(center_authors) > 1:
                    center_label = f"{first_author} et al."
                else:
                    center_label = first_author
            else:
                center_label = "Unknown"
        except:
            center_label = "Unknown"
    else:
        center_label = "Unknown"

    # 添加年份
    if center_meta["year"]:
        center_label += f" ({center_meta['year']})"

    G.add_node(
        center_doi,
        label=center_label,
        color=center_color,
        size=center_size,
        node_type="center",
        citation_count=center_meta["citation_count"],
        impact_factor=center_meta["impact_factor"],
        journal=center_meta["journal"],
        year=center_meta["year"],
        full_title=center_meta["title"]
    )

    print(f"   🎯 中心文献: {center_meta['title'][:50]}...")
    print(f"   📊 被引次数: {center_meta['citation_count']}, 影响因子: {center_meta['impact_factor']:.2f}")

    # 获取参考文献
    print(f"📥 开始抓取参考文献（最多 {MAX_NODES} 条）...")
    ref_dois = get_references(center_doi)
    for i, ref_doi in enumerate(ref_dois, 1):
        print(f"  [{i}/{len(ref_dois)}] {ref_doi}")

        ref_meta = get_enhanced_metadata(ref_doi)
        if ref_meta:
            # 计算节点样式
            node_size = get_citation_size(ref_meta["citation_count"])
            node_color = get_if_color_variant(ref_meta["impact_factor"], "reference")

            # 创建简洁的显示标签：作者 + 年份
            authors = ref_meta.get("authors", [])
            if authors and len(authors) > 0 and authors[0]:
                try:
                    # 取第一作者姓氏
                    author_parts = authors[0].strip().split()
                    if author_parts:
                        first_author = author_parts[-1]
                        if len(authors) > 1:
                            display_label = f"{first_author} et al."
                        else:
                            display_label = first_author
                    else:
                        display_label = "Unknown"
                except:
                    display_label = "Unknown"
            else:
                display_label = "Unknown"

            # 添加年份
            if ref_meta["year"]:
                display_label += f" ({ref_meta['year']})"

            G.add_node(
                ref_doi,
                label=display_label,
                color=node_color,
                size=node_size,
                node_type="reference",
                citation_count=ref_meta["citation_count"],
                impact_factor=ref_meta["impact_factor"],
                journal=ref_meta["journal"],
                year=ref_meta["year"],
                full_title=ref_meta["title"]
            )
            G.add_edge(ref_doi, center_doi)

            print(f"     📊 被引: {ref_meta['citation_count']}, IF: {ref_meta['impact_factor']:.2f}")

        time.sleep(0.3)  # 减少延迟

    # 获取被引文献
    print(f"📤 开始抓取被引文献（最多 {MAX_NODES} 条）...")
    cit_dois = get_citedby(center_doi)
    for i, cit_doi in enumerate(cit_dois, 1):
        print(f"  [{i}/{len(cit_dois)}] {cit_doi}")

        cit_meta = get_enhanced_metadata(cit_doi)
        if cit_meta:
            # 计算节点样式
            node_size = get_citation_size(cit_meta["citation_count"])
            node_color = get_if_color_variant(cit_meta["impact_factor"], "citation")

            # 创建简洁的显示标签：作者 + 年份
            authors = cit_meta.get("authors", [])
            if authors and len(authors) > 0 and authors[0]:
                try:
                    # 取第一作者姓氏
                    author_parts = authors[0].strip().split()
                    if author_parts:
                        first_author = author_parts[-1]
                        if len(authors) > 1:
                            display_label = f"{first_author} et al."
                        else:
                            display_label = first_author
                    else:
                        display_label = "Unknown"
                except:
                    display_label = "Unknown"
            else:
                display_label = "Unknown"

            # 添加年份
            if cit_meta["year"]:
                display_label += f" ({cit_meta['year']})"

            G.add_node(
                cit_doi,
                label=display_label,
                color=node_color,
                size=node_size,
                node_type="citation",
                citation_count=cit_meta["citation_count"],
                impact_factor=cit_meta["impact_factor"],
                journal=cit_meta["journal"],
                year=cit_meta["year"],
                full_title=cit_meta["title"]
            )
            G.add_edge(center_doi, cit_doi)

            print(f"     📊 被引: {cit_meta['citation_count']}, IF: {cit_meta['impact_factor']:.2f}")

        time.sleep(0.3)

    return G


# ---------- 可视化 ----------
def visualize(G, filename="citation_graph.html"):
    # 检查图谱是否有节点
    if len(G.nodes()) == 0:
        print("⚠️ 图谱中没有节点，无法生成可视化")
        return

    print(f"📊 图谱统计：{len(G.nodes())} 个节点，{len(G.edges())} 条边")

    try:
        # 创建pyvis网络，使用更兼容的参数
        net = Network(
            height="750px",
            width="100%",
            bgcolor="#222222",
            font_color="white",
            directed=True,
            notebook=False,  # 确保不是notebook模式
            cdn_resources='remote'  # 使用远程CDN资源
        )

        print("   🔧 正在添加节点...")

        # 预先分类和排序节点，基于时间轴布局
        all_nodes = []
        center_year = None

        # 找到中心文献的年份
        for node_id, node_data in G.nodes(data=True):
            if node_data.get('node_type') == 'center':
                center_year = node_data.get('year', 2020)
                break

        # 收集所有节点并按年份排序
        for node_id, node_data in G.nodes(data=True):
            all_nodes.append((node_id, node_data))

        # 按年份排序，用于垂直分布时避免重叠
        all_nodes.sort(key=lambda x: (x[1].get('year', 2020), x[1].get('citation_count', 0)), reverse=True)

        # 手动添加节点，包含增强信息和防重叠位置设置
        for node_id, node_data in G.nodes(data=True):
            label = node_data.get('label', str(node_id))
            node_type = node_data.get('node_type', 'unknown')

            # 基于发表年份的时间轴位置计算
            year = node_data.get('year', center_year)

            if node_type == 'center':
                # 中心文献放在屏幕中央
                x_pos = 0
                y_pos = 0
            else:
                # 根据发表年份计算水平位置（时间轴）
                x_pos = calculate_time_position(year, center_year)

                # 改进的空间分布算法
                citation_count = node_data.get('citation_count', 0)

                # 根据节点类型和影响力进行分层布局
                if node_type == 'reference':
                    # 参考文献：根据被引次数分层
                    if citation_count > 500:
                        y_base = -150  # 高影响力文献在上层
                    elif citation_count > 100:
                        y_base = -50   # 中等影响力在中层
                    else:
                        y_base = 50    # 低影响力在下层
                elif node_type == 'citation':
                    # 被引文献：根据发表年份分层
                    if year and year >= 2024:
                        y_base = -100  # 最新文献在上层
                    else:
                        y_base = 100   # 较早文献在下层
                else:
                    y_base = 0

                # 在每一层内部进行微调，避免完全重叠
                same_layer_nodes = []
                for nid, ndata in all_nodes:
                    if ndata.get('node_type') == node_type and nid != node_id:
                        other_year = ndata.get('year', center_year)
                        other_citation = ndata.get('citation_count', 0)
                        other_x = calculate_time_position(other_year, center_year)

                        # 检查是否在相似的位置
                        if abs(other_x - x_pos) < 100:  # 水平距离较近
                            if node_type == 'reference':
                                if (citation_count > 500 and other_citation > 500) or \
                                   (100 < citation_count <= 500 and 100 < other_citation <= 500) or \
                                   (citation_count <= 100 and other_citation <= 100):
                                    same_layer_nodes.append((nid, ndata))
                            elif node_type == 'citation':
                                if (year and other_year and year >= 2024 and other_year >= 2024) or \
                                   (year and other_year and year < 2024 and other_year < 2024):
                                    same_layer_nodes.append((nid, ndata))

                # 在同一层内进行微调
                if same_layer_nodes:
                    try:
                        index = len([n for n in same_layer_nodes if n[0] < node_id])  # 简单排序
                        offset = (index - len(same_layer_nodes)/2) * 40  # 每个节点间隔40像素
                        y_pos = y_base + offset
                    except:
                        y_pos = y_base
                else:
                    y_pos = y_base

                # 限制垂直范围
                y_pos = max(-250, min(250, y_pos))

            # 创建详细的悬停信息
            impact_factor = node_data.get('impact_factor')
            if_display = f"{impact_factor:.2f}" if impact_factor else "N/A"

            hover_info = f"""
📄 {node_data.get('full_title', label)}
📊 被引次数: {node_data.get('citation_count', 'N/A')}
📈 影响因子: {if_display}
📖 期刊: {node_data.get('journal', 'N/A')}
📅 年份: {node_data.get('year', 'N/A')}
🔗 DOI: {node_id}
            """.strip()

            net.add_node(
                node_id,
                label=label,
                color=node_data.get('color', 'gray'),
                size=node_data.get('size', 15),
                title=hover_info,  # 鼠标悬停显示详细信息
                font={'color': 'white', 'size': 10},
                borderWidth=2,
                borderWidthSelected=4,
                x=x_pos,  # 设置X坐标
                y=y_pos,  # 设置Y坐标
                fixed={'x': True, 'y': True}  # 固定位置
            )

        print("   🔗 正在添加边...")
        # 添加边
        for source, target in G.edges():
            net.add_edge(source, target, color='#666666', width=2)

        print("   ⚙️ 正在设置时间轴布局...")
        # 设置时间轴布局选项
        net.set_options("""
        var options = {
          "physics": {
            "enabled": false
          },
          "layout": {
            "hierarchical": {
              "enabled": false
            }
          },
          "interaction": {
            "dragNodes": true,
            "dragView": true,
            "zoomView": true
          },
          "edges": {
            "smooth": {
              "enabled": true,
              "type": "continuous",
              "roundness": 0.5
            },
            "arrows": {
              "to": {
                "enabled": true,
                "scaleFactor": 1.2
              }
            }
          },
          "nodes": {
            "font": {
              "color": "white",
              "size": 12
            }
          }
        }
        """)

        # 添加时间轴标记
        print("   📅 添加时间轴标记...")
        years = set()
        center_year = None

        # 收集所有年份和中心年份
        for node_id, node_data in G.nodes(data=True):
            year = node_data.get('year')
            if year:
                years.add(year)
            if node_data.get('node_type') == 'center':
                center_year = year

        # 为每个年份添加时间轴标记
        for year in sorted(years):
            if year != center_year and center_year:  # 不为中心年份添加标记，避免重叠
                x_pos = calculate_time_position(year, center_year)
                net.add_node(
                    f"timeline_{year}",
                    label=str(year),
                    x=x_pos,
                    y=350,  # 放在底部
                    size=8,
                    color="#666666",
                    font={'color': '#666666', 'size': 10},
                    fixed={'x': True, 'y': True},
                    physics=False,
                    shape="box"
                )

        print("   💾 正在保存文件...")
        # 确保文件路径正确
        import os
        full_path = os.path.abspath(filename)

        # 尝试保存文件
        net.save_graph(full_path)

        # 验证文件是否成功创建
        if os.path.exists(full_path) and os.path.getsize(full_path) > 0:
            print(f"\n✅ 交互式图谱已生成：{full_path}")
            print(f"   请用浏览器打开查看\n")
        else:
            raise Exception("文件保存失败或文件为空")

    except Exception as e:
        print(f"❌ pyvis可视化失败：{e}")
        print("   🔄 尝试备用方案...")

        # 备用方案1：使用show方法
        try:
            print("   📝 尝试使用show方法...")
            net = Network(height="750px", width="100%", bgcolor="#222222", font_color="white")

            # 重新添加节点和边
            for node_id, node_data in G.nodes(data=True):
                label = node_data.get('label', str(node_id))[:80]
                net.add_node(node_id, label=label, color=node_data.get('color', 'gray'))

            for source, target in G.edges():
                net.add_edge(source, target)

            net.show(filename)
            print(f"✅ 使用show方法成功生成：{os.path.abspath(filename)}")
            return

        except Exception as e2:
            print(f"   ❌ show方法也失败：{e2}")

        # 备用方案2：生成简单的HTML文件
        try:
            print("   📄 生成简化HTML版本...")
            html_content = f"""
<!DOCTYPE html>
<html>
<head>
    <title>引用图谱</title>
    <meta charset="utf-8">
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }}
        .container {{ max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }}
        h1 {{ color: #333; text-align: center; }}
        .stats {{ background: #e8f4fd; padding: 15px; border-radius: 5px; margin: 20px 0; }}
        .node-list {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 15px; }}
        .node {{ padding: 20px; border-radius: 12px; border-left: 5px solid; box-shadow: 0 2px 8px rgba(0,0,0,0.1); }}
        .center {{ background: linear-gradient(135deg, #fff3e0, #ffe0b2); border-left-color: #ff9800; }}
        .reference {{ background: linear-gradient(135deg, #e3f2fd, #bbdefb); border-left-color: #2196f3; }}
        .citation {{ background: linear-gradient(135deg, #ffebee, #ffcdd2); border-left-color: #f44336; }}
        .node-title {{ font-weight: bold; margin-bottom: 12px; font-size: 1.1em; line-height: 1.4; }}
        .node-type {{ font-size: 0.9em; color: #666; margin-bottom: 10px; font-weight: 500; }}
        .node-stats {{ display: flex; gap: 15px; margin: 10px 0; }}
        .stat {{ background: rgba(0,0,0,0.05); padding: 4px 8px; border-radius: 4px; font-size: 0.85em; font-weight: 500; }}
        .node-meta {{ font-size: 0.8em; color: #777; margin-top: 8px; }}
        .node-meta div {{ margin: 2px 0; }}
    </style>
</head>
<body>
    <div class="container">
        <h1>📊 文献引用图谱</h1>
        <div class="stats">
            <strong>图谱统计：</strong>{len(G.nodes())} 个节点，{len(G.edges())} 条边
        </div>
        <div class="node-list">
"""

            # 按类型分组显示节点，包含增强信息
            for node_id, node_data in G.nodes(data=True):
                title = node_data.get('full_title', node_data.get('label', str(node_id)))
                node_type_key = node_data.get('node_type', 'unknown')
                citation_count = node_data.get('citation_count', 0)
                impact_factor = node_data.get('impact_factor', 0)
                journal = node_data.get('journal', 'N/A')
                year = node_data.get('year', 'N/A')

                if node_type_key == 'center':
                    node_class = 'center'
                    node_type = '🎯 中心文献'
                elif node_type_key == 'reference':
                    node_class = 'reference'
                    node_type = '📚 参考文献'
                elif node_type_key == 'citation':
                    node_class = 'citation'
                    node_type = '📖 被引文献'
                else:
                    node_class = 'node'
                    node_type = '📄 文献'

                # 限制标题长度用于显示
                display_title = title[:100] + "..." if len(title) > 100 else title

                # 格式化影响因子显示
                if_display = f"{impact_factor:.2f}" if impact_factor else "N/A"

                html_content += f"""
            <div class="node {node_class}">
                <div class="node-title">{display_title}</div>
                <div class="node-type">{node_type}</div>
                <div class="node-stats">
                    <span class="stat">📊 被引: {citation_count}</span>
                    <span class="stat">📈 IF: {if_display}</span>
                </div>
                <div class="node-meta">
                    <div>📖 {journal}</div>
                    <div>📅 {year}</div>
                </div>
            </div>
"""

            html_content += """
        </div>
    </div>
</body>
</html>
"""
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(html_content)
            print(f"✅ 美化版图谱已生成：{os.path.abspath(filename)}")

        except Exception as e3:
            print(f"❌ 所有方案都失败：{e3}")


def generate_sidebar_html(G, filename="citation_network_with_sidebar.html"):
    """生成带侧边栏的HTML可视化文件"""
    import json
    import os

    # 检查图谱是否有节点
    if len(G.nodes()) == 0:
        print("⚠️ 图谱中没有节点，无法生成可视化")
        return

    print(f"📊 生成带侧边栏的图谱：{len(G.nodes())} 个节点，{len(G.edges())} 条边")

    # 准备网络数据
    nodes_data = []
    edges_data = []

    # 处理节点数据
    for node_id, node_data in G.nodes(data=True):
        node_info = {
            'id': node_id,
            'label': node_data.get('label', str(node_id)),
            'color': node_data.get('color', 'gray'),
            'size': node_data.get('size', 15),
            'node_type': node_data.get('node_type', 'unknown'),
            'citation_count': node_data.get('citation_count', 0),
            'impact_factor': node_data.get('impact_factor', 0),
            'journal': node_data.get('journal', ''),
            'year': node_data.get('year', ''),
            'full_title': node_data.get('full_title', node_data.get('label', '')),
            'authors': node_data.get('authors', [])
        }
        nodes_data.append(node_info)

    # 处理边数据
    for source, target in G.edges():
        edge_info = {
            'from': source,
            'to': target
        }
        edges_data.append(edge_info)

    # 创建网络数据JSON
    network_data = {
        'nodes': nodes_data,
        'edges': edges_data
    }

    # 读取HTML模板
    template_path = "citation_network_with_sidebar.html"
    if not os.path.exists(template_path):
        print(f"❌ 模板文件不存在：{template_path}")
        return

    try:
        with open(template_path, 'r', encoding='utf-8') as f:
            html_content = f.read()

        # 在HTML中插入数据
        data_script = f"""
        <script>
            // 网络数据
            const networkData = {json.dumps(network_data, ensure_ascii=False, indent=2)};

            // 页面加载完成后加载数据
            document.addEventListener('DOMContentLoaded', function() {{
                initNetwork();
                loadNetworkData(networkData);
            }});
        </script>
        """

        # 在</body>标签前插入数据脚本
        html_content = html_content.replace('</body>', data_script + '\n</body>')

        # 保存文件
        full_path = os.path.abspath(filename)
        with open(full_path, 'w', encoding='utf-8') as f:
            f.write(html_content)

        print(f"✅ 带侧边栏的图谱已生成：{full_path}")
        print(f"   请用浏览器打开查看\n")

    except Exception as e:
        print(f"❌ 生成带侧边栏HTML失败：{e}")


# ---------- 主流程 ----------
if __name__ == "__main__":
    try:
        user_in = input("请输入 DOI / PMID / 标题：")
        doi = parse_input(user_in)
        print("解析到 DOI →", doi)
        G = build_enhanced_graph(doi)

        # 生成传统的pyvis图谱
        print("\n🎨 生成传统图谱...")
        visualize(G, "citation_graph_traditional.html")

        # 生成带侧边栏的图谱
        print("\n🎨 生成带侧边栏的图谱...")
        generate_sidebar_html(G, "citation_graph_with_sidebar.html")

    except Exception as e:
        print("❌ 错误：", e)