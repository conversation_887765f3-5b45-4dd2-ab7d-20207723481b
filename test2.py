#!/usr/bin/env python3
"""
输入 PubMed PMID → 返回：
1. 在 PMC 中被引次数
2. 引用它的文献列表（PMID + 标题）
"""
import requests

PMC_ELINK = "https://eutils.ncbi.nlm.nih.gov/entrez/eutils/elink.fcgi"
PMC_ESUMMARY = "https://eutils.ncbi.nlm.nih.gov/entrez/eutils/esummary.fcgi"


def get_pmc_citations(pmid: str):
    """返回被引次数 + 引用文献列表"""
    params = {
        "dbfrom": "pubmed",
        "linkname": "pmc_refs_pubmed",
        "id": pmid,
        "retmode": "json"
    }
    r = requests.get(PMC_ELINK, params=params, timeout=10)
    r.raise_for_status()
    data = r.json()

    citing_pmids = []
    for ls in data.get("linksets", []):
        for ldb in ls.get("linksetdbs", []):
            if ldb.get("linkname") == "pmc_refs_pubmed":
                citing_pmids.extend(ldb.get("links", []))

    # 去重
    citing_pmids = list(set(citing_pmids))
    return citing_pmids


def fetch_titles(pmids):
    """批量获取标题"""
    if not pmids:
        return {}
    params = {"db": "pubmed", "id": ",".join(pmids), "retmode": "json"}
    r = requests.get(PMC_ESUMMARY, params=params, timeout=10)
    r.raise_for_status()
    data = r.json()
    return {pmid: info["title"] for pmid, info in data["result"].items() if pmid != "uids"}


if __name__ == "__main__":
    pmid = input("请输入 PubMed PMID：").strip()
    citing_pmids = get_pmc_citations(pmid)
    titles = fetch_titles(citing_pmids)

    print(f"\n✅ 在 PMC 中共有 {len(citing_pmids)} 篇文献引用了 PMID {pmid}：")
    for cp in citing_pmids:
        print(f"{cp:<10} {titles.get(cp, 'N/A')}")