<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>URL修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-link {
            display: inline-block;
            margin: 5px;
            padding: 10px 15px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 3px;
        }
        .test-link:hover {
            background: #0056b3;
        }
        .status {
            margin-top: 10px;
            padding: 10px;
            border-radius: 3px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <h1>NNScholar URL修复测试</h1>
    
    <div class="test-section">
        <h2>修复前的问题</h2>
        <p>之前深度分析工具箱中的功能卡片使用了硬编码的 <code>http://localhost:5000</code> URL，导致部署到网站时跳转失败。</p>
    </div>
    
    <div class="test-section">
        <h2>修复后的URL（相对路径）</h2>
        <p>现在所有链接都使用相对路径，可以在任何域名下正常工作：</p>
        
        <a href="/analysis/journal-selection" class="test-link" target="_blank">AI投稿选刊</a>
        <a href="/analysis/paper-translation" class="test-link" target="_blank">论文翻译</a>
        <a href="/analysis/paper-polish" class="test-link" target="_blank">论文润色</a>
        <a href="/analysis/cover-letter" class="test-link" target="_blank">Cover Letter生成</a>
        <a href="/analysis/statistical-analysis" class="test-link" target="_blank">统计分析专家</a>
        <a href="/analysis/visualization-expert" class="test-link" target="_blank">绘图分析专家</a>
        <a href="/analysis/reference-matching" class="test-link" target="_blank">引文真实性验证</a>
    </div>
    
    <div class="test-section">
        <h2>测试说明</h2>
        <ul>
            <li>✅ 所有链接现在使用相对路径 <code>/analysis/xxx</code></li>
            <li>✅ 无论部署在 localhost、域名还是子目录都能正常工作</li>
            <li>✅ 不再依赖硬编码的端口号</li>
            <li>✅ 支持HTTPS和HTTP协议</li>
        </ul>
    </div>
    
    <div class="test-section">
        <h2>部署兼容性</h2>
        <div class="status success">
            <strong>✅ 修复完成</strong><br>
            现在可以安全地部署到任何环境：
            <ul>
                <li>本地开发：http://localhost:5000</li>
                <li>生产环境：https://yourdomain.com</li>
                <li>子目录部署：https://yourdomain.com/nnscholar</li>
            </ul>
        </div>
    </div>
    
    <script>
        // 测试JavaScript中的URL处理
        function testUrlHandling() {
            console.log('当前页面URL:', window.location.href);
            console.log('基础URL:', window.location.origin);
            console.log('相对路径测试:', new URL('/analysis/paper-translation', window.location.origin).href);
        }
        
        // 页面加载时运行测试
        document.addEventListener('DOMContentLoaded', testUrlHandling);
        
        // 添加点击测试
        document.querySelectorAll('.test-link').forEach(link => {
            link.addEventListener('click', function(e) {
                console.log('点击链接:', this.href);
            });
        });
    </script>
</body>
</html>
