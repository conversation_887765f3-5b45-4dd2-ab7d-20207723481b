"""Utility functions for NNScholar application."""

# Initialize NLTK data on import
try:
    from .nltk_setup import download_nltk_data
    download_nltk_data()
except Exception:
    pass

from .text_processing import *
from .file_utils import *
from .cache_utils import *

__all__ = [
    # Text processing
    'preprocess_text', 'split_paragraph_to_sentences', 'create_text_features',
    # File utils
    'ensure_directory', 'cleanup_temp_files',
    # Cache utils
    'get_cache_key', 'cache_result'
]