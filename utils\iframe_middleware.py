"""Middleware to handle iframe embedding configuration."""

import os
from flask import Flask, Response
from typing import Optional

def configure_iframe_headers(app: Flask):
    """Configure application to allow iframe embedding based on environment settings."""
    
    @app.after_request
    def set_iframe_headers(response: Response) -> Response:
        """Set appropriate headers for iframe embedding."""
        
        # Get iframe mode from environment
        iframe_mode = os.getenv('IFRAME_MODE', 'DENY').upper()
        
        if iframe_mode == 'ALLOW':
            # Remove restrictive headers to allow iframe embedding
            response.headers.pop('X-Frame-Options', None)
            response.headers.pop('Content-Security-Policy', None)
            
            # Add permissive CORS headers
            response.headers['Access-Control-Allow-Origin'] = '*'
            response.headers['Access-Control-Allow-Methods'] = 'GET, POST, OPTIONS'
            response.headers['Access-Control-Allow-Headers'] = 'Content-Type'
            
        elif iframe_mode == 'SAMEORIGIN':
            response.headers['X-Frame-Options'] = 'SAMEORIGIN'
            
        elif iframe_mode == 'DENY':
            response.headers['X-Frame-Options'] = 'DENY'
            
        else:
            # Custom domain mode - use CSP
            allowed_domains = os.getenv('IFRAME_ALLOWED_DOMAINS', '')
            if allowed_domains:
                csp_value = f"frame-ancestors 'self' {allowed_domains}"
                response.headers['Content-Security-Policy'] = csp_value
                response.headers.pop('X-Frame-Options', None)
        
        return response
    
    return app