"""NLTK data initialization module."""
import nltk
import os
import logging

logger = logging.getLogger(__name__)

def download_nltk_data():
    """Download required NLTK data packages."""
    try:
        # Set NLTK data path
        nltk_data_dir = os.path.join(os.path.expanduser('~'), 'nltk_data')
        os.makedirs(nltk_data_dir, exist_ok=True)
        
        # Download required packages
        packages = ['punkt', 'stopwords', 'wordnet']
        for package in packages:
            try:
                nltk.data.find(f'tokenizers/{package}')
                logger.info(f"NLTK package '{package}' already downloaded")
            except LookupError:
                logger.info(f"Downloading NLTK package '{package}'...")
                nltk.download(package, quiet=True)
                
        return True
    except Exception as e:
        logger.warning(f"Failed to download NLTK data: {e}")
        return False

# Run setup when module is imported
download_nltk_data()